name: Deploy development

on:
  push:
    branches:
      - development
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy via SSH
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Copy files to webuild-dev
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV }}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          source: .
          target: /home/<USER>/webild-backend

      - name: Trigger deploy on webuild-dev via SSH
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV }}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          script_stop: true
          command_timeout: 30m
          script: |
            set -euo pipefail

            PROJECT_PATH="${EC2_PROJECT_PATH:-/home/<USER>/webild-backend}"

            if [ ! -d "$PROJECT_PATH" ]; then
              echo "Project path $PROJECT_PATH does not exist. Please create it and set up the repo."
              exit 1
            fi

            cd "$PROJECT_PATH"

            # Run deployment using Makefile
            make start-server-dev



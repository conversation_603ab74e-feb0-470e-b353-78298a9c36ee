NODE_ENV=development
PROJECT_NAME=webild-backend
API_PORT=3000

MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=app
MYSQL_ROOT_PASSWORD=root
MYSQL_USER=user
MYSQL_PASSWORD=user

REDIS_HOST=localhost
REDIS_PORT=6379

CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_USER_JWT_FOR_SWAGGER=

OPENAI_API_KEY=
OPENAI_API_CODE_GENERATE=gpt-4o-mini
OPENAI_API_PROMPT_GENERATE=gpt-5-nano

GITHUB_TOKEN=

VERCEL_API_TOKEN=
VERCEL_TEAM_ID=
VERCEL_TEAM_SLUG=

DAYTONA_API_KEY=
DAYTONA_API_URL=

WHISPER_ENDPOINT=http://localhost:8000/whisper/transcribe

BULL_BOARD_PASSWORD=admin

STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

STRIPE_PRICE_PRO=price_***
STRIPE_PRICE_PREMIUM=
STRIPE_PRICE_ULTRA=

STRIPE_SUCCESS_URL=
STRIPE_CANCEL_URL=

PLAN_FREE_REQUESTS=
PLAN_FREE_SITES=
PLAN_PRO_REQUESTS=
PLAN_PRO_SITES=
PLAN_PREMIUM_REQUESTS=
PLAN_PREMIUM_SITES=
PLAN_ULTRA_REQUESTS=
PLAN_ULTRA_SITES=

AWS_REGION=eu-north-1
AWS_S3_BUCKET=webuild-dev
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_PUBLIC_BASE_URL=https://webuild-dev.s3.eu-north-1.amazonaws.com

DNSIMPLE_CLIENT_ID=
DNSIMPLE_CLIENT_SECRET=
DNSIMPLE_REDIRECT_URI=http://localhost:8000/dnsimple/oauth/callback
DNSIMPLE_ENABLE_PROD=false


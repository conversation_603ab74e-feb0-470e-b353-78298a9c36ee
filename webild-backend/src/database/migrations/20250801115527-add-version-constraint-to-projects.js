'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('projects', {
      fields: ['active_version_id'],
      type: 'foreign key',
      name: 'fk_projects_active_version_id',
      references: {
        table: 'project_versions',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeConstraint('projects', 'fk_projects_active_version_id');
  },
};

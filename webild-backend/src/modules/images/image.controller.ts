import { Body, Controller, Delete, Get, Post } from '@nestjs/common';
import { <PERSON>pi<PERSON>earerAuth } from '@nestjs/swagger';

import { DeleteImagesDto, DeleteImagesDtoResponse } from './dto/delete-image.dto';
import { GenerateImageDto, GenerateImageDtoResponse } from './dto/generate-image.dto';
import { GetAllImagesDtoResponse } from './dto/get-all-images.dto';
import { ImageService } from './image.service';
import {
  SwaggerDeleteImage,
  SwaggerGenerateImage,
  SwaggerGetAllImages,
  SwaggerSaveImage,
} from './image.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { User } from '../users/users.entity';
import { SaveImageDto, SaveImageDtoResponse } from './dto/save-image.dto';

@ApiBearerAuth('clerk-auth')
@Controller('images')
export class ImageController {
  constructor(private readonly imageService: ImageService) {}

  @SwaggerGenerateImage()
  @Post('generate')
  async generate(
    @Body() dto: GenerateImageDto,
    @DbAuthUser() user: User,
  ): Promise<GenerateImageDtoResponse> {
    return await this.imageService.fetchAndUploadImage(dto.prompt, user.clerkId);
  }

  @SwaggerGetAllImages()
  @Get()
  async getAllImages(@DbAuthUser() user: User): Promise<GetAllImagesDtoResponse[]> {
    return this.imageService.getAllUsersImages(user.clerkId);
  }

  @SwaggerSaveImage()
  @Post('save')
  async saveImage(
    @Body() dto: SaveImageDto,
    @DbAuthUser() user: User,
  ): Promise<SaveImageDtoResponse> {
    return this.imageService.saveImage(user.clerkId, dto);
  }

  @SwaggerDeleteImage()
  @Delete()
  async deleteImages(
    @Body() dto: DeleteImagesDto,
    @DbAuthUser() user: User,
  ): Promise<DeleteImagesDtoResponse> {
    return this.imageService.deleteImages(user.clerkId, dto);
  }
}

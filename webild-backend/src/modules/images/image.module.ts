import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { ImageController } from './image.controller';
import { ImageService } from './image.service';
import { AwsS3Service } from '../../common/services/aws/aws-s3.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 15000,
      maxRedirects: 0,
    }),
    ConfigModule.forRoot({ isGlobal: true }),
  ],
  controllers: [ImageController],
  providers: [ImageService, AwsS3Service],
  exports: [ImageService],
})
export class ImageModule {}

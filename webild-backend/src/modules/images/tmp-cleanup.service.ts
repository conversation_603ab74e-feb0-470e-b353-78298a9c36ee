import { S3Client, ListObjectsV2Command, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class TmpCleanupService {
  private readonly logger = new Logger(TmpCleanupService.name);
  private readonly bucket = process.env.AWS_S3_BUCKET!;
  private readonly region = process.env.AWS_REGION ?? 'us-east-1';
  private readonly days = Number(process.env.TMP_TTL_DAYS ?? 7);
  private readonly s3 = new S3Client({ region: this.region });

  @Cron(CronExpression.EVERY_DAY_AT_3AM, { timeZone: 'UTC' })
  async cleanupTmp(): Promise<void> {
    if (!this.bucket) {
      this.logger.warn('AWS_S3_BUCKET is not set; skipping tmp cleanup');
      return;
    }

    const cutoff = Date.now() - this.days * 24 * 60 * 60 * 1000;
    this.logger.log(`Cleaning tmp images older than ${this.days} days…`);

    let token: string | undefined;
    let deleted = 0;

    do {
      const res = await this.s3.send(
        new ListObjectsV2Command({
          Bucket: this.bucket,
          Prefix: 'users/',
          ContinuationToken: token,
        }),
      );

      const toDelete = (res.Contents ?? [])
        .filter((o) => o.Key && o.Key.includes('/tmp/'))
        .filter((o) => o.LastModified && o.LastModified.getTime() < cutoff)
        .map((o) => o.Key!);

      const batchSize = 20;
      for (let i = 0; i < toDelete.length; i += batchSize) {
        const slice = toDelete.slice(i, i + batchSize);
        const results = await Promise.allSettled(
          slice.map((Key) => this.s3.send(new DeleteObjectCommand({ Bucket: this.bucket, Key }))),
        );
        const ok = results.filter((r) => r.status === 'fulfilled').length;
        const fail = results.length - ok;
        deleted += ok;
        if (fail > 0) this.logger.warn(`Failed to delete ${fail} objects in this batch`);
      }

      token = res.IsTruncated ? res.NextContinuationToken : undefined;
    } while (token);

    this.logger.log(`Cleanup complete. Deleted ${deleted} stale tmp files.`);
  }
}

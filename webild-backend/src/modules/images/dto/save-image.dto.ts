import { ApiProperty } from '@nestjs/swagger';
import { IsString, ValidateIf } from 'class-validator';

export class SaveImageDto {
  @ApiProperty()
  @ValidateIf((o: SaveImageDto) => !o.url)
  @IsString()
  declare key?: string;

  @ApiProperty()
  @ValidateIf((o: SaveImageDto) => !o.key)
  @IsString()
  declare url?: string;
}

export class SaveImageDtoResponse {
  @ApiProperty()
  declare key: string;

  @ApiProperty()
  declare url: string;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, ValidateIf, IsArray, IsNotEmpty } from 'class-validator';

export class DeleteImagesDto {
  @ApiPropertyOptional({
    description: 'Array of S3 object keys of the images to delete',
    type: [String],
  })
  @ValidateIf((o: DeleteImagesDto) => !o.urls || o.urls.length === 0)
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  keys?: string[];

  @ApiPropertyOptional({
    description: 'Array of public URLs of the images to delete',
    type: [String],
  })
  @ValidateIf((o: DeleteImagesDto) => !o.keys || o.keys.length === 0)
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  urls?: string[];
}

export class DeleteImagesDtoResponse {
  @ApiProperty({
    description: 'Array of deletion results',
    type: () => [{ key: String, deleted: Boolean }],
  })
  results!: { key: string; deleted: boolean }[];
}

import { applyDecorators } from '@nestjs/common';
import { ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

import { DeleteImagesDtoResponse } from './dto/delete-image.dto';
import { GenerateImageDtoResponse } from './dto/generate-image.dto';
import { SaveImageDtoResponse } from './dto/save-image.dto';
import { ResponseWrapperDto } from '../../common/dto/response-wrapper.dto';

export function SwaggerGenerateImage() {
  return applyDecorators(
    ApiOkResponse({
      description: 'Returns generated image URL from AWS.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(GenerateImageDtoResponse) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerGetAllImages() {
  return applyDecorators(
    ApiOkResponse({
      description: 'Returns public URLs of images from the common folder and the user folder.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { type: 'string', format: 'uri' },
              },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerSaveImage() {
  return applyDecorators(
    ApiOkResponse({
      description:
        'Moves an image from /tmp to the root of the user folder and returns its public URL.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(SaveImageDtoResponse) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerDeleteImage() {
  return applyDecorators(
    ApiOkResponse({
      description: 'Deletes an image from the user folder.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DeleteImagesDtoResponse) },
            },
          },
        ],
      },
    }),
  );
}

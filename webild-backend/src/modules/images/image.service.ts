import { HttpService } from '@nestjs/axios';
import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

import { GenerateImageDtoResponse } from './dto/generate-image.dto';
import { AwsS3Service } from '../../common/services/aws/aws-s3.service';

@Injectable()
export class ImageService {
  private readonly endpoint = 'https://ai-image-api.xeven.workers.dev/img';

  constructor(
    private readonly http: HttpService,
    private readonly s3: AwsS3Service,
  ) {}

  async fetchAndUploadImage(
    prompt: string,
    userClerkId: string,
    model = 'flux-schnell',
  ): Promise<GenerateImageDtoResponse> {
    const response = await firstValueFrom(
      this.http.get<ArrayBuffer>(this.endpoint, {
        params: { prompt, model },
        responseType: 'arraybuffer',
        validateStatus: () => true,
        headers: {
          Accept: 'image/*',
        },
      }),
    );

    if (response.status >= 400) {
      throw new HttpException(
        `Upstream error: ${response.status} ${response.statusText ?? ''}`.trim(),
        HttpStatus.BAD_GATEWAY,
      );
    }
    const contentType = (response.headers?.['content-type'] as string) || 'image/png';
    const data = Buffer.from(response.data);

    const filename = this.s3.makeTmpFilename(prompt, contentType);
    const key = `users/${userClerkId}/tmp/${filename}`;
    const { url } = await this.s3.uploadBufferToKey({
      key,
      contentType,
      body: data,
    });

    return { key, url, contentType };
  }

  async getAllUsersImages(userClerkId: string) {
    const [common, user] = await Promise.all([
      this.s3.listUrls('common/'),
      this.s3.listUrls(`users/${userClerkId}/`),
    ]);

    const filesOnly = [...common, ...user]
      .filter(Boolean)
      .map((url) => ({ url, key: this.s3.keyFromUrl(url) }))
      .filter(({ key }) => key && !key.endsWith('/'));

    return Array.from(new Set(filesOnly));
  }

  async saveImage(userId: string, input: { key?: string; url?: string }) {
    const sourceKey = input.key ?? this.s3.keyFromUrl(input.url!);
    if (!sourceKey.startsWith(`users/${userId}/tmp/`)) {
      throw new BadRequestException('Only tmp images for this user can be saved');
    }
    const destKey = sourceKey.replace('/tmp/', '/');
    await this.s3.moveObject(sourceKey, destKey);
    return { url: this.s3.publicUrlForKey(destKey), key: destKey };
  }

  async deleteImages(userId: string, input: { keys?: string[]; urls?: string[] }) {
    const keys = input.keys ?? input.urls!.map((url) => this.s3.keyFromUrl(url));

    for (const key of keys) {
      if (
        !key.startsWith(`users/${userId}/`) ||
        (/\/tmp\//.test(key) === false && !key.includes(`users/${userId}/`))
      ) {
        throw new BadRequestException(
          `Invalid key: ${key}. Can only delete images in the user folder`,
        );
      }
    }

    const deletePromises = keys.map(async (key) => {
      try {
        await this.s3.deleteKey(key);
        return { key, deleted: true };
      } catch (error) {
        console.error(`Failed to delete key ${key}:`, error);
        return { key, deleted: false };
      }
    });

    const results = await Promise.all(deletePromises);
    return { results };
  }
}

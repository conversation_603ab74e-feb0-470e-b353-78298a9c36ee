export interface DbUser {
  id: string;
}

export interface OAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
}

export interface WhoamiResponse {
  data: {
    account?: { id: number } | null;
    user?: { id: number } | null;
  };
}

export interface DnsimpleDomain {
  id: number;
  name: string;
  unicode_name?: string;
  state?: string;
  created_at?: string;
}

export interface DnsimplePagination {
  current_page?: number;
  per_page?: number;
  total_entries?: number;
  total_pages?: number;
}

export interface ListDomainsResponse {
  data: DnsimpleDomain[];
  pagination?: DnsimplePagination;
}

export interface DnsimpleDomainDetails {
  id: number;
  name: string;
  unicode_name?: string;
  state?: string;
  auto_renew?: boolean;
  private_whois?: boolean;
  expires_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface DomainAvailabilityResponse {
  domain: string;
  available: boolean;
  premium?: boolean;
}

export interface DomainPremiumPriceResponse {
  premium_price: string;
  action: string;
}

export interface DomainPricesResponse {
  domain: string;
  premium: boolean;
  registration_price: number;
  renewal_price: number;
  transfer_price: number;
  restore_price: number;
}

export interface DomainRegistrationRequest {
  registrant_id: number;
  whois_privacy?: boolean;
  auto_renew?: boolean;
  premium_price?: string;
  extended_attributes?: ExtendedAttributes;
}

export interface DomainRegistrationResponse {
  id: number;
  state: string;
  domain_id: number;
  created_at?: string;
}

export interface Contact {
  id: number;
  account_id: number;
  label: string;
  first_name: string;
  last_name: string;
  organization_name?: string;
  job_title?: string;
  address1: string;
  address2?: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  phone: string;
  fax?: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface ContactsResponse {
  data: Contact[];
  pagination?: DnsimplePagination;
}

export interface CreateContactRequest {
  label?: string;
  first_name: string;
  last_name: string;
  organization_name?: string;
  job_title?: string;
  address1: string;
  address2?: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  email: string;
  phone: string;
  fax?: string;
}

export interface CreateContactResponse {
  data: Contact;
}

export interface TldExtendedAttribute {
  name: string;
  description: string;
  required: boolean;
  options?: Array<{
    title: string;
    value: string;
    description?: string;
  }>;
}

export interface TldExtendedAttributesResponse {
  data: TldExtendedAttribute[];
}

export interface ExtendedAttributes {
  [key: string]: string | number | boolean;
}

export interface PreregisterData {
  domain: string;
  available: boolean;
  premium: boolean;
  registrant_id: number;
  whois_privacy: boolean;
  auto_renew: boolean;
  premium_price?: string;
  registration_price?: number;
  extended_attributes?: ExtendedAttributes;
  contact_info: Contact;
  tld_attributes?: TldExtendedAttribute[];
}

export interface AutoRenewalRequest {
  enable: boolean;
}

export interface AutoRenewalResponse {
  id: number;
  domain_id: number;
  domain_name: string;
  auto_renew: boolean;
  created_at: string;
  updated_at: string;
}

export interface ContactsResponse {
  data: Contact[];
  pagination?: DnsimplePagination;
}

export interface Contact {
  id: number;
  account_id: number;
  label: string;
  first_name: string;
  last_name: string;
  organization_name?: string;
  job_title?: string;
  address1: string;
  address2?: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  phone: string;
  fax?: string;
  email: string;
  created_at: string;
  updated_at: string;
}

import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/sequelize';
import { AxiosResponse } from 'axios';
import { firstValueFrom } from 'rxjs';

import { DnsimpleUserDomain } from './dnsimple-user-domain.entity';
import {
  ListDomainsResponse,
  DnsimpleDomainDetails,
  DomainAvailabilityResponse,
  DomainPremiumPriceResponse,
  DomainPricesResponse,
  DomainRegistrationRequest,
  DomainRegistrationResponse,
  TldExtendedAttributesResponse,
  TldExtendedAttribute,
  PreregisterData,
  ExtendedAttributes,
  AutoRenewalRequest,
  AutoRenewalResponse,
  ContactsResponse,
  Contact,
} from './dnsimple.types';

@Injectable()
export class DnsimpleService {
  private dnsimpeApiBase;
  private readonly log = new Logger(DnsimpleService.name);
  private readonly accessToken: string;
  private readonly accountId: string;

  constructor(
    private readonly config: ConfigService,
    private readonly http: HttpService,
    @InjectModel(DnsimpleUserDomain) private dnsimpleUserDomainModel: typeof DnsimpleUserDomain,
  ) {
    const isProd = this.config.get<string>('DNSIMPLE_ENABLE_PROD', 'false') === 'true';
    if (isProd) {
      this.dnsimpeApiBase = 'https://api.dnsimple.com';
    } else {
      this.dnsimpeApiBase = 'https://api.sandbox.dnsimple.com';
    }

    this.accessToken = this.config.get<string>('DNSIMPLE_ACCESS_TOKEN') ?? '';
    this.accountId = this.config.get<string>('DNSIMPLE_ACCOUNT_ID') ?? '';

    if (!this.accessToken || !this.accountId) {
      throw new Error('DNSIMPLE_ACCESS_TOKEN and DNSIMPLE_ACCOUNT_ID must be configured');
    }
  }

  private getAuthHeaders() {
    return {
      Authorization: `Bearer ${this.accessToken}`,
      Accept: 'application/json',
    };
  }

  async listDomainsForUser(userId: string): Promise<ListDomainsResponse> {
    const resp: AxiosResponse<ListDomainsResponse> = await firstValueFrom(
      this.http.get<ListDomainsResponse>(`${this.dnsimpeApiBase}/v2/${this.accountId}/domains`, {
        headers: this.getAuthHeaders(),
      }),
    );

    const allDomains = resp.data.data;

    const userDomains = await this.dnsimpleUserDomainModel.findAll({
      where: { user_id: userId },
    });

    const userDomainNames = userDomains.map((ud) => ud.domain_name);

    const filteredDomains = allDomains.filter((domain) => userDomainNames.includes(domain.name));

    return {
      ...resp.data,
      data: filteredDomains,
    };
  }

  async getDomainDetails(domainId: string): Promise<DnsimpleDomainDetails> {
    const resp: AxiosResponse<{ data: DnsimpleDomainDetails }> = await firstValueFrom(
      this.http.get<{ data: DnsimpleDomainDetails }>(
        `${this.dnsimpeApiBase}/v2/${this.accountId}/domains/${domainId}`,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );
    return resp.data.data;
  }

  async checkDomainAvailability(domainName: string): Promise<DomainAvailabilityResponse> {
    const resp: AxiosResponse<{ data: DomainAvailabilityResponse }> = await firstValueFrom(
      this.http.get<{ data: DomainAvailabilityResponse }>(
        `${this.dnsimpeApiBase}/v2/${this.accountId}/registrar/domains/${domainName}/check`,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );
    return resp.data.data;
  }

  async getDomainPremiumPrice(
    domainName: string,
    action: string = 'registration',
  ): Promise<DomainPremiumPriceResponse> {
    const resp: AxiosResponse<{ data: DomainPremiumPriceResponse }> = await firstValueFrom(
      this.http.get<{ data: DomainPremiumPriceResponse }>(
        `${this.dnsimpeApiBase}/v2/${this.accountId}/registrar/domains/${domainName}/premium_price?action=${action}`,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );
    return resp.data.data;
  }

  async getDomainPrices(domainName: string): Promise<DomainPricesResponse> {
    const resp: AxiosResponse<{ data: DomainPricesResponse }> = await firstValueFrom(
      this.http.get<{ data: DomainPricesResponse }>(
        `${this.dnsimpeApiBase}/v2/${this.accountId}/registrar/domains/${domainName}/prices`,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );
    return resp.data.data;
  }

  async getTldExtendedAttributes(tld: string): Promise<TldExtendedAttribute[]> {
    const resp: AxiosResponse<TldExtendedAttributesResponse> = await firstValueFrom(
      this.http.get<TldExtendedAttributesResponse>(
        `${this.dnsimpeApiBase}/v2/tlds/${tld}/extended_attributes`,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );
    return resp.data.data;
  }

  async preregisterDomain(domainName: string): Promise<PreregisterData> {
    const availability = await this.checkDomainAvailability(domainName);
    if (!availability.available) {
      throw new Error('Domain is not available for registration');
    }

    const contacts = await this.getContacts();
    if (contacts.length === 0) {
      throw new Error('No contacts found. Please create a contact first.');
    }
    const contact = contacts[0];

    const tld = domainName.split('.').pop() || '';
    let tldAttributes: TldExtendedAttribute[] = [];
    const extendedAttributes: ExtendedAttributes = {};

    try {
      tldAttributes = await this.getTldExtendedAttributes(tld);
      tldAttributes.forEach((attr) => {
        if (attr.required && attr.options && attr.options.length > 0) {
          extendedAttributes[attr.name] = attr.options[0].value;
        }
      });
    } catch (error) {
      console.warn(`No extended attributes for TLD ${tld}:`, error);
    }

    let premiumPrice: string | undefined;
    let registrationPrice: number | undefined;

    if (availability.premium) {
      const premiumPriceData = await this.getDomainPremiumPrice(domainName, 'registration');
      premiumPrice = premiumPriceData.premium_price;
    } else {
      const pricesData = await this.getDomainPrices(domainName);
      registrationPrice = pricesData.registration_price;
    }

    return {
      domain: domainName,
      available: availability.available,
      premium: availability.premium ?? false,
      registrant_id: contact.id,
      whois_privacy: false,
      auto_renew: true,
      premium_price: premiumPrice,
      registration_price: registrationPrice,
      extended_attributes:
        Object.keys(extendedAttributes).length > 0 ? extendedAttributes : undefined,
      contact_info: contact,
      tld_attributes: tldAttributes.length > 0 ? tldAttributes : undefined,
    };
  }

  async registerDomain(
    domainName: string,
    registrationData: DomainRegistrationRequest,
    userId: string,
  ): Promise<DomainRegistrationResponse> {
    const resp: AxiosResponse<{ data: DomainRegistrationResponse }> = await firstValueFrom(
      this.http.post<{ data: DomainRegistrationResponse }>(
        `${this.dnsimpeApiBase}/v2/${this.accountId}/registrar/domains/${domainName}/registrations`,
        registrationData,
        {
          headers: this.getAuthHeaders(),
        },
      ),
    );

    await this.dnsimpleUserDomainModel.create({
      user_id: userId,
      domain_name: domainName,
    });

    return resp.data.data;
  }

  async updateDomainAutoRenewal(
    domainName: string,
    autoRenewalData: AutoRenewalRequest,
  ): Promise<AutoRenewalResponse> {
    const url = `${this.dnsimpeApiBase}/v2/${this.accountId}/registrar/domains/${domainName}/auto_renewal`;
    const headers = this.getAuthHeaders();

    let resp: AxiosResponse<{ data: AutoRenewalResponse }>;

    if (autoRenewalData.enable) {
      resp = await firstValueFrom(
        this.http.put<{ data: AutoRenewalResponse }>(url, {}, { headers }),
      );
    } else {
      resp = await firstValueFrom(
        this.http.delete<{ data: AutoRenewalResponse }>(url, { headers }),
      );
    }

    return resp.data.data;
  }

  async checkDomainOwnership(domainName: string): Promise<boolean> {
    try {
      const resp: AxiosResponse<{ data: any }> = await firstValueFrom(
        this.http.get<{ data: any }>(
          `${this.dnsimpeApiBase}/v2/${this.accountId}/domains/${domainName}`,
          {
            headers: this.getAuthHeaders(),
          },
        ),
      );

      return resp.data && resp.data.data;
    } catch (error) {
      this.log.error(`Failed to check domain ownership for ${domainName}:`, error);
      return false;
    }
  }

  async createDnsRecord(
    domainName: string,
    record: { name: string; type: string; content: string; ttl?: number },
  ) {
    try {
      const resp: AxiosResponse<{ data: any }> = await firstValueFrom(
        this.http.post<{ data: any }>(
          `${this.dnsimpeApiBase}/v2/${this.accountId}/zones/${domainName}/records`,
          {
            name: record.name,
            type: record.type,
            content: record.content,
            ttl: record.ttl || 3600,
          },
          {
            headers: this.getAuthHeaders(),
          },
        ),
      );

      return resp.data.data;
    } catch (error) {
      this.log.error(`Failed to create DNS record for domain ${domainName}:`, error);
      throw error;
    }
  }

  async connectDomainToVercel(
    domain: string,
    projectId: string,
    vercelService: any,
  ): Promise<{
    success: boolean;
    message: string;
    domain: string;
    projectId: string;
    dnsRecordsAdded: number;
  }> {
    try {
      const isOwner = await this.checkDomainOwnership(domain);
      if (!isOwner) {
        throw new Error(`Domain ${domain} does not belong to the current account`);
      }

      await vercelService.addProjectDomain(projectId, domain);

      const config = await vercelService.getProjectDomainConfig(projectId, domain);

      let dnsRecordsAdded = 0;

      if (config.recommendedIPv4 && config.recommendedIPv4.length > 0) {
        const ipRecord = config.recommendedIPv4[0];
        if (ipRecord.value && ipRecord.value.length > 0) {
          const ipValue = ipRecord.value[0];
          await this.createDnsRecord(domain, {
            name: '@',
            type: 'A',
            content: ipValue,
            ttl: 3600,
          });
          dnsRecordsAdded++;
        }
      }

      if (config.recommendedCNAME && config.recommendedCNAME.length > 0) {
        const cname = config?.recommendedCNAME?.value;
        if (cname) {
          await this.createDnsRecord(domain, {
            name: '@',
            type: 'CNAME',
            content: cname,
            ttl: 3600,
          });
          dnsRecordsAdded++;
        }
      }

      return {
        success: true,
        message: `Domain ${domain} successfully connected to Vercel project ${projectId}`,
        domain,
        projectId,
        dnsRecordsAdded,
      };
    } catch (error) {
      this.log.error(`Failed to connect domain ${domain} to Vercel project ${projectId}:`, error);
      throw error;
    }
  }

  async getContacts(): Promise<Contact[]> {
    const resp: AxiosResponse<ContactsResponse> = await firstValueFrom(
      this.http.get<ContactsResponse>(`${this.dnsimpeApiBase}/v2/${this.accountId}/contacts`, {
        headers: this.getAuthHeaders(),
      }),
    );
    return resp.data.data;
  }
}

import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';

import { DnsimpleUserDomain } from './dnsimple-user-domain.entity';
import { DnsimpleController } from './dnsimple.controller';
import { DnsimpleService } from './dnsimple.service';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';
import { VercelService } from '../../common/services/vercel/vercel.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ConfigModule,
    HttpModule,
    UsersModule,
    SequelizeModule.forFeature([DnsimpleUserDomain]),
  ],
  controllers: [DnsimpleController],
  providers: [...clerkClientProviders, DnsimpleService, VercelService],
  exports: [DnsimpleService],
})
export class DnsimpleModule {}

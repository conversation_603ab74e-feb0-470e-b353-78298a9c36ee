import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsNotEmpty } from 'class-validator';

export class ContactDto {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  account_id!: number;

  @ApiProperty()
  label!: string;

  @ApiProperty()
  first_name!: string;

  @ApiProperty()
  last_name!: string;

  @ApiPropertyOptional()
  organization_name?: string;

  @ApiPropertyOptional()
  job_title?: string;

  @ApiProperty()
  address1!: string;

  @ApiPropertyOptional()
  address2?: string;

  @ApiProperty()
  city!: string;

  @ApiProperty()
  state_province!: string;

  @ApiProperty()
  postal_code!: string;

  @ApiProperty()
  country!: string;

  @ApiProperty()
  phone!: string;

  @ApiPropertyOptional()
  fax?: string;

  @ApiProperty()
  email!: string;

  @ApiProperty()
  created_at!: string;

  @ApiProperty()
  updated_at!: string;
}

export class CreateContactRequestDto {
  @ApiPropertyOptional({ description: 'Contact label' })
  @IsOptional()
  @IsString()
  label?: string;

  @ApiProperty({ description: 'First name', required: true })
  @IsNotEmpty()
  @IsString()
  first_name!: string;

  @ApiProperty({ description: 'Last name', required: true })
  @IsNotEmpty()
  @IsString()
  last_name!: string;

  @ApiPropertyOptional({ description: 'Organization name' })
  @IsOptional()
  @IsString()
  organization_name?: string;

  @ApiPropertyOptional({ description: 'Job title' })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiProperty({ description: 'Address line 1', required: true })
  @IsNotEmpty()
  @IsString()
  address1!: string;

  @ApiPropertyOptional({ description: 'Address line 2' })
  @IsOptional()
  @IsString()
  address2?: string;

  @ApiProperty({ description: 'City', required: true })
  @IsNotEmpty()
  @IsString()
  city!: string;

  @ApiProperty({ description: 'State or province', required: true })
  @IsNotEmpty()
  @IsString()
  state_province!: string;

  @ApiProperty({ description: 'Postal code', required: true })
  @IsNotEmpty()
  @IsString()
  postal_code!: string;

  @ApiProperty({ description: 'Country code (ISO 3166-1 alpha-2)', required: true })
  @IsNotEmpty()
  @IsString()
  country!: string;

  @ApiProperty({ description: 'Email address', required: true })
  @IsNotEmpty()
  @IsEmail()
  email!: string;

  @ApiProperty({ description: 'Phone number', required: true })
  @IsNotEmpty()
  @IsString()
  phone!: string;

  @ApiPropertyOptional({ description: 'Fax number' })
  @IsOptional()
  @IsString()
  fax?: string;
}

export class CreateContactResponseDto {
  @ApiProperty({ description: 'Created contact', type: ContactDto })
  contact!: ContactDto;
}

export class ListContactsResponseDto {
  @ApiProperty({ description: 'List of contacts', type: [ContactDto] })
  contacts!: ContactDto[];

  @ApiProperty({ description: 'Pagination information' })
  pagination!: {
    current_page: number;
    per_page: number;
    total_entries: number;
    total_pages: number;
  };
}

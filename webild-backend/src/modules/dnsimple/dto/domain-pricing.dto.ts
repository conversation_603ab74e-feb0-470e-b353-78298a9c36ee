import { ApiProperty } from '@nestjs/swagger';

export class DomainPremiumPriceDto {
  @ApiProperty()
  premium_price!: string;

  @ApiProperty()
  action!: string;
}

export class DomainPricesDto {
  @ApiProperty()
  domain!: string;

  @ApiProperty()
  premium!: boolean;

  @ApiProperty()
  registration_price!: number;

  @ApiProperty()
  renewal_price!: number;

  @ApiProperty()
  transfer_price!: number;

  @ApiProperty()
  restore_price!: number;
}

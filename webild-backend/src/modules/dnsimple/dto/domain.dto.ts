import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DnsimpleDomainDto {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  name!: string;

  @ApiPropertyOptional()
  unicode_name?: string;

  @ApiPropertyOptional()
  state?: string;

  @ApiPropertyOptional()
  created_at?: string;
}

export class DnsimplePaginationDto {
  @ApiPropertyOptional()
  current_page?: number;

  @ApiPropertyOptional()
  per_page?: number;

  @ApiPropertyOptional()
  total_entries?: number;

  @ApiPropertyOptional()
  total_pages?: number;
}

export class ListDomainsDtoResponse {
  @ApiProperty({ type: [DnsimpleDomainDto] })
  data!: DnsimpleDomainDto[];

  @ApiPropertyOptional({ type: DnsimplePaginationDto })
  pagination?: DnsimplePaginationDto;
}

import { ApiProperty } from '@nestjs/swagger';

export class VercelConnectRequestDto {
  @ApiProperty({ description: 'Domain name to connect' })
  domain!: string;

  @ApiProperty({ description: 'Vercel project ID' })
  projectId!: string;
}

export class VercelConnectResponseDto {
  @ApiProperty()
  success!: boolean;

  @ApiProperty()
  message!: string;

  @ApiProperty()
  domain!: string;

  @ApiProperty()
  projectId!: string;

  @ApiProperty({ description: 'Number of DNS records added to DNSimple' })
  dnsRecordsAdded!: number;
}

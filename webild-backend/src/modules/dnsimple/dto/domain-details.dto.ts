import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DomainDetailsDto {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  name!: string;

  @ApiPropertyOptional()
  unicode_name?: string;

  @ApiPropertyOptional()
  state?: string;

  @ApiPropertyOptional()
  auto_renew?: boolean;

  @ApiPropertyOptional()
  private_whois?: boolean;

  @ApiPropertyOptional()
  expires_at?: string;

  @ApiPropertyOptional()
  created_at?: string;

  @ApiPropertyOptional()
  updated_at?: string;
}

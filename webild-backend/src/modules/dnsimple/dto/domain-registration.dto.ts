import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { ContactDto } from './contact.dto';
import { TldExtendedAttributeDto } from './tld.dto';

export class DomainRegistrationRequestDto {
  @ApiProperty({ description: 'Registrant contact ID in DNSimple' })
  registrant_id!: number;

  @ApiPropertyOptional({ description: 'Enable whois privacy', default: false })
  whois_privacy?: boolean;

  @ApiPropertyOptional({ description: 'Enable auto renew', default: false })
  auto_renew?: boolean;

  @ApiPropertyOptional({ description: 'Premium price confirmation (required for premium domains)' })
  premium_price?: string;

  @ApiPropertyOptional({ description: 'Extended attributes for TLD-specific requirements' })
  extended_attributes?: Record<string, string | number | boolean>;
}

export class DomainRegistrationResponseDto {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  state!: string;

  @ApiProperty()
  domain_id!: number;

  @ApiPropertyOptional()
  created_at?: string;
}

export class PreregisterDataDto {
  @ApiProperty()
  domain!: string;

  @ApiProperty()
  available!: boolean;

  @ApiProperty()
  premium!: boolean;

  @ApiProperty()
  registrant_id!: number;

  @ApiProperty()
  whois_privacy!: boolean;

  @ApiProperty()
  auto_renew!: boolean;

  @ApiPropertyOptional()
  premium_price?: string;

  @ApiPropertyOptional()
  registration_price?: number;

  @ApiPropertyOptional()
  extended_attributes?: Record<string, string | number | boolean>;

  @ApiProperty({ type: ContactDto })
  contact_info!: ContactDto;

  @ApiPropertyOptional({ type: [TldExtendedAttributeDto] })
  tld_attributes?: TldExtendedAttributeDto[];
}

import { ApiProperty } from '@nestjs/swagger';

export class AutoRenewalRequestDto {
  @ApiProperty({ description: 'Enable or disable auto renewal for the domain' })
  enable!: boolean;
}

export class AutoRenewalResponseDto {
  @ApiProperty({ description: 'Domain registration ID' })
  id!: number;

  @ApiProperty({ description: 'Domain ID in DNSimple' })
  domain_id!: number;

  @ApiProperty({ description: 'Domain name' })
  domain_name!: string;

  @ApiProperty({ description: 'Auto renewal status' })
  auto_renew!: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at!: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updated_at!: string;
}

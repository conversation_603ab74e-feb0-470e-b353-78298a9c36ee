import {
  Table,
  Column,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Type,
  Foreign<PERSON>ey,
  Unique,
} from 'sequelize-typescript';

import { User } from '../users/users.entity';

export interface DnsimpleOauthCreationAttrs {
  userId: string;
  accountId?: string | null;
  accessToken?: string | null;
  refreshToken?: string | null;
  tokenExpiresAt?: Date | null;
}

@Table({
  tableName: 'dnsimple_oauths',
  timestamps: true,
})
export class DnsimpleOauth extends Model<DnsimpleOauth, DnsimpleOauthCreationAttrs> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => User)
  @Unique
  @Column({ type: DataType.UUID, allowNull: false, field: 'user_id' })
  declare userId: string;

  @Column({ type: DataType.STRING, allowNull: true, field: 'account_id' })
  declare accountId: string | null;

  @Column({ type: DataType.STRING, allowNull: true, field: 'access_token' })
  declare accessToken: string | null;

  @Column({ type: DataType.STRING, allowNull: true, field: 'refresh_token' })
  declare refreshToken: string | null;

  @Column({ type: DataType.DATE, allowNull: true, field: 'token_expires_at' })
  declare tokenExpiresAt: Date | null;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;
}

import { applyDecorators } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { ResponseWrapperDto } from 'src/common/dto/response-wrapper.dto';

import { AutoRenewalRequestDto, AutoRenewalResponseDto } from './dto/auto-renewal.dto';
import {
  CreateContactRequestDto,
  CreateContactResponseDto,
  ListContactsResponseDto,
} from './dto/contact.dto';
import { DomainAvailabilityDto } from './dto/domain-availability.dto';
import { DomainDetailsDto } from './dto/domain-details.dto';
import { DomainPremiumPriceDto, DomainPricesDto } from './dto/domain-pricing.dto';
import {
  DomainRegistrationRequestDto,
  DomainRegistrationResponseDto,
  PreregisterDataDto,
} from './dto/domain-registration.dto';
import { ListDomainsDtoResponse } from './dto/domain.dto';
import { VercelConnectRequestDto, VercelConnectResponseDto } from './dto/vercel-connect.dto';

export function SwaggerOAuthLogin() {
  return applyDecorators(
    ApiExtraModels(ResponseWrapperDto),
    ApiOkResponse({
      description: 'Get DNSimple OAuth authorization URL',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: {
                type: 'object',
                properties: {
                  url: { type: 'string', description: 'OAuth authorization URL' },
                },
              },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerOAuthCallback() {
  return applyDecorators(
    ApiQuery({
      name: 'code',
      description: 'OAuth authorization code from DNSimple',
      required: true,
    }),
    ApiQuery({ name: 'state', description: 'OAuth state parameter for security', required: true }),
    ApiOkResponse({
      description: 'OAuth callback - redirects to frontend after processing',
      schema: {
        type: 'string',
        description: 'Redirect response',
      },
    }),
  );
}

export function SwaggerListDomains() {
  return applyDecorators(
    ApiExtraModels(ResponseWrapperDto, ListDomainsDtoResponse),
    ApiOkResponse({
      description: 'List domains',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(ListDomainsDtoResponse) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerGetDomainDetails() {
  return applyDecorators(
    ApiParam({ name: 'domainId', description: 'Domain ID', type: 'string' }),
    ApiExtraModels(ResponseWrapperDto, DomainDetailsDto),
    ApiOkResponse({
      description: 'Get domain details',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DomainDetailsDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerCheckDomainAvailability() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to check (e.g., example.com)',
      type: 'string',
    }),
    ApiExtraModels(ResponseWrapperDto, DomainAvailabilityDto),
    ApiOkResponse({
      description: 'Check domain availability',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DomainAvailabilityDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerGetDomainPremiumPrice() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to check premium price (e.g., example.com)',
      type: 'string',
    }),
    ApiQuery({
      name: 'action',
      description: 'Action type: registration, renewal, or transfer',
      required: false,
      type: 'string',
    }),
    ApiExtraModels(ResponseWrapperDto, DomainPremiumPriceDto),
    ApiOkResponse({
      description: 'Get domain premium price',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DomainPremiumPriceDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerGetDomainPrices() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to get prices (e.g., example.com)',
      type: 'string',
    }),
    ApiExtraModels(ResponseWrapperDto, DomainPricesDto),
    ApiOkResponse({
      description: 'Get domain prices',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DomainPricesDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerPreregisterDomain() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to preregister (e.g., example.com)',
      type: 'string',
    }),
    ApiExtraModels(ResponseWrapperDto, PreregisterDataDto),
    ApiOkResponse({
      description: 'Get domain preregistration data',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(PreregisterDataDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerRegisterDomain() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to register (e.g., example.com)',
      type: 'string',
    }),
    ApiBody({ type: DomainRegistrationRequestDto }),
    ApiExtraModels(ResponseWrapperDto, DomainRegistrationResponseDto),
    ApiOkResponse({
      description: 'Register domain',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(DomainRegistrationResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerUpdateDomainAutoRenewal() {
  return applyDecorators(
    ApiParam({
      name: 'domainName',
      description: 'Domain name to update auto renewal status (e.g., example.com)',
      type: 'string',
    }),
    ApiBody({ type: AutoRenewalRequestDto }),
    ApiExtraModels(ResponseWrapperDto, AutoRenewalRequestDto, AutoRenewalResponseDto),
    ApiOkResponse({
      description: 'Update domain auto renewal status',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(AutoRenewalResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerVercelConnect() {
  return applyDecorators(
    ApiBody({ type: VercelConnectRequestDto }),
    ApiExtraModels(ResponseWrapperDto, VercelConnectRequestDto, VercelConnectResponseDto),
    ApiOkResponse({
      description: 'Connect DNSimple domain to Vercel project',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(VercelConnectResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerListContacts() {
  return applyDecorators(
    ApiQuery({
      name: 'sort',
      description: 'Sort contacts by: id, label, or email',
      required: false,
      type: 'string',
    }),
    ApiExtraModels(ResponseWrapperDto, ListContactsResponseDto),
    ApiOkResponse({
      description: 'List contacts in the account',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(ListContactsResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

export function SwaggerCreateContact() {
  return applyDecorators(
    ApiBody({ type: CreateContactRequestDto }),
    ApiExtraModels(ResponseWrapperDto, CreateContactRequestDto, CreateContactResponseDto),
    ApiOkResponse({
      description: 'Create a new contact',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateContactResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

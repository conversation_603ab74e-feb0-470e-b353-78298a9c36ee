import { Controller, Get, Post, Put, Query, UseGuards, Param, Body } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { successResponse, throwSmartErrorResponse } from 'src/common/utils/response.util';

import { DnsimpleService } from './dnsimple.service';
import {
  SwaggerListDomains,
  SwaggerGetDomainDetails,
  SwaggerCheckDomainAvailability,
  SwaggerGetDomainPremiumPrice,
  SwaggerGetDomainPrices,
  SwaggerPreregisterDomain,
  SwaggerRegisterDomain,
  SwaggerUpdateDomainAutoRenewal,
  SwaggerVercelConnect,
} from './dnsimple.swagger';
import { AutoRenewalRequestDto } from './dto/auto-renewal.dto';
import { DomainRegistrationRequestDto } from './dto/domain-registration.dto';
import { VercelConnectRequestDto } from './dto/vercel-connect.dto';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { ClerkAuthGuard } from '../../common/guards/clerk-auth.guard';
import { VercelService } from '../../common/services/vercel/vercel.service';
import { User } from '../users/users.entity';

@ApiBearerAuth('clerk-auth')
@Controller('dnsimple')
export class DnsimpleController {
  constructor(
    private readonly dnsimpleService: DnsimpleService,
    private readonly vercelService: VercelService,
  ) {}

  @UseGuards(ClerkAuthGuard)
  @Get('domains')
  @SwaggerListDomains()
  async listDomains(@DbAuthUser() dbUser: User) {
    try {
      const data = await this.dnsimpleService.listDomainsForUser(dbUser.id);
      return successResponse(data, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Get('domains/:domainId')
  @SwaggerGetDomainDetails()
  async getDomainDetails(@Param('domainId') domainId: string) {
    try {
      const data = await this.dnsimpleService.getDomainDetails(domainId);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Get('domains/check/:domainName')
  @SwaggerCheckDomainAvailability()
  async checkDomainAvailability(@Param('domainName') domainName: string) {
    try {
      const data = await this.dnsimpleService.checkDomainAvailability(domainName);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Get('domains/premium-price/:domainName')
  @SwaggerGetDomainPremiumPrice()
  async getDomainPremiumPrice(
    @Param('domainName') domainName: string,
    @Query('action') action: string = 'registration',
  ) {
    try {
      const data = await this.dnsimpleService.getDomainPremiumPrice(domainName, action);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Get('domains/prices/:domainName')
  @SwaggerGetDomainPrices()
  async getDomainPrices(@Param('domainName') domainName: string) {
    try {
      const data = await this.dnsimpleService.getDomainPrices(domainName);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Post('domains/preregister/:domainName')
  @SwaggerPreregisterDomain()
  async preregisterDomain(@Param('domainName') domainName: string) {
    try {
      const data = await this.dnsimpleService.preregisterDomain(domainName);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Post('domains/register/:domainName')
  @SwaggerRegisterDomain()
  async registerDomain(
    @Param('domainName') domainName: string,
    @Body() registrationData: DomainRegistrationRequestDto,
    @DbAuthUser() dbUser: User,
  ) {
    try {
      const data = await this.dnsimpleService.registerDomain(
        domainName,
        registrationData,
        dbUser.id,
      );
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Put('domains/auto-renewal/:domainName')
  @SwaggerUpdateDomainAutoRenewal()
  async updateDomainAutoRenewal(
    @Param('domainName') domainName: string,
    @Body() autoRenewalData: AutoRenewalRequestDto,
  ) {
    try {
      const data = await this.dnsimpleService.updateDomainAutoRenewal(domainName, autoRenewalData);
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }

  @UseGuards(ClerkAuthGuard)
  @Post('vercel/connect')
  @SwaggerVercelConnect()
  async connectDomainToVercel(@Body() connectData: VercelConnectRequestDto) {
    try {
      const data = await this.dnsimpleService.connectDomainToVercel(
        connectData.domain,
        connectData.projectId,
        this.vercelService,
      );
      return successResponse({ data }, 200);
    } catch (error) {
      throwSmartErrorResponse(error);
    }
  }
}

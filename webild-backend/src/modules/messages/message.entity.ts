import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';

import { MessageTypes } from './message.constants';
import { ProjectVersion } from '../projects/project-version.entity';
import { Project } from '../projects/project.entity';
import { User } from '../users/users.entity';

@Table({ tableName: 'messages', timestamps: true })
export class Message extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: string;

  @ForeignKey(() => Project)
  @Column({ field: 'project_id', type: DataType.UUID })
  declare project_id: string;

  @ForeignKey(() => ProjectVersion)
  @Column({ field: 'version_id', type: DataType.UUID })
  declare version_id: string;

  @Column({ field: 'version_number', type: DataType.INTEGER, allowNull: false })
  declare version_number: number;

  @ForeignKey(() => User)
  @Column({ field: 'user_id', type: DataType.UUID, allowNull: true })
  declare user_id: string;

  @Column({ type: DataType.ENUM(...Object.values(MessageTypes)) })
  declare type: MessageTypes;

  @Column({ type: DataType.BOOLEAN })
  declare is_system: boolean;

  @Column({ type: DataType.TEXT })
  declare message: string;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;

  @BelongsTo(() => Project)
  declare project?: Project;

  @BelongsTo(() => ProjectVersion)
  declare version?: ProjectVersion;

  @BelongsTo(() => User)
  declare user?: User;
}

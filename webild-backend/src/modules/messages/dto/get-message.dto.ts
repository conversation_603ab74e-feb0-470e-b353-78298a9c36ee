import { ApiProperty } from '@nestjs/swagger';

import { MessageTypes } from '../message.constants';

export class GetMessageDto {
  @ApiProperty()
  declare id: string;

  @ApiProperty()
  declare version_number: number;

  @ApiProperty({ enum: MessageTypes })
  declare type: MessageTypes;

  @ApiProperty()
  declare message: string;

  @ApiProperty()
  declare createdAt: Date;

  @ApiProperty()
  declare updatedAt: Date;
}

import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';

import { CreateMessageDto } from './dto/create-message.dto';
import { MessageTypes } from './message.constants';
import { MessagesService } from './messages.service';
import { SwaggerCreateMessage, SwaggerEditMessage, SwaggerGetMessages } from './messages.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { CursorPaginationQueryDto } from '../../common/dto/cursor-pagination.dto';
import { ProjectOwnershipGuard } from '../../common/guards/project-ownership.guard';
import { RequestLimitInterceptor } from '../../common/interceptors/request-limit.interceptor';
import { SystemPrompts } from '../../common/services/openai/openai.service';
import { errorResponse, successResponse } from '../../common/utils/response.util';
import { ProjectVersionStatus } from '../projects/project.constants';
import { ProjectsService } from '../projects/projects.service';
import { User } from '../users/users.entity';
import { EditMessageDto } from './dto/edit-message.dto';

@ApiBearerAuth('clerk-auth')
@Controller('projects')
export class MessagesController {
  constructor(
    private readonly messagesService: MessagesService,
    private readonly projectsService: ProjectsService,
  ) {}

  @SwaggerEditMessage()
  @UseGuards(ProjectOwnershipGuard)
  @UseInterceptors(RequestLimitInterceptor)
  @Post(':projectId/versions/:versionId/messages/edit')
  async editMessage(
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Body() dto: EditMessageDto,
  ) {
    try {
      const preview = await this.messagesService.editMessage(projectId, versionId, dto);
      return successResponse({ preview }, 200);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerCreateMessage()
  @UseGuards(ProjectOwnershipGuard)
  @UseInterceptors(RequestLimitInterceptor)
  @Post(':projectId/versions/:versionId/messages')
  async createMessage(
    @DbAuthUser() user: User,
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Body() dto: CreateMessageDto,
  ) {
    try {
      dto.type = MessageTypes.User;
      let isGenerating = false;

      const projectVersion = await this.projectsService.getProjectVersion(projectId, versionId);

      if (
        (projectVersion.version_status as ProjectVersionStatus) === ProjectVersionStatus.Executed
      ) {
        await this.projectsService.updateVersionCode(projectId, versionId, user.id, dto.message);

        isGenerating = true;
        const message = {
          message: 'Start',
          type: 'assistant',
          is_system: false,
        };

        return successResponse({ data: { message, isGenerating } }, 201);
      }

      const message = await this.messagesService.askNStoreAI(user.id, projectId, versionId, dto, [
        SystemPrompts.USER_AGENT,
      ]);

      if (message.message === 'Start') {
        this.projectsService.executeVersionPrompt(projectId, versionId, user.id);
        isGenerating = true;
      }

      return successResponse(
        {
          data: {
            message,
            isGenerating,
          },
        },
        201,
      );
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerGetMessages()
  @Get(':projectId/versions/:versionId/messages')
  async getVersionMessages(
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Query() query: CursorPaginationQueryDto,
  ) {
    const result = await this.messagesService.getPaginatedMessages({ projectId, versionId, query });
    return successResponse(result);
  }

  @SwaggerGetMessages()
  @Get(':projectId/messages')
  async getProjectMessages(
    @Param('projectId') projectId: string,
    @Query() query: CursorPaginationQueryDto,
  ) {
    const result = await this.messagesService.getPaginatedMessages({ projectId, query });
    return successResponse(result);
  }
}

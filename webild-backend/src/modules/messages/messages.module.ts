import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { Message } from './message.entity';
import { MessagesController } from './messages.controller';
import { MessagesService } from './messages.service';
import { SSEController } from './sse.controller';
import { SSEAuthGuard } from '../../common/guards/sse-auth.guard';
import { RequestLimitInterceptor } from '../../common/interceptors/request-limit.interceptor';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';
import { GithubService } from '../../common/services/github/github.service';
import { OpenaiService } from '../../common/services/openai/openai.service';
import { SandboxService } from '../../common/services/sandbox/sandbox.service';
import { SSEService } from '../../common/services/sse/sse.service';
import { ProjectUser } from '../projects/project-user.entity';
import { ProjectsModule } from '../projects/projects.module';
import { PlanLimit } from '../stripe/plan-limit.entity';
import { Subscription } from '../stripe/subscriptions.entity';
import { UsageCounter } from '../stripe/usage-counters.entity';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Message, ProjectUser, UsageCounter, PlanLimit, Subscription]),
    forwardRef(() => ProjectsModule),
    UsersModule,
  ],
  providers: [
    ...clerkClientProviders,
    SSEAuthGuard,
    MessagesService,
    OpenaiService,
    SSEService,
    RequestLimitInterceptor,
    GithubService,
    SandboxService,
  ],
  exports: [MessagesService, SSEService],
  controllers: [MessagesController, SSEController],
})
export class MessagesModule {}

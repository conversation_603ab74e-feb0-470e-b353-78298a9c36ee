import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

import { CreateMessageDto } from './dto/create-message.dto';
import { EditMessageDto } from './dto/edit-message.dto';
import { MessageTypes } from './message.constants';
import { Message } from './message.entity';
import {
  CursorPaginationQueryDto,
  CursorPaginationResponseDto,
} from '../../common/dto/cursor-pagination.dto';
import { GithubService } from '../../common/services/github/github.service';
import {
  GenerateCodeType,
  OpenaiService,
  SystemPrompts,
  UserContentPart,
} from '../../common/services/openai/openai.service';
import { welcomePrompt } from '../../common/services/openai/prompts/welcomePrompt';
import { paginateCursor } from '../../common/utils/paginate-cursor.util';
import { ProjectsService } from '../projects/projects.service';
import { GetMessageDto } from './dto/get-message.dto';
import { SandboxService } from '../../common/services/sandbox/sandbox.service';
import { SSEService } from '../../common/services/sse/sse.service';
import { listZipFilePaths, readZipTextFileBySuffix } from '../../common/utils/file-utils';

@Injectable()
export class MessagesService {
  constructor(
    @InjectModel(Message) private readonly messageModel: typeof Message,
    private readonly openaiService: OpenaiService,
    @Inject(forwardRef(() => ProjectsService))
    private readonly projectsService: ProjectsService,
    private readonly sseService: SSEService,
    private readonly gitHubService: GithubService,
    private readonly sandboxService: SandboxService,
  ) {}

  async create(
    userId: string,
    projectId: string,
    versionId: string,
    dto: CreateMessageDto,
  ): Promise<Message> {
    const projectVersion = await this.projectsService.getProjectVersion(projectId, versionId);

    const message = await this.messageModel.create({
      ...dto,
      user_id: userId,
      project_id: projectId,
      version_id: versionId,
      version_number: projectVersion.version_number,
    });

    this.sseService.sendToUser(userId, {
      type: 'message_created',
      data: {
        id: message.id,
        project_id: message.project_id,
        version_id: message.version_id,
        version_number: message.version_number,
        type: message.type,
        message: message.message,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
      },
    });

    return message;
  }

  async askNStoreAI(
    userId: string,
    projectId: string,
    versionId: string,
    dto: CreateMessageDto,
    systemPrompts: SystemPrompts[] = [],
    useHistory: boolean = true,
    type: GenerateCodeType = GenerateCodeType.PROMPT_GENERATE,
    richUserContent?: UserContentPart[],
    isSystem: boolean = false,
  ): Promise<Message> {
    const historyMessages = await this.getHistoryMessages(projectId, versionId);

    const userMessage = await this.create(userId, projectId, versionId, {
      message: dto.message,
      type: MessageTypes.User,
      is_system: isSystem,
    });

    const aiAnswer = await this.openaiService.sendMessage(
      userMessage.message,
      systemPrompts,
      useHistory ? historyMessages : [],
      type,
      richUserContent,
    );

    const aiMessage = await this.create(userId, projectId, versionId, {
      message: aiAnswer,
      type: MessageTypes.AI,
      is_system: isSystem,
    });

    return aiMessage;
  }

  async initAIDialogue(userId: string, projectId: string, versionId: string): Promise<Message> {
    const aiAnswer = await this.openaiService.sendMessage('', [SystemPrompts.WELCOME]);
    return await this.create(userId, projectId, versionId, {
      message: aiAnswer,
      type: MessageTypes.AI,
    });
  }

  async getHistoryMessages(projectId: string, versionId: string): Promise<Message[]> {
    const projectVersion = await this.projectsService.getProjectVersion(projectId, versionId);

    const messages = await this.messageModel.findAll({
      where: {
        project_id: projectId,
        version_number: { [Op.lte]: projectVersion.version_number },
      },
      order: [['created_at', 'ASC']],
    });

    return [{ type: MessageTypes.User, message: welcomePrompt } as Message, ...messages];
  }

  async generateVersionPrompt(projectId: string, versionId: string): Promise<string> {
    const historyMessages = await this.getHistoryMessages(projectId, versionId);

    return await this.openaiService.sendMessage(
      'Plan single-page site',
      [SystemPrompts.OPTIMIZE],
      historyMessages,
    );
  }

  async getPaginatedMessages(props: {
    projectId: string;
    versionId?: string;
    query: CursorPaginationQueryDto;
  }): Promise<CursorPaginationResponseDto<GetMessageDto>> {
    const { projectId, versionId, query } = props;

    const { data, meta } = await paginateCursor(Message, {
      limit: query.limit ?? 10,
      cursor: query.cursor,
      where: {
        project_id: projectId,
        ...(versionId ? { version_id: versionId } : {}),
      },
      orderBy: 'created_at',
      orderDirection: 'DESC',
    });

    const mapped = data.map((msg) => ({
      id: msg.id,
      version_number: msg.version_number,
      type: msg.type,
      is_system: msg.is_system,
      message: msg.message,
      createdAt: msg.createdAt,
      updatedAt: msg.updatedAt,
    }));

    return {
      data: mapped,
      meta,
    };
  }

  async editMessage(projectId: string, versionId: string, dto: EditMessageDto) {
    try {
      const projectFiles = await this.editProject(projectId, versionId, dto);

      await this.projectsService.createProjectFromFiles(projectFiles.files, projectId, false);

      const { repositoryOwner, repositoryName, branch } =
        await this.projectsService.pushProjectToGitHub(projectId, versionId);

      await new Promise((r) => setTimeout(r, 2000));
      await this.gitHubService.forcePushBranchToMain(repositoryOwner, repositoryName, branch);

      await this.projectsService.saveLastSuccessfulFiles(
        projectId,
        versionId,
        JSON.stringify(projectFiles),
      );

      const project = await this.projectsService.getProject({ id: projectId });

      const resp = await this.sandboxService.updateSandboxWithPreview(
        project.repositoryName,
        project.sandbox_id,
      );
      await this.projectsService.updateProject(projectId, {
        preview_url: resp.preview,
        sandbox_id: resp.sandboxId,
      });

      return resp.preview;
    } catch (e) {
      console.log('Error while editMessage', e);
    }
  }

  async editProject(projectId: string, versionId: string, dto: EditMessageDto) {
    try {
      const last = await this.projectsService.getLastSuccessfulFiles(projectId, versionId);
      if (!last?.files?.length) {
        throw new Error('No previously generated files found. Generate a project first.');
      }

      const pageFileIndex = last.files.findIndex(
        (file: { path: string; content: string }) => file.path === 'src/app/page.tsx',
      );
      if (pageFileIndex === -1) {
        throw new Error('page.tsx not found.');
      }

      const pageFile = last.files[pageFileIndex];
      const content = pageFile.content;

      if (dto.sectionId) {
        const idPattern = `<div id="${dto.sectionId}"`;
        const sectionStart = content.indexOf(idPattern);
        if (sectionStart === -1) {
          throw new Error(`Section with id "${dto.sectionId}" not found.`);
        }

        let openDivCount = 1;
        let currentIndex = content.indexOf('>', sectionStart) + 1;
        while (openDivCount > 0 && currentIndex < content.length) {
          if (content.startsWith('<div', currentIndex)) {
            openDivCount++;
            currentIndex = content.indexOf('>', currentIndex) + 1;
          } else if (content.startsWith('</div>', currentIndex)) {
            openDivCount--;
            currentIndex += 6;
          } else {
            currentIndex++;
          }
        }
        if (openDivCount !== 0) {
          throw new Error('Invalid section structure: unclosed div tags.');
        }
        const sectionEnd = currentIndex;

        const sectionContent = content.substring(sectionStart, sectionEnd);

        let updatedProject = {
          files: last.files,
        };

        if (sectionContent.includes(dto.oldValue)) {
          const updatedSectionContent = sectionContent.replaceAll(dto.oldValue, dto.newValue);

          const updatedContent =
            content.substring(0, sectionStart) +
            updatedSectionContent +
            content.substring(sectionEnd);

          last.files[pageFileIndex] = {
            ...pageFile,
            content: updatedContent,
          };

          updatedProject = {
            files: last.files,
          };
          return updatedProject;
        } else {
          const componentStart = sectionContent.indexOf('<', sectionContent.indexOf('>') + 1);
          if (componentStart === -1) {
            throw new Error('No component found in the section.');
          }

          let nameEnd = sectionContent.indexOf(' ', componentStart + 1);
          const nameEndGt = sectionContent.indexOf('>', componentStart + 1);
          if (nameEnd === -1 || (nameEndGt !== -1 && nameEndGt < nameEnd)) {
            nameEnd = nameEndGt;
          }
          if (nameEnd === -1) {
            throw new Error('Invalid component structure.');
          }

          const componentName = sectionContent.substring(componentStart + 1, nameEnd).trim();

          const importRegex = new RegExp(`import\\s+${componentName}\\s+from\\s+['"]([^'"]+)['"];`);
          const importMatch = content.match(importRegex);
          if (!importMatch || !importMatch[1]) {
            throw new Error(`Import not found for component "${componentName}".`);
          }

          const componentPath = importMatch[1];
          const path = componentPath.replace('@', 'src');
          const fileContent = await readZipTextFileBySuffix('example.zip', path);
          if (fileContent?.includes(dto.oldValue)) {
            const updatedFileContent = fileContent.replaceAll(dto.oldValue, dto.newValue);
            const updatedFile = {
              path,
              content: updatedFileContent,
            };
            updatedProject = {
              files: [...last.files, updatedFile],
            };
            return updatedProject;
          } else {
            const allPaths = await listZipFilePaths('example.zip');
            const updatedFiles = [];
            for (const p of allPaths) {
              if (p === 'src/app/page.tsx' || p === path) continue;
              const cont = await readZipTextFileBySuffix('example.zip', p);
              if (cont?.includes(dto.oldValue)) {
                const upd = cont.replaceAll(dto.oldValue, dto.newValue);
                updatedFiles.push({ path: p, content: upd });
              }
            }
            if (updatedFiles.length > 0) {
              updatedProject = {
                files: [...last.files, ...updatedFiles],
              };
            } else {
              throw new Error('oldValue not found anywhere in the project.');
            }
            return updatedProject;
          }
        }
      } else {
        throw new Error('sectionId is required to target the replacement.');
      }
    } catch (e) {
      console.error('Error while editing message', e);
      throw e;
    }
  }
}

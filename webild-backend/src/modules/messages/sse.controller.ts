import { Controller, Get, Res, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { SwaggerSubscribeToUserMessages } from './sse.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { Public } from '../../common/decorators/public.decorator';
import { SSEAuthGuard } from '../../common/guards/sse-auth.guard';
import { SSEService } from '../../common/services/sse/sse.service';
import { User } from '../users/users.entity';

@ApiTags('SSE')
@Controller('sse')
@UseGuards(SSEAuthGuard)
@Public()
export class SSEController {
  constructor(private readonly sseService: SSEService) {}

  @SwaggerSubscribeToUserMessages()
  @Get('messages')
  async subscribeToUserMessages(@DbAuthUser() user: User, @Res() response: Response) {
    this.sseService.addClient(user.id, response);
  }
}

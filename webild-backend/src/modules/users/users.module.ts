import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { UsersController } from './users.controller';
import { User } from './users.entity';
import { UsersService } from './users.service';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';

@Module({
  imports: [SequelizeModule.forFeature([User])],
  controllers: [UsersController],
  providers: [...clerkClientProviders, UsersService],
  exports: [UsersService],
})
export class UsersModule {}

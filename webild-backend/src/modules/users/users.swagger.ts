import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { ResponseWrapperDto } from 'src/common/dto/response-wrapper.dto';

import { GetUserDto } from './dto/get-user.dto';

export function SwaggerGetCurrentUser() {
  return applyDecorators(
    ApiExtraModels(ResponseWrapperDto, GetUserDto),
    ApiOkResponse({
      description: 'Get the current authenticated user profile.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(GetUserDto) },
            },
          },
        ],
      },
    }),
  );
}

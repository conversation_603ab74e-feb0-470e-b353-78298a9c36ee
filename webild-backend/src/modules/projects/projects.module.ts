import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import { Project } from './project.entity';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { SitesLimitInterceptor } from '../../common/interceptors/deploy-limit.interceptor';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';
import { GithubService } from '../../common/services/github/github.service';
import { SandboxService } from '../../common/services/sandbox/sandbox.service';
import { VercelService } from '../../common/services/vercel/vercel.service';
import { Message } from '../messages/message.entity';
import { MessagesModule } from '../messages/messages.module';
import { QueueModule } from '../queue/queue.module';
import { PlanLimit } from '../stripe/plan-limit.entity';
import { Subscription } from '../stripe/subscriptions.entity';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Project,
      ProjectVersion,
      ProjectUser,
      Message,
      PlanLimit,
      Subscription,
    ]),
    forwardRef(() => MessagesModule),
    forwardRef(() => QueueModule),
  ],
  exports: [ProjectsService],
  controllers: [ProjectsController],
  providers: [
    ...clerkClientProviders,
    ProjectsService,
    GithubService,
    VercelService,
    SandboxService,
    SitesLimitInterceptor,
  ],
})
export class ProjectsModule {}

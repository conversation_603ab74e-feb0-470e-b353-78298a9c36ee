import {
  <PERSON>,
  Column,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Type,
  Foreign<PERSON>ey,
} from 'sequelize-typescript';

import { Project } from './project.entity';
import { User } from '../users/users.entity';

@Table({ tableName: 'project_users', timestamps: true })
export class ProjectUser extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: string;

  @ForeignKey(() => User)
  @Column({ field: 'user_id', type: DataType.UUID })
  declare user_id: string;

  @ForeignKey(() => Project)
  @Column({ field: 'project_id', type: DataType.UUID })
  declare project_id: string;

  @Column({ allowNull: true })
  declare role: string;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;
}

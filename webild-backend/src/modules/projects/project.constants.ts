export enum ProjectVersionStatus {
  Published = 'published',
  Drafted = 'drafted',
  Executed = 'executed',
  Failed = 'failed',
  Processing = 'processing',
}

export enum ProjectUserRole {
  Owner = 'owner',
}

export const BuildFileContent =
  'name: Build\n' +
  '\n' +
  'on:\n' +
  '  workflow_dispatch:\n' +
  '    inputs:\n' +
  '      branch:\n' +
  "        description: 'Branch to build'\n" +
  '        required: true\n' +
  "        default: 'main'\n" +
  '\n' +
  'permissions:\n' +
  '  contents: read\n' +
  '\n' +
  'jobs:\n' +
  '  build:\n' +
  '    runs-on: ubuntu-latest\n' +
  '\n' +
  '    steps:\n' +
  '      - name: Checkout branch\n' +
  '        uses: actions/checkout@v4\n' +
  '        with:\n' +
  '          ref: ${{ github.event.inputs.branch }}\n' +
  '\n' +
  '      - name: Setup Node.js\n' +
  '        uses: actions/setup-node@v4\n' +
  '        with:\n' +
  '          node-version: 24\n' +
  "          cache: 'npm'\n" +
  '\n' +
  '      - name: Install dependencies\n' +
  '        run: |\n' +
  '          set -euo pipefail\n' +
  '          npm ci 2>&1 | tee install.log\n' +
  '\n' +
  '      - name: Build (next build)\n' +
  '        run: |\n' +
  '          set -euo pipefail\n' +
  '          npm run build 2>&1 | tee build.log\n' +
  '\n' +
  '      # Sanity check that Next produced the expected .next artifacts\n' +
  '      - name: Verify .next exists\n' +
  '        run: test -d .next || (echo "No .next folder. Check build logs above."; exit 1)\n' +
  '\n' +
  '      - name: Upload logs on failure\n' +
  '        if: failure()\n' +
  '        uses: actions/upload-artifact@v4\n' +
  '        with:\n' +
  '          name: build-logs\n' +
  '          path: |\n' +
  '            install.log\n' +
  '            build.log\n' +
  '          if-no-files-found: ignore\n';

export const NextConfigContent =
  '' +
  'import type { NextConfig } from "next";\n' +
  '\n' +
  'const nextConfig: NextConfig = {\n' +
  '  images: {\n' +
  '    remotePatterns: [\n' +
  '      {\n' +
  "        protocol: 'https',\n" +
  "        hostname: '*.s3.*.amazonaws.com',\n" +
  "        pathname: '/**',\n" +
  '      },\n' +
  '    ],\n' +
  '  },\n' +
  '  };\n' +
  '\n' +
  'export default nextConfig;\n';

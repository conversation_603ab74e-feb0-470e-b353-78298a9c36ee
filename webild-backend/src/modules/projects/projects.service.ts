import * as fs from 'fs';
import * as path from 'path';

import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { jsonrepair } from 'jsonrepair';

import { CreateProjectVersionDto, UpdateProjectVersionDto } from './dto/create-project-version.dto';
import { CreateProjectDto } from './dto/create-project.dto';
import { GetProjectDto } from './dto/get-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import {
  BuildFileContent,
  NextConfigContent,
  ProjectUserRole,
  ProjectVersionStatus,
} from './project.constants';
import { Project } from './project.entity';
import { GithubService, ITree } from '../../common/services/github/github.service';
import {
  GenerateCodeType,
  SystemPrompts,
  UserContentPart,
} from '../../common/services/openai/openai.service';
import { SandboxService } from '../../common/services/sandbox/sandbox.service';
import { VercelService } from '../../common/services/vercel/vercel.service';
import { isTextFile, unzipProject } from '../../common/utils/file-utils';
import { paginateCursor } from '../../common/utils/paginate-cursor.util';
import { MessageTypes } from '../messages/message.constants';
import { MessagesService } from '../messages/messages.service';
import { QueueService } from '../queue/queue.service';

export interface ManifestFile {
  path: string;
  content: string;
}
export interface ManifestBundle {
  files: ManifestFile[];
}

@Injectable()
export class ProjectsService {
  constructor(
    @InjectModel(Project) private projectModel: typeof Project,
    @InjectModel(ProjectVersion) private versionModel: typeof ProjectVersion,
    @InjectModel(ProjectUser) private projectUserModel: typeof ProjectUser,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    private readonly gitHubService: GithubService,
    private readonly queueService: QueueService,
    private readonly vercelService: VercelService,
    private readonly sandboxService: SandboxService,
  ) {}

  private readonly tempProjPath = './tmp/';

  async createProject(dto: CreateProjectDto, userId: string) {
    const repositoryUrl = 'https://github.com';

    const project = await this.projectModel.create({ ...dto, repositoryUrl }, { returning: true });

    await this.projectUserModel.create({
      user_id: userId,
      project_id: project.id,
      role: ProjectUserRole.Owner,
    });

    const repository = await this.gitHubService.createRepository(project.id);
    project.repositoryUrl = repository.html_url;
    project.repositoryOwner = repository.owner.login;
    project.repositoryName = repository.name;
    await project.save();

    const version = await this.createProjectVersion({ version_prompt: '', project_id: project.id });

    await this.messagesService.initAIDialogue(userId, project.id, version.id);

    const createdProject = await this.projectModel.findByPk(project.id, {
      include: ['activeVersion'],
    });

    if (!createdProject) {
      throw new Error('Failed to create project');
    }

    return createdProject.get({ plain: true });
  }

  async createProjectVersion(dto: CreateProjectVersionDto & { project_id: string }) {
    const project = await this.projectModel.findByPk(dto.project_id);
    if (!project) throw new Error('Project not found');

    const lastVersion = await this.versionModel.findOne({
      where: { project_id: dto.project_id },
      order: [['createdAt', 'DESC']],
    });

    const versionNumber = lastVersion ? lastVersion.version_number + 1 : 1;
    const versionBranch = `version_${versionNumber}`;

    await this.gitHubService.createBranch(
      project.repositoryOwner,
      project.repositoryName,
      versionBranch,
    );

    const version = await this.versionModel.create(
      {
        ...dto,
        version_number: versionNumber,
        version_branch: versionBranch,
      },
      { returning: true },
    );

    await this.projectModel.update(
      { activeVersionId: version.id },
      { where: { id: dto.project_id } },
    );

    return version.get({ plain: true });
  }

  async getProject(dto: GetProjectDto) {
    const project = await this.projectModel.findByPk(dto.id, {
      include: ['versions'],
    });

    if (!project) {
      throw new Error('Project not found');
    }

    return project.get({ plain: true });
  }

  async getProjectVersion(projectId: string, projectVersionId: string) {
    const version = await this.versionModel.findOne({
      where: {
        id: projectVersionId,
        project_id: projectId,
      },
    });

    if (!version) throw new Error('Project version not found');
    return version;
  }

  async getUserProjects(params: { userId: string; limit: number; cursor: string | undefined }) {
    const { userId, limit, cursor } = params;

    return await paginateCursor(Project, {
      limit,
      cursor,
      include: [
        {
          model: ProjectUser,
          where: { user_id: userId },
          attributes: [],
        },
      ],
      orderBy: 'created_at',
      orderDirection: 'ASC',
    });
  }

  async updateProject(id: string, dto: Partial<UpdateProjectDto>) {
    const project = await this.projectModel.findByPk(id);
    if (!project) {
      throw new Error('Project not found');
    }

    if (dto.activeVersionId) {
      const version = await this.versionModel.findOne({
        where: { id: dto.activeVersionId, project_id: id },
      });

      if (!version) {
        throw new Error('activeVersionId is invalid or does not belong to the project');
      }
    }

    await project.update(dto);
    return project.get({ plain: true });
  }

  async updateProjectVersion(projectId: string, versionId: string, dto: UpdateProjectVersionDto) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });

    if (!version) throw new Error('Project version not found');

    await version.update(dto);
    return version.get({ plain: true });
  }

  async updateProjectVersionStatus(
    projectId: string,
    versionId: string,
    status: ProjectVersionStatus,
  ) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });

    if (!version) throw new Error('Project version not found');

    await version.update({ version_status: status });
    return version.get({ plain: true });
  }

  async updateVersionCode(
    projectId: string,
    versionId: string,
    userId: string,
    editPrompt: string,
  ) {
    await this.queueService.enqueueProjectEditJob(editPrompt, userId, projectId, versionId);
  }

  async saveLastSuccessfulFiles(
    projectId: string,
    versionId: string,
    filesJson: string,
  ): Promise<void> {
    await this.updateProjectVersion(projectId, versionId, { last_generated_code: filesJson });
  }

  async getLastSuccessfulFiles(projectId: string, versionId: string): Promise<any> {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });

    if (!version) throw new Error('Project version not found');

    if (!version.last_generated_code)
      throw new Error('Project version does not have success generation');

    return JSON.parse(jsonrepair(version.last_generated_code));
  }

  async executeVersionPrompt(projectId: string, versionId: string, userId: string) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });
    if (!version) throw new Error('Version not found');

    const combinedPrompt = await this.messagesService.generateVersionPrompt(projectId, versionId);
    version.version_prompt = combinedPrompt;
    version.version_status = ProjectVersionStatus.Processing;
    await version.save();

    try {
      await this.queueService.enqueueProjectGenerationJob(
        combinedPrompt,
        userId,
        projectId,
        versionId,
      );
    } catch (error) {
      version.version_status = ProjectVersionStatus.Drafted;
      await version.save();
      console.error('Failed to create project files', error);
      throw new Error('Failed to create project files');
    }

    return version.get({ plain: true });
  }

  async deployProject(projectId: string) {
    const project = await this.projectModel.findByPk(projectId);

    if (!project) throw new Error('Project not found');

    const projectVersion = await this.getProjectVersion(projectId, project.activeVersionId);

    if (!projectVersion) throw new Error('Project version not found');

    const { id, sandbox_id } = project;
    if (!sandbox_id) throw new Error('Project preview not found');

    const url = await this.vercelService.promoteToProduction(id, sandbox_id);

    if (url) {
      project.publish_url = url;
      await project.save();

      projectVersion.version_status = ProjectVersionStatus.Published;
      await projectVersion.save();
    }

    return project.get({ plain: true });
  }

  createTempProjectFolder(path: string) {
    const fullPath = `${this.tempProjPath}${path}`;
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
    fs.mkdirSync(fullPath, { recursive: true });

    return fullPath;
  }

  writeParsedFile(filePath: string, content: string, root = './tmp/openai-project') {
    const fullPath = path.join(root, filePath);
    const dir = path.dirname(fullPath);

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(fullPath, content, 'utf-8');
  }

  async getProjectStructure(
    userId: string,
    projectId: string,
    versionId: string,
    plainPrompt: string,
    systemPrompts: SystemPrompts[],
    userPrompt?: UserContentPart[],
  ) {
    const generatedMessage = await this.messagesService.askNStoreAI(
      userId,
      projectId,
      versionId,
      {
        message: plainPrompt,
        type: MessageTypes.User,
      },
      systemPrompts,
      false,
      GenerateCodeType.CODE_GENERATE,
      userPrompt,
      true,
    );

    return this.safeParseManifest(generatedMessage.message);
  }

  async createProjectFromFiles(
    filesPaths: ManifestFile[],
    projectId: string,
    initialRun: boolean = true,
  ) {
    const projectFolder = this.createTempProjectFolder(projectId);
    const projectPath = `${projectFolder}/main`;
    if (initialRun) {
      await unzipProject('example.zip', projectFolder);
    }

    for (let i = 0; i < filesPaths.length; i++) {
      const { path, content } = filesPaths[i];

      this.writeParsedFile(path, content, projectPath);
    }
    if (initialRun) {
      this.writeParsedFile('.github/workflows/build.yml', BuildFileContent, projectPath);
      this.writeParsedFile('next.config.ts', NextConfigContent, projectPath);
    }
  }

  async pushProjectToGitHub(projectId: string, versionId: string) {
    const project = await this.projectModel.findByPk(projectId);
    if (!project) throw new Error('Project not found');
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });
    if (!version) throw new Error('Project version not found');

    const { repositoryOwner, repositoryName, id } = project;
    const branch = version.version_branch;
    const projectPath = `${this.tempProjPath}${id}/main`;

    const tree: ITree[] = [];
    const maxFileSize = 100 * 1024 * 1024;
    const walk = async (dir: string, prefix: string = ''): Promise<void> => {
      const entries = await fs.promises.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.join(prefix, entry.name).replace(/\\/g, '/');
        if (entry.isDirectory()) {
          await walk(fullPath, relativePath);
        } else {
          const stats = await fs.promises.stat(fullPath);
          if (stats.size > maxFileSize) {
            throw new Error(`File ${relativePath} exceeds GitHub's 100 MB limit`);
          }
          const content = await fs.promises.readFile(fullPath);
          const isText = isTextFile(fullPath);
          if (isText) {
            tree.push({
              path: relativePath,
              mode: '100644',
              type: 'blob',
              content: content.toString('utf-8'),
            });
          } else {
            const base64Content = content.toString('base64');
            const sha = await this.gitHubService.createBlob(
              repositoryOwner,
              repositoryName,
              base64Content,
              relativePath,
            );

            tree.push({
              path: relativePath,
              mode: '100644',
              type: 'blob',
              sha,
            });
          }
        }
      }
    };

    try {
      await walk(projectPath);
    } catch (error) {
      console.error('Failed to process files:', error);
      throw new Error(`Failed to process files`);
    }

    await this.gitHubService.pushProjectTree(repositoryOwner, repositoryName, branch, tree);

    fs.rmSync(projectPath, { recursive: true, force: true });
    return {
      repositoryOwner,
      repositoryName,
      branch,
    };
  }

  safeParseManifest(input: string): ManifestBundle {
    try {
      const repaired = jsonrepair(input);
      const parsed = JSON.parse(repaired);
      if (!parsed || !Array.isArray(parsed.files)) throw new Error('Missing files[]');
      for (const f of parsed.files) {
        if (!f || typeof f.path !== 'string' || typeof f.content !== 'string') {
          throw new Error('Bad file entry');
        }
      }
      return parsed as ManifestBundle;
    } catch (e) {
      throw new Error(`Invalid manifest JSON: ${(e as Error).message}`);
    }
  }

  async getProjectDomainsConfig(projectId: string) {
    try {
      const project = await this.projectModel.findByPk(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const vercelProjectId = project.repositoryName;
      if (!vercelProjectId) {
        throw new Error('Project is not connected to Vercel');
      }

      const domains = await this.vercelService.getProjectDomains(vercelProjectId);

      const domainsWithConfig = await Promise.all(
        domains.map(async (domain) => {
          try {
            const config = await this.vercelService.getProjectDomainConfig(
              vercelProjectId,
              domain.name,
            );

            const filteredIPv4 = (config.recommendedIPv4 || []).filter(
              (item: any) =>
                item &&
                typeof item === 'object' &&
                Object.keys(item).length > 0 &&
                item.value &&
                item.value.length > 0,
            );
            const filteredCNAME = (config.recommendedCNAME || []).filter(
              (item: any) =>
                item && typeof item === 'object' && Object.keys(item).length > 0 && item.value,
            );

            return {
              domain: {
                name: domain.name,
                apexName: domain.apexName,
                projectId: domain.projectId,
                redirect: domain.redirect,
                redirectStatusCode: domain.redirectStatusCode,
                gitBranch: domain.gitBranch,
                customEnvironmentId: domain.customEnvironmentId,
                updatedAt: domain.updatedAt,
                createdAt: domain.createdAt,
                verified: domain.verified,
                verification: domain.verification,
              },
              config: {
                configuredBy: config.configuredBy,
                acceptedChallenges: config.acceptedChallenges || [],
                recommendedIPv4: filteredIPv4,
                recommendedCNAME: filteredCNAME,
                misconfigured: config.misconfigured || false,
              },
            };
          } catch (error) {
            console.warn(`Failed to get config for domain ${domain.name}:`, error);
            return null;
          }
        }),
      );

      return {
        projectId: project.id,
        projectName: project.name,
        domains: domainsWithConfig,
      };
    } catch (error) {
      throw new Error(
        `Failed to get project domains config: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async addDomainToProject(projectId: string, domain: string) {
    try {
      const project = await this.projectModel.findByPk(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const vercelProjectId = project.repositoryName;
      if (!vercelProjectId) {
        throw new Error('Project is not connected to Vercel');
      }

      const addedDomain = await this.vercelService.addProjectDomain(vercelProjectId, domain);

      return {
        success: true,
        message: `Domain ${domain} added to project successfully`,
        domain: {
          name: addedDomain.name,
          apexName: addedDomain.apexName,
          projectId: addedDomain.projectId,
          redirect: addedDomain.redirect,
          redirectStatusCode: addedDomain.redirectStatusCode,
          gitBranch: addedDomain.gitBranch,
          customEnvironmentId: addedDomain.customEnvironmentId,
          updatedAt: addedDomain.updatedAt,
          createdAt: addedDomain.createdAt,
          verified: addedDomain.verified,
          verification: addedDomain.verification,
        },
      };
    } catch (error) {
      throw new Error(
        `Failed to add domain to project: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async removeDomainFromProject(projectId: string, domain: string) {
    try {
      const project = await this.projectModel.findByPk(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const vercelProjectId = project.repositoryName;
      if (!vercelProjectId) {
        throw new Error('Project is not connected to Vercel');
      }

      await this.vercelService.removeProjectDomain(vercelProjectId, domain);

      return {
        success: true,
        message: `Domain ${domain} removed from project successfully`,
      };
    } catch (error) {
      throw new Error(
        `Failed to remove domain from project: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }
}

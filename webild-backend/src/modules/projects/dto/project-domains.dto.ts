import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class VerificationDto {
  @ApiProperty()
  type!: string;

  @ApiProperty()
  domain!: string;

  @ApiProperty()
  value!: string;

  @ApiProperty()
  reason!: string;
}

export class RecommendedIPv4Dto {
  @ApiProperty()
  rank!: number;

  @ApiProperty({ type: [String] })
  value!: string[];
}

export class RecommendedCNAMEDto {
  @ApiProperty()
  rank!: number;

  @ApiProperty()
  value!: string;
}

export class DnsRecordDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  name!: string;

  @ApiProperty()
  type!: string;

  @ApiProperty()
  content!: string;

  @ApiPropertyOptional()
  ttl?: number;

  @ApiPropertyOptional()
  priority?: number;

  @ApiProperty()
  createdAt!: string;

  @ApiProperty()
  updatedAt!: string;
}

export class VercelDomainDto {
  @ApiProperty()
  name!: string;

  @ApiProperty()
  apexName!: string;

  @ApiProperty()
  projectId!: string;

  @ApiPropertyOptional()
  redirect?: string;

  @ApiPropertyOptional()
  redirectStatusCode?: number;

  @ApiPropertyOptional()
  gitBranch?: string;

  @ApiPropertyOptional()
  customEnvironmentId?: string;

  @ApiProperty()
  updatedAt!: number;

  @ApiProperty()
  createdAt!: number;

  @ApiProperty()
  verified!: boolean;

  @ApiPropertyOptional({ type: [VerificationDto] })
  verification?: VerificationDto[];
}

export class DomainConfigDto {
  @ApiPropertyOptional()
  configuredBy?: string | null;

  @ApiPropertyOptional({ type: [String] })
  acceptedChallenges?: string[];

  @ApiPropertyOptional({ type: [RecommendedIPv4Dto] })
  recommendedIPv4?: RecommendedIPv4Dto[];

  @ApiPropertyOptional({ type: [RecommendedCNAMEDto] })
  recommendedCNAME?: RecommendedCNAMEDto[];

  @ApiProperty()
  misconfigured!: boolean;
}

export class DomainWithConfigDto {
  @ApiProperty()
  domain!: VercelDomainDto;

  @ApiProperty()
  config!: DomainConfigDto;
}

export class ProjectDomainsResponseDto {
  @ApiProperty()
  projectId!: string;

  @ApiProperty()
  projectName!: string;

  @ApiProperty({ type: [DomainWithConfigDto] })
  domains!: DomainWithConfigDto[];
}

export class AddDomainRequestDto {
  @ApiProperty({ description: 'Domain name to add to the project' })
  domain!: string;
}

export class AddDomainResponseDto {
  @ApiProperty()
  success!: boolean;

  @ApiProperty()
  message!: string;

  @ApiPropertyOptional()
  domain?: VercelDomainDto;
}

export class RemoveDomainRequestDto {
  @ApiProperty({ description: 'Domain name to remove from the project' })
  domain!: string;
}

export class RemoveDomainResponseDto {
  @ApiProperty()
  success!: boolean;

  @ApiProperty()
  message!: string;
}

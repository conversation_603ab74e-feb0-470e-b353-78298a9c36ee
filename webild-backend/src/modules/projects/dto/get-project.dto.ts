import { ApiProperty } from '@nestjs/swagger';

import { ProjectVersion } from '../project-version.entity';
import { CreateProjectVersionDtoResponse } from './create-project-version.dto';

export class GetProjectDto {
  @ApiProperty()
  declare id: string;
}

export class GetProjectDtoResponse {
  @ApiProperty()
  declare id: string;

  @ApiProperty()
  declare name: string;

  @ApiProperty()
  declare repositoryUrl: string;

  @ApiProperty()
  declare createdAt: Date;

  @ApiProperty()
  declare updatedAt: Date;

  @ApiProperty({ type: [CreateProjectVersionDtoResponse] })
  declare versions: ProjectVersion[];
}

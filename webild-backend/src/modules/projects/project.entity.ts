import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  HasMany,
  BelongsToMany,
  BelongsTo,
} from 'sequelize-typescript';

import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import { User } from '../users/users.entity';

@Table({ tableName: 'projects', timestamps: true })
export class Project extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: string;

  @Column({ allowNull: false })
  declare name: string;

  @Column({ field: 'repository_url', allowNull: false })
  declare repositoryUrl: string;

  @Column({ field: 'repository_owner', allowNull: true })
  declare repositoryOwner: string;

  @Column({ field: 'repository_name', allowNull: true })
  declare repositoryName: string;

  @Column({ field: 'active_version_id', allowNull: true })
  declare activeVersionId: string;

  @Column({ allowNull: true, type: DataType.TEXT('long') })
  declare publish_url?: string;

  @Column({ allowNull: true, type: DataType.TEXT('long') })
  declare preview_url?: string;

  @Column({ allowNull: true, type: DataType.TEXT('long') })
  declare sandbox_id?: string;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;

  @HasMany(() => ProjectVersion, 'project_id')
  declare versions?: ProjectVersion[];

  @BelongsToMany(() => User, () => ProjectUser)
  declare users?: User[];

  @HasMany(() => ProjectUser, 'project_id')
  declare projectUsers?: ProjectUser[];

  @BelongsTo(() => ProjectVersion, 'activeVersionId')
  declare activeVersion?: ProjectVersion;
}

// src/modules/stripe/usage-counters.entity.ts
import { CreationOptional, InferAttributes, InferCreationAttributes } from 'sequelize';
import {
  Table,
  Model,
  Column,
  DataType,
  PrimaryKey,
  AutoIncrement,
  AllowNull,
  Default,
} from 'sequelize-typescript';

@Table({
  tableName: 'usage_counters',
  timestamps: true,
  underscored: true,
})
export class UsageCounter extends Model<
  InferAttributes<UsageCounter>,
  InferCreationAttributes<UsageCounter>
> {
  @PrimaryKey
  @AutoIncrement
  @Column({ type: DataType.BIGINT.UNSIGNED })
  declare id: CreationOptional<number>;

  @AllowNull(false)
  @Column({ field: 'userId', type: DataType.UUID })
  declare userId: string;

  @AllowNull(false)
  @Default('FREE')
  @Column({ field: 'price_id', type: DataType.STRING(64) })
  declare priceId: string;

  @AllowNull(false)
  @Column({ field: 'period_start', type: DataType.DATE })
  declare periodStart: Date;

  @AllowNull(false)
  @Column({ field: 'period_end', type: DataType.DATE })
  declare periodEnd: Date;

  @AllowNull(false)
  @Default(0)
  @Column({ field: 'used', type: DataType.INTEGER.UNSIGNED })
  declare used: number;

  declare readonly createdAt: CreationOptional<Date>;
  declare readonly updatedAt: CreationOptional<Date>;
}

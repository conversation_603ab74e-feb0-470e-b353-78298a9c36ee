import { ApiProperty } from '@nestjs/swagger';

export enum PlanCode {
  MONTH = 'month',
  YEAR = 'year',
}

export class CreateCheckoutDto {
  @ApiProperty({
    description: 'Subscription plan',
    enum: PlanCode,
    enumName: 'PlanCode',
    example: PlanCode.MONTH,
    required: true,
  })
  plan!: PlanCode;

  @ApiProperty({
    description: 'Project ID',
  })
  projectId?: string;
}

export class CheckoutResponseDto {
  @ApiProperty({
    description: 'Stripe Checkout URL',
    format: 'uri',
    example: 'https://checkout.stripe.com/c/session/abc123',
  })
  url!: string;
}

// src/modules/stripe/plan-limit.entity.ts
import { CreationOptional, InferAttributes, InferCreationAttributes } from 'sequelize';
import {
  Table,
  Model,
  Column,
  DataType,
  PrimaryKey,
  Default,
  Unique,
  AllowNull,
} from 'sequelize-typescript';

@Table({
  tableName: 'plan_limits',
  timestamps: true,
  underscored: true,
})
export class PlanLimit extends Model<
  InferAttributes<PlanLimit>,
  InferCreationAttributes<PlanLimit>
> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: CreationOptional<string>;

  @Unique('uniq_plan_limits_price')
  @AllowNull(false)
  @Column({ field: 'price_id', type: DataType.STRING(64) })
  declare priceId: string;

  @AllowNull(false)
  @Column({ field: 'name', type: DataType.STRING })
  declare name: string;

  @AllowNull(false)
  @Default(0)
  @Column({ field: 'requests_per_period', type: DataType.INTEGER.UNSIGNED })
  declare requestsPerPeriod: number;

  @AllowNull(false)
  @Default(0)
  @Column({ field: 'sites_concurrent_limit', type: DataType.INTEGER.UNSIGNED })
  declare sitesConcurrentLimit: number;

  declare readonly createdAt: CreationOptional<Date>;
  declare readonly updatedAt: CreationOptional<Date>;
}

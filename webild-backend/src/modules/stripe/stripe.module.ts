import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';

import { PlanLimit } from './plan-limit.entity';
import { StripeCustomer } from './stripe-customer.entity';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { Subscription } from './subscriptions.entity';
import { UsageCounter } from './usage-counters.entity';
import { ProjectUser } from '../projects/project-user.entity';
import { ProjectVersion } from '../projects/project-version.entity';
import { Project } from '../projects/project.entity';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    SequelizeModule.forFeature([
      StripeCustomer,
      Subscription,
      UsageCounter,
      Project,
      ProjectUser,
      ProjectVersion,
      PlanLimit,
    ]),
  ],
  controllers: [StripeController],
  providers: [StripeService],
})
export class StripeModule {}

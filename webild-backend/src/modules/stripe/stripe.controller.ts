import { BadRequestException, Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Public } from 'src/common/decorators/public.decorator';
import { successResponse, errorResponse } from 'src/common/utils/response.util';

import { CreateCheckoutDto, PlanCode } from './dto/create-checkout.dto';
import { StripeService } from './stripe.service';
import { SwaggerCreateCheckout, SwaggerGetPlans } from './stripe.swagger';

@ApiTags('stripe')
@Controller('stripe')
export class StripeController {
  constructor(private readonly stripe: StripeService) {}

  @Post('checkout-session')
  @ApiBearerAuth('clerk-auth')
  @ApiOperation({ summary: 'Create Stripe Checkout Session for subscription' })
  @SwaggerCreateCheckout()
  async createCheckout(@Body() { plan, projectId }: CreateCheckoutDto, @Req() req: Request) {
    if (!plan) {
      throw new BadRequestException({ message: 'Missing required body parameter: "plan"' });
    }
    const allowed = Object.values(PlanCode);
    if (!allowed.includes(plan)) {
      throw new BadRequestException({ message: `Invalid "plan". Allowed: ${allowed.join(' | ')}` });
    }
    const userId = req.dbUser.dataValues.id;

    try {
      const url = await this.stripe.createCheckoutSession({ userId, plan, projectId });
      return successResponse({ data: { url } }, 201);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @Get('plans')
  @ApiBearerAuth('clerk-auth')
  @ApiOperation({ summary: 'get all plans list' })
  @SwaggerGetPlans()
  async listPlans() {
    const plans = await this.stripe.listAllPlans();
    return successResponse({ data: { plans } }, 200);
  }

  @Post('webhook')
  @Public()
  @ApiOperation({ summary: 'Stripe webhook handler' })
  async webhook(@Req() req: Request) {
    const rawBody = req.rawBody;
    const sig = req.headers['stripe-signature'];
    if (!rawBody || !Buffer.isBuffer(rawBody)) {
      throw new BadRequestException('Missing raw body');
    }
    if (!sig) {
      throw new BadRequestException('Missing Stripe-Signature header');
    }

    await this.stripe.handleWebhook({
      rawBody: rawBody,
      sigHeader: req.headers['stripe-signature'] as string,
    });

    return { received: true };
  }
}

import { applyDecorators } from '@nestjs/common';
import { ApiCreatedResponse, ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

import { CheckoutResponseDto } from './dto/create-checkout.dto';
import { PlanLimit } from './plan-limit.entity';
import { ResponseWrapperDto } from '../../common/dto/response-wrapper.dto';
import { Project } from '../projects/project.entity';

export function SwaggerCreateCheckout() {
  return applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CheckoutResponseDto),
    ApiCreatedResponse({
      description: 'Creates a Stripe Checkout Session and returns a redirect URL.',
      content: {
        'application/json': {
          schema: {
            allOf: [
              { $ref: getSchemaPath(ResponseWrapperDto) },
              {
                type: 'object',
                properties: {
                  data: { $ref: getSchemaPath(CheckoutResponseDto) },
                },
                required: ['data'],
              },
            ],
          },
          examples: {
            success: {
              summary: 'Checkout URL',
              value: {
                code: 0,
                message: 'Checkout session created',
                data: { url: 'https://checkout.stripe.com/c/session/abc123' },
              },
            },
          },
        },
      },
    }),
  );
}

export const SwaggerGetPlans = () =>
  applyDecorators(
    ApiExtraModels(PlanLimit),
    ApiOkResponse({
      description: 'Returns a list of plans.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(Project) },
              },
            },
          },
        ],
      },
    }),
  );

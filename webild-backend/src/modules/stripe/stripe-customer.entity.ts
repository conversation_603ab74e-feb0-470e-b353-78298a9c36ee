import { CreationOptional, InferAttributes, InferCreationAttributes } from 'sequelize';
import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  Unique,
  BelongsTo,
  ForeignKey,
  AllowNull,
} from 'sequelize-typescript';

import { User } from '../users/users.entity';

@Table({
  tableName: 'stripe_customers',
  timestamps: true,
  underscored: true,
})
export class Stripe<PERSON>ustomer extends Model<
  InferAttributes<StripeCustomer>,
  InferCreationAttributes<StripeCustomer>
> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: CreationOptional<string>;

  @AllowNull(false)
  @ForeignKey(() => User)
  @Unique('uniq_stripe_customers_user')
  @Column({ field: 'userId', type: DataType.UUID })
  declare userId: string;

  @BelongsTo(() => User, { foreignKey: 'userId', targetKey: 'id', onDelete: 'CASCADE' })
  declare user?: CreationOptional<User>;

  @AllowNull(false)
  @Unique('uniq_stripe_customer_id')
  @Column({ field: 'customerId', type: DataType.STRING(64) })
  declare customerId: string;
}

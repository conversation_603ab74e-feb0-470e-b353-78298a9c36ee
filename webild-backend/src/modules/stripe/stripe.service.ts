import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import Stripe from 'stripe';

import { PlanLimit } from './plan-limit.entity';
import { StripeCustomer } from './stripe-customer.entity';
import { Subscription } from './subscriptions.entity';
import { UsageCounter } from './usage-counters.entity';

type PlanCode = 'month' | 'year';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: Stripe;
  private readonly priceByPlan: Record<'month' | 'year', string>;

  constructor(
    private readonly cfg: ConfigService,
    @InjectModel(StripeCustomer) private readonly stripeCustomers: typeof StripeCustomer,
    @InjectModel(Subscription) private readonly subscriptions: typeof Subscription,
    @InjectModel(UsageCounter) private readonly counters: typeof UsageCounter,
  ) {
    this.stripe = new Stripe(this.cfg.get<string>('STRIPE_SECRET_KEY')!);
    this.priceByPlan = {
      month: this.cfg.get<string>('STRIPE_PRICE_MONTH')!,
      year: this.cfg.get<string>('STRIPE_PRICE_YEAR')!,
    };
  }

  private async getOrCreateCustomer(userId: number | string): Promise<string> {
    const uid = String(userId);

    const existing = await this.stripeCustomers.findOne({ where: { userId: uid } });
    if (existing) return existing.customerId;

    const customer = await this.stripe.customers.create({
      name: uid,
      metadata: { app_user_id: uid },
    });

    try {
      await this.stripeCustomers.create({ userId: uid, customerId: customer.id });
    } catch (err) {
      if (err instanceof UniqueConstraintError) {
        const again = await this.stripeCustomers.findOne({ where: { userId: uid } });
        if (again) return again.customerId;
      }
      throw err;
    }

    return customer.id;
  }

  async createCheckoutSession(params: { userId: string; plan: PlanCode; projectId?: string }) {
    const customerId = await this.getOrCreateCustomer(params.userId);
    const successUrl = `${this.cfg.get<string>('STRIPE_SUCCESS_URL')!}${
      params.projectId
        ? 'projects/' + params.projectId + '?session_id={CHECKOUT_SESSION_ID}'
        : 'home' + '?session_id={CHECKOUT_SESSION_ID}'
    }`;

    const session = await this.stripe.checkout.sessions.create({
      mode: 'subscription',
      customer: customerId,
      line_items: [{ price: this.priceByPlan[params.plan], quantity: 1 }],
      success_url: successUrl,
      cancel_url: this.cfg.get<string>('STRIPE_CANCEL_URL')!,
      allow_promotion_codes: true,
      metadata: {
        app_user_id: String(params.userId),
        plan_code: params.plan,
      },
    });

    return session.url!;
  }

  async handleWebhook(args: { rawBody: Buffer; sigHeader: string }) {
    const secret = this.cfg.get<string>('STRIPE_WEBHOOK_SECRET')!;
    const event = this.stripe.webhooks.constructEvent(args.rawBody, args.sigHeader, secret);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;

        const customerId =
          typeof session.customer === 'string' ? session.customer : (session.customer?.id ?? null);
        const userIdFromMeta = session.metadata?.app_user_id ?? null;

        if (customerId && userIdFromMeta) {
          await this.ensureStripeCustomerLink(String(userIdFromMeta), customerId);
        }

        if (typeof session.subscription === 'string' && session.subscription) {
          const sub = await this.stripe.subscriptions.retrieve(session.subscription);
          await this.upsertSubscriptionFromStripe(sub);
        }
        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted': {
        const sub = event.data.object;
        await this.upsertSubscriptionFromStripe(sub);
        break;
      }
      case 'customer.deleted': {
        const customer = event.data.object as Stripe.Customer | Stripe.DeletedCustomer;
        const customerId = customer.id;

        await this.subscriptions.update(
          {
            status: 'canceled' as Subscription['status'],
            canceledAt: new Date(),
            cancelAtPeriodEnd: false,
            customerId: null,
          },
          { where: { customerId } },
        );

        try {
          await this.stripeCustomers.destroy({ where: { customerId } });
        } catch {
          this.logger.debug(
            `Could not delete stripe_customers for ${customerId} (likely FK constraint).`,
          );
        }

        break;
      }

      default:
        this.logger.debug(`Unhandled Stripe event: ${event.type}`);
        break;
    }
  }

  async listAllPlans() {
    return await PlanLimit.findAll();
  }

  async incrementUsage(userId: string, amount: number) {
    const now = new Date();
    await this.counters.increment(
      { used: amount },
      { where: { userId: userId, periodEnd: { [Op.gt]: now } } },
    );
  }

  private async ensureStripeCustomerLink(userId: string, customerId: string) {
    const existingByUser = await this.stripeCustomers.findOne({ where: { userId } });
    if (existingByUser) return;

    const existingByCustomer = await this.stripeCustomers.findOne({ where: { customerId } });
    if (existingByCustomer) return;

    try {
      await this.stripeCustomers.create({ userId, customerId });
    } catch {
      /* empty */
    }
  }

  private async upsertSubscriptionFromStripe(sub: Stripe.Subscription) {
    const customerId = typeof sub.customer === 'string' ? sub.customer : sub.customer.id;

    let link = await this.stripeCustomers.findOne({ where: { customerId } });
    if (!link) {
      const cust = await this.stripe.customers.retrieve(customerId);
      if (!('deleted' in cust)) {
        const metaUserId = cust.metadata?.app_user_id;
        if (metaUserId) {
          await this.ensureStripeCustomerLink(String(metaUserId), customerId);
          link = await this.stripeCustomers.findOne({ where: { customerId } });
        }
      }
    }
    if (!link) {
      this.logger.warn(`No stripe_customers link for ${customerId}; skip subscription ${sub.id}`);
      return;
    }

    const item = sub.items.data[0];
    const priceId = item?.price?.id ?? null;
    const productId =
      typeof item?.price?.product === 'string'
        ? item.price.product
        : (item?.price?.product?.id ?? null);

    const toDate = (sec?: number | null) => (sec ? new Date(sec * 1000) : null);

    await this.subscriptions.upsert({
      userId: link.userId,
      customerId,
      subscriptionId: sub.id,
      status: sub.status as Subscription['status'],

      currentPeriodStart: toDate(item.current_period_start),
      currentPeriodEnd: toDate(item.current_period_end),

      cancelAtPeriodEnd: !!sub.cancel_at_period_end,
      cancelAt: toDate(sub.cancel_at),
      canceledAt: toDate(sub.canceled_at),

      priceId,
      productId,
    });
  }
}

// src/modules/stripe/subscription.model.ts
import { CreationOptional, InferAttributes, InferCreationAttributes } from 'sequelize';
import {
  Table,
  Model,
  Column,
  DataType,
  Unique,
  Default,
  AllowNull,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';

import { StripeCustomer } from './stripe-customer.entity';
import { User } from '../users/users.entity';

export type SubscriptionStatus =
  | 'incomplete'
  | 'trialing'
  | 'active'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'paused';

@Table({
  tableName: 'subscriptions',
  timestamps: true,
  underscored: true,
})
export class Subscription extends Model<
  InferAttributes<Subscription>,
  InferCreationAttributes<Subscription>
> {
  @Column({
    type: DataType.BIGINT.UNSIGNED,
    primaryKey: true,
    autoIncrement: true,
  })
  declare id: CreationOptional<number>;

  @AllowNull(false)
  @ForeignKey(() => User)
  @Column({
    field: 'userId',
    type: DataType.UUID,
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  declare userId: string;

  @BelongsTo(() => User)
  declare user?: CreationOptional<User>;

  @AllowNull(true)
  @ForeignKey(() => StripeCustomer)
  @Column({
    field: 'customerId',
    type: DataType.STRING(64),
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  })
  declare customerId: string | null;

  @BelongsTo(() => StripeCustomer, { foreignKey: 'customerId', targetKey: 'customerId' })
  declare stripeCustomer?: CreationOptional<StripeCustomer>;

  @Unique('uniq_subscription_id')
  @AllowNull(false)
  @Column({ field: 'subscription_id', type: DataType.STRING(64) })
  declare subscriptionId: string;

  @AllowNull(false)
  @Column({
    type: DataType.ENUM(
      'incomplete',
      'trialing',
      'active',
      'past_due',
      'canceled',
      'unpaid',
      'paused',
    ),
  })
  declare status: SubscriptionStatus;

  @AllowNull(true)
  @Column({ field: 'current_period_start', type: DataType.DATE })
  declare currentPeriodStart: Date | null;

  @AllowNull(true)
  @Column({ field: 'current_period_end', type: DataType.DATE })
  declare currentPeriodEnd: Date | null;

  @Default(false)
  @AllowNull(false)
  @Column({ field: 'cancel_at_period_end', type: DataType.BOOLEAN })
  declare cancelAtPeriodEnd: boolean;

  @AllowNull(true)
  @Column({ field: 'cancel_at', type: DataType.DATE })
  declare cancelAt: Date | null;

  @AllowNull(true)
  @Column({ field: 'canceled_at', type: DataType.DATE })
  declare canceledAt: Date | null;

  @AllowNull(true)
  @Column({ field: 'price_id', type: DataType.STRING(64) })
  declare priceId: string | null;

  @AllowNull(true)
  @Column({ field: 'product_id', type: DataType.STRING(64) })
  declare productId: string | null;

  declare readonly createdAt: CreationOptional<Date>;
  declare readonly updatedAt: CreationOptional<Date>;
}

import { InjectQueue } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bullmq';

import {
  PROJECT_EDIT_PROCESS,
  PROJECT_EDIT_QUEUE,
  PROJECT_FILES_PROCESS,
  PROJECT_FILES_QUEUE,
} from './queue.constants';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue(PROJECT_FILES_QUEUE) private readonly filesQueue: Queue,
    @InjectQueue(PROJECT_EDIT_QUEUE) private readonly editQueue: Queue,
  ) {}

  async enqueueProjectGenerationJob(
    prompt: string,
    userId: string,
    projectId: string,
    versionId: string,
  ): Promise<void> {
    await this.filesQueue.add(PROJECT_FILES_PROCESS, {
      prompt,
      userId,
      projectId,
      versionId,
    });
  }

  async enqueueProjectEditJob(
    editPrompt: string,
    userId: string,
    projectId: string,
    versionId: string,
  ): Promise<void> {
    await this.editQueue.add(PROJECT_EDIT_PROCESS, { editPrompt, userId, projectId, versionId });
  }
}

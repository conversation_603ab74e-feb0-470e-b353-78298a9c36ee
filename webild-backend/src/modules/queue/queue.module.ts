import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullModule } from '@nestjs/bullmq';
import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { EditProjectProcessor } from './processors/edit-project.processor';
import { PROJECT_EDIT_QUEUE, PROJECT_FILES_QUEUE, TRANSCRIBE_QUEUE } from './queue.constants';
import { QueueService } from './queue.service';
import { GithubService } from '../../common/services/github/github.service';
import { MessagesModule } from '../messages/messages.module';
import { ProjectsModule } from '../projects/projects.module';
import { GenerateProjectProcessor } from './processors/generate-project.processor';
import { VercelService } from '../../common/services/vercel/vercel.service';
import { StripeCustomer } from '../stripe/stripe-customer.entity';
import { StripeModule } from '../stripe/stripe.module';
import { StripeService } from '../stripe/stripe.service';
import { Subscription } from '../stripe/subscriptions.entity';
import { UsageCounter } from '../stripe/usage-counters.entity';

@Module({
  imports: [
    BullModule.registerQueue({
      name: PROJECT_FILES_QUEUE,
    }),
    BullModule.registerQueue({
      name: PROJECT_EDIT_QUEUE,
    }),
    BullModule.registerQueue({
      name: TRANSCRIBE_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        removeOnComplete: 50,
        removeOnFail: 10,
      },
    }),
    BullBoardModule.forFeature({
      name: PROJECT_FILES_QUEUE,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: PROJECT_EDIT_QUEUE,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: TRANSCRIBE_QUEUE,
      adapter: BullMQAdapter,
    }),
    SequelizeModule.forFeature([StripeCustomer, Subscription, UsageCounter]),
    forwardRef(() => MessagesModule),
    forwardRef(() => ProjectsModule),
    forwardRef(() => StripeModule),
  ],
  providers: [
    QueueService,
    GenerateProjectProcessor,
    EditProjectProcessor,
    GithubService,
    VercelService,
    StripeService,
  ],
  exports: [QueueService, BullModule],
})
export class QueueModule {}

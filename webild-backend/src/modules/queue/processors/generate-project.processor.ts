import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { jsonrepair } from 'jsonrepair';

import { GithubService } from '../../../common/services/github/github.service';
import { SystemPrompts, UserContentPart } from '../../../common/services/openai/openai.service';
import { VercelService } from '../../../common/services/vercel/vercel.service';
import { buildLockedSelection } from '../../../common/utils/executor-gasket';
import {
  buildImageManifestFromZip,
  readManyBySuffixes,
  readZipTextFileBySuffix,
} from '../../../common/utils/file-utils';
import { makeFixUserContent } from '../../../common/utils/get-fix-prompt';
import { ProjectVersionStatus } from '../../projects/project.constants';
import { ProjectsService } from '../../projects/projects.service';
import { StripeService } from '../../stripe/stripe.service';
import { PROJECT_FILES_QUEUE } from '../queue.constants';

interface GenerateProjectFilesPayload {
  prompt: string;
  userId: string;
  projectId: string;
  versionId: string;
}

@Processor(PROJECT_FILES_QUEUE)
@Injectable()
export class GenerateProjectProcessor extends WorkerHost {
  constructor(
    private readonly projectsService: ProjectsService,
    private readonly gitHubService: GithubService,
    private readonly vercelService: VercelService,
    private readonly stripeService: StripeService,
  ) {
    super();
  }

  override async process(job: Job<GenerateProjectFilesPayload>): Promise<void> {
    const { prompt, userId, projectId, versionId } = job.data;

    type BuildResult = { success: true } | { success: false; error?: string };

    const runCiPipeline = async (
      owner: string,
      repo: string,
      branch: string,
    ): Promise<BuildResult> => {
      await this.gitHubService.ensureWorkflowIsIndexed(owner, repo);
      await this.gitHubService.triggerWorkflowDispatch(owner, repo, branch);
      return this.gitHubService.waitForWorkflowCompletion(owner, repo, branch);
    };

    const pushAll = async (owner: string, repo: string, branch: string) => {
      await this.projectsService.pushProjectToGitHub(projectId, versionId);
      await sleep(2000);
      await this.gitHubService.forcePushBranchToMain(owner, repo, branch);
    };

    const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

    const onSuccess = async (
      projectId: string,
      owner: string,
      repo: string,
      branch: string,
      filesJson: any,
    ) => {
      await this.projectsService.saveLastSuccessfulFiles(
        projectId,
        versionId,
        JSON.stringify(filesJson),
      );

      const resp = await this.vercelService.createAndCheckDeployment(
        projectId,
        repo,
        branch,
        owner,
      );

      if (resp) {
        await this.projectsService.updateProject(projectId, {
          preview_url: resp.url,
          sandbox_id: resp.id,
        });
      }
      await this.projectsService.updateProjectVersionStatus(
        projectId,
        versionId,
        ProjectVersionStatus.Executed,
      );
    };

    try {
      const files = await readManyBySuffixes('example.zip', [
        'src/app/page.tsx',
        'src/app/layout.tsx',
        'src/app/globals.css',
        'next.config.ts',
        'package.json',
      ]);

      const registryJson = await readZipTextFileBySuffix('example.zip', 'registry.json');
      if (!registryJson) throw new Error('registry.json not found');

      const registry = JSON.parse(jsonrepair(registryJson));
      const plan = JSON.parse(jsonrepair(prompt));

      const locked = buildLockedSelection(plan, registry);

      const imageManifest = await buildImageManifestFromZip('example.zip');

      const userPrompt: UserContentPart[] = [
        {
          type: 'input_text',
          text: `LOCKED_SELECTION JSON:\n${JSON.stringify(locked)}`,
        },
        { type: 'input_text', text: `COMPONENT REGISTRY JSON:\n${registryJson}` },
        { type: 'input_text', text: `PLANNER PLAN JSON:\n${prompt}` },
        {
          type: 'input_text',
          text: `CURRENT src/app/page.tsx:\n${files['src/app/page.tsx'] ?? ''}`,
        },
        {
          type: 'input_text',
          text: `CURRENT src/app/layout.tsx:\n${files['src/app/layout.tsx'] ?? ''}`,
        },
        {
          type: 'input_text',
          text: `CURRENT src/app/globals.css:\n${files['src/app/globals.css'] ?? ''}`,
        },
        { type: 'input_text', text: `CURRENT next.config.ts:\n${files['next.config.ts']}` },
        { type: 'input_text', text: `CURRENT package.json:\n${files['package.json']}` },
        { type: 'input_text', text: `IMAGE MANIFEST JSON:\n${JSON.stringify(imageManifest)}` },
        { type: 'input_text', text: `Generate code strictly per the Executor rules.` },
      ];

      const plainPrompt = `Review attached components. Update the project and generate code according to these instructions: ${prompt}`;

      const initialFiles = await this.projectsService.getProjectStructure(
        userId,
        projectId,
        versionId,
        plainPrompt,
        [SystemPrompts.GENERATE_MANIFEST],
        userPrompt,
      );

      await this.stripeService.incrementUsage(userId, JSON.stringify(initialFiles).length);

      await this.projectsService.createProjectFromFiles(initialFiles.files, projectId);

      const { repositoryOwner, repositoryName, branch } =
        await this.projectsService.pushProjectToGitHub(projectId, versionId);
      await sleep(2000);
      await this.gitHubService.forcePushBranchToMain(repositoryOwner, repositoryName, branch);

      const result = await runCiPipeline(repositoryOwner, repositoryName, branch);
      if (result.success) {
        await onSuccess(projectId, repositoryOwner, repositoryName, branch, initialFiles);
        console.log('SUCCESS');
        return;
      }

      const MAX_FIX_ATTEMPTS = 5;
      let attempt = 1;
      let lastError = result.error ?? 'Unknown build error';
      let previousPatch = initialFiles;

      while (attempt <= MAX_FIX_ATTEMPTS) {
        const fixUserContent: UserContentPart[] = makeFixUserContent({
          errorLogs: lastError,
          previousPatchJson: previousPatch,
          registryJson,
          lockedJson: JSON.stringify(locked),
        });
        const fixPromptForDB = `Auto-fix attempt #${attempt}. Error: ${lastError}.`;

        const filesToChange = await this.projectsService.getProjectStructure(
          userId,
          projectId,
          versionId,
          fixPromptForDB,
          [SystemPrompts.FIX_CODE_ERRORS],
          fixUserContent,
        );

        if (!filesToChange?.files?.length) {
          console.warn('No files suggested for change. Stopping auto-fix loop.');
          break;
        }

        await this.projectsService.createProjectFromFiles(filesToChange.files, projectId, false);
        previousPatch = filesToChange;

        await pushAll(repositoryOwner, repositoryName, branch);

        await sleep(1500 + attempt * 250);

        const next = await runCiPipeline(repositoryOwner, repositoryName, branch);
        if (next.success) {
          await onSuccess(projectId, repositoryOwner, repositoryName, branch, previousPatch);
          console.log('SUCCESS');
          return;
        }

        lastError = next.error ?? lastError;
        console.log(`Attempt ${attempt} failed:`, lastError);
        attempt++;

        await sleep(1000 * attempt);
      }

      await this.projectsService.updateProjectVersionStatus(
        projectId,
        versionId,
        ProjectVersionStatus.Failed,
      );
    } catch (error) {
      console.error('Failed to create project files', error);
      throw new Error('Failed to create project files');
    }
  }
}

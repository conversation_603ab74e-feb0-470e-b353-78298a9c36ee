import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';

import { GithubService } from '../../../common/services/github/github.service';
import { SystemPrompts, UserContentPart } from '../../../common/services/openai/openai.service';
import { VercelService } from '../../../common/services/vercel/vercel.service';
import {
  buildLockedSelectionForEdit,
  normalizeIntentForHelpers,
} from '../../../common/utils/edit-helpers';
import { extractEditIntent } from '../../../common/utils/edit-intent';
import {
  buildImageManifestFromZip,
  readZipTextFileBySuffix,
} from '../../../common/utils/file-utils';
import { makeFixUserContent } from '../../../common/utils/get-fix-prompt';
import { ProjectVersionStatus } from '../../projects/project.constants';
import { ProjectsService } from '../../projects/projects.service';
import { StripeService } from '../../stripe/stripe.service';
import { PROJECT_EDIT_QUEUE } from '../queue.constants';

interface EditProjectPayload {
  editPrompt: string;
  userId: string;
  projectId: string;
  versionId: string;
}

@Processor(PROJECT_EDIT_QUEUE)
@Injectable()
export class EditProjectProcessor extends WorkerHost {
  constructor(
    private readonly projectsService: ProjectsService,
    private readonly gitHubService: GithubService,
    private readonly vercelService: VercelService,
    private readonly stripeService: StripeService,
  ) {
    super();
  }

  override async process(job: Job<EditProjectPayload>): Promise<void> {
    const { editPrompt, userId, projectId, versionId } = job.data;

    const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

    type BuildResult = { success: true } | { success: false; error?: string };

    const runCiPipeline = async (
      owner: string,
      repo: string,
      branch: string,
    ): Promise<BuildResult> => {
      await this.gitHubService.ensureWorkflowIsIndexed(owner, repo);
      await this.gitHubService.triggerWorkflowDispatch(owner, repo, branch);
      return this.gitHubService.waitForWorkflowCompletion(owner, repo, branch);
    };

    const pushAll = async (owner: string, repo: string, branch: string) => {
      await this.projectsService.pushProjectToGitHub(projectId, versionId);
      await sleep(1500);
      await this.gitHubService.forcePushBranchToMain(owner, repo, branch);
    };

    const onSuccess = async (owner: string, repo: string, branch: string, filesJson: any) => {
      await this.projectsService.saveLastSuccessfulFiles(
        projectId,
        versionId,
        JSON.stringify(filesJson),
      );

      const project = await this.projectsService.getProject({ id: projectId });

      const resp = await this.vercelService.createAndCheckDeployment(
        projectId,
        repo,
        branch,
        owner,
        project.sandbox_id,
      );
      if (resp) {
        await this.projectsService.updateProject(projectId, {
          preview_url: resp.url,
          sandbox_id: resp.id,
        });
      }

      await this.projectsService.updateProjectVersionStatus(
        projectId,
        versionId,
        ProjectVersionStatus.Executed,
      );
    };

    try {
      const last = await this.projectsService.getLastSuccessfulFiles(projectId, versionId);
      if (!last?.files?.length) {
        throw new Error('No previously generated files found. Generate a project first.');
      }

      const page = last.files.find((f: any) => f.path === 'src/app/page.tsx')?.content ?? '';
      const layout = last.files.find((f: any) => f.path === 'src/app/layout.tsx')?.content ?? '';
      const registryJson = await readZipTextFileBySuffix('example.zip', 'registry.json');
      if (!registryJson) throw new Error('registry.json not found');
      const imageManifest = await buildImageManifestFromZip('example.zip');

      const intent = extractEditIntent(editPrompt, registryJson);

      const locked = buildLockedSelectionForEdit({
        currentPageTsx: page,
        registryJson,
        intent: normalizeIntentForHelpers(intent),
      });

      const userContent: UserContentPart[] = [
        { type: 'input_text', text: `USER EDIT REQUEST:\n${editPrompt}` },

        { type: 'input_text', text: `LOCKED_SELECTION JSON:\n${JSON.stringify(locked)}` },
        { type: 'input_text', text: `COMPONENT REGISTRY JSON:\n${registryJson}` },
        { type: 'input_text', text: `IMAGE MANIFEST JSON:\n${JSON.stringify(imageManifest)}` },

        { type: 'input_text', text: `CURRENT src/app/page.tsx:\n${page}` },
        { type: 'input_text', text: `CURRENT src/app/layout.tsx:\n${layout}` },

        {
          type: 'input_text',
          text: `Apply the user's edits ONLY to unlocked items in LOCKED_SELECTION:
- If EDIT_INTENT.navbar.action === "replace" and navbar.locked === false:
  - If preferredReplacementName exists, use that exact component (verbatim registry import).
  - Else choose uniformly at random among eligible navbar components in componentRegistry (category "navbar" or path contains /navigation/), matching propsSchema.
- For each unlocked section (locked === false):
  - If preferredReplacementName exists, use that exact component (verbatim registry import).
  - Else choose uniformly at random among sectionRegistry items with the same category, respecting style/theme and propsSchema constraints.
- Preserve all locked components (do not rename, move, or alter them except safe prop updates explicitly requested).
- Keep SiteThemeProvider ONLY in src/app/page.tsx as the root; render navbar first inside it.
- Wrap every section in <div id="{section.id}" data-section="{section.id}" className="scroll-mt-24">...</div>.
- Only modify src/app/page.tsx and src/app/layout.tsx.`,
        },
      ];

      const editPatch = await this.projectsService.getProjectStructure(
        userId,
        projectId,
        versionId,
        `Apply user edits: ${editPrompt}`,
        [SystemPrompts.APPLY_EDITS],
        userContent,
      );

      if (!editPatch?.files?.length) {
        throw new Error('No files returned by APPLY_EDITS stage.');
      }

      await this.stripeService.incrementUsage(userId, JSON.stringify(editPatch).length);

      await this.projectsService.createProjectFromFiles(editPatch.files, projectId, false);

      const { repositoryOwner, repositoryName, branch } =
        await this.projectsService.pushProjectToGitHub(projectId, versionId);
      await sleep(1500);
      await this.gitHubService.forcePushBranchToMain(repositoryOwner, repositoryName, branch);

      const first = await runCiPipeline(repositoryOwner, repositoryName, branch);
      if (first.success) {
        await onSuccess(repositoryOwner, repositoryName, branch, editPatch);
        return;
      }

      const MAX_FIX_ATTEMPTS = 5;
      let attempt = 1;
      let lastError = first.error ?? 'Unknown build error';
      let previousPatch = editPatch;

      while (attempt <= MAX_FIX_ATTEMPTS) {
        const fixUserContent: UserContentPart[] = makeFixUserContent({
          errorLogs: lastError,
          previousPatchJson: previousPatch,
          registryJson,
          lockedJson: JSON.stringify(locked),
        });

        const fixPrompt = `Auto-fix after edit #${attempt}. Error: ${lastError}.`;
        const nextPatch = await this.projectsService.getProjectStructure(
          userId,
          projectId,
          versionId,
          fixPrompt,
          [SystemPrompts.FIX_CODE_ERRORS],
          fixUserContent,
        );

        if (!nextPatch?.files?.length) break;

        await this.projectsService.createProjectFromFiles(nextPatch.files, projectId, false);
        previousPatch = nextPatch;
        await pushAll(repositoryOwner, repositoryName, branch);

        await sleep(1200 + attempt * 250);

        const next = await runCiPipeline(repositoryOwner, repositoryName, branch);
        if (next.success) {
          await onSuccess(repositoryOwner, repositoryName, branch, nextPatch);
          return;
        }

        lastError = next.error ?? lastError;
        attempt++;
        await sleep(1000 * attempt);
      }

      await this.projectsService.updateProjectVersionStatus(
        projectId,
        versionId,
        ProjectVersionStatus.Failed,
      );
    } catch (err) {
      console.error('EditProjectProcessor failed', err);
      throw err;
    }
  }
}

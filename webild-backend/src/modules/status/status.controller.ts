import { Controller, Get } from '@nestjs/common';

import { GetStatusDtoResponse } from './dto/get-status.dto';
import { StatusService } from './status.service';
import { SwaggerGetStatus } from './status.swagger';
import { Public } from '../../common/decorators/public.decorator';

@Controller()
export class StatusController {
  constructor(private readonly statusService: StatusService) {}

  @Public()
  @SwaggerGetStatus()
  @Get('status')
  async getStatus(): Promise<GetStatusDtoResponse> {
    return await this.statusService.getStatus();
  }
}

import * as process from 'node:process';

import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';
import { Sequelize } from 'sequelize-typescript';

import { GetStatusDtoResponse } from './dto/get-status.dto';

@Injectable()
export class StatusService {
  private readonly redis: Redis;

  constructor(private readonly sequelize: Sequelize) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
    });
  }

  async getStatus(): Promise<GetStatusDtoResponse> {
    const dbStatus = await this.checkDbConnection();
    const redisStatus = await this.checkRedisConnection();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: dbStatus,
      redis: redisStatus,
    };
  }

  private async checkDbConnection(): Promise<'connected' | 'disconnected'> {
    try {
      await this.sequelize.authenticate();
      return 'connected';
    } catch {
      return 'disconnected';
    }
  }

  private async checkRedisConnection(): Promise<'connected' | 'disconnected'> {
    try {
      await this.redis.ping();
      return 'connected';
    } catch {
      return 'disconnected';
    }
  }
}

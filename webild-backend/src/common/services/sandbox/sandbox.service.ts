import { Daytona } from '@daytonaio/sdk';
import { Injectable } from '@nestjs/common';

@Injectable()
export class SandboxService {
  private daytona: Daytona;

  constructor() {
    this.daytona = new Daytona({
      apiKey: process.env.DAYTONA_API_KEY,
      apiUrl: process.env.DAYTONA_API_URL,
      target: process.env.DAYTONA_TARGET || 'us',
    });
  }

  async createSandboxWithPreview(
    repoUrl: string,
    name: string,
    branch: string = 'main',
  ): Promise<{ sandboxId: string; preview: string }> {
    let sandbox: any;
    const port = 3000;

    try {
      sandbox = await this.daytona.create({
        image: 'node:24',
        language: 'typescript',
        public: true,
        autoStopInterval: 0,
        autoArchiveInterval: 0,
        autoDeleteInterval: -1,
        resources: {
          memory: 4,
          cpu: 2,
        },
        envVars: {
          NODE_ENV: 'production',
          NEXT_TELEMETRY_DISABLED: '1',
          NODE_OPTIONS: '--max-old-space-size=4096',
        },
      });

      const rootDir: string = await sandbox.getUserRootDir();
      const WORKSPACE = `${rootDir}/workspace/${name}`;

      await sandbox.process.executeCommand(`mkdir -p "${WORKSPACE}"`);
      await sandbox.git.clone(repoUrl, WORKSPACE, branch);
      await sandbox.process.executeCommand(`npm ci || npm install`, WORKSPACE);
      await sandbox.process.executeCommand(`bash -lc "npx kill-port ${port} || true"`, WORKSPACE);
      sandbox.process.executeCommand(
        `npm run build && npm run start -- -H 0.0.0.0 -p ${port}`,
        WORKSPACE,
      );

      const preview = await sandbox.getPreviewLink(port);

      return {
        sandboxId: sandbox.id,
        preview: preview.legacyProxyUrl,
      };
    } catch (error) {
      // Optional: clean up the sandbox on failure
      // if (sandbox) { try { await this.daytona.delete(sandbox); } catch {} }
      throw new Error(`Failed to create sandbox: ${(error as Error)?.message ?? String(error)}`);
    }
  }

  async updateSandboxWithPreview(
    name: string,
    sandboxId: string,
  ): Promise<{ sandboxId: string; preview: string }> {
    const port = 3000;
    let sandbox: any;
    try {
      sandbox = await this.daytona.get(sandboxId);

      const rootDir: string = await sandbox.getUserRootDir();
      const WORKSPACE = `${rootDir}/workspace/${name}`;

      await sandbox.git.pull(WORKSPACE);
      await sandbox.process.executeCommand(`npm ci || npm install`, WORKSPACE);
      await sandbox.process.executeCommand(`bash -lc "npx kill-port ${port} || true"`, WORKSPACE);

      await sandbox.process.executeCommand(`pkill -f "node .*start" || true`, WORKSPACE);
      await sandbox.process.executeCommand(`pkill -f "next" || true`, WORKSPACE);
      await sandbox.process.executeCommand(`rm -rf .next .vercel .cache dist`, WORKSPACE);

      console.log('RUN build');
      sandbox.process.executeCommand(
        `npm run build && nohup npm run start -- -H 0.0.0.0 -p ${port} &`,
        WORKSPACE,
      );

      const preview = await sandbox.getPreviewLink(port);

      return {
        sandboxId: sandbox.id,
        preview: preview.legacyProxyUrl,
      };
    } catch (error) {
      // Optional: clean up the sandbox on failure
      throw new Error(`Failed to create sandbox: ${(error as Error)?.message ?? String(error)}`);
    }
  }

  async deleteSandBox(sandboxId: string, name: string) {
    try {
      const sandbox = await this.daytona.get(sandboxId);

      const rootDir = await sandbox.getUserRootDir();
      const WORKSPACE = `${rootDir}/workspace/${name}`;

      await sandbox.process.executeCommand(`bash -lc "npx kill-port 3000 || true"`, WORKSPACE);
      await sandbox.process.executeCommand(`pkill -f "node .*start" || true`, WORKSPACE);
      await sandbox.process.executeCommand(`pkill -f "next" || true`, WORKSPACE);
      await sandbox.process.executeCommand(`rm -rf .next .vercel .cache dist`, WORKSPACE);

      await this.daytona.delete(sandbox);
    } catch (error) {
      console.log('Error to delete sandbox', error);
    }
  }
}

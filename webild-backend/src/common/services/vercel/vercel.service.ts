import { Injectable, Logger } from '@nestjs/common';
import { Vercel } from '@vercel/sdk';

@Injectable()
export class VercelService {
  private readonly log = new Logger(VercelService.name);
  private readonly vercel = new Vercel({
    bearerToken: process.env.VERCEL_API_TOKEN,
  });
  private readonly teamId = process.env.VERCEL_TEAM_ID || undefined;
  private readonly teamSlug = process.env.VERCEL_TEAM_SLUG || undefined;

  private async ensureProjectByName(projectName: string) {
    const list = await this.vercel.projects.getProjects({
      teamId: this.teamId,
      slug: this.teamSlug,
      limit: '100',
      search: projectName,
    });

    const found = list.projects?.find((p) => p.name === projectName);
    if (found) return found;

    return await this.vercel.projects.createProject({
      teamId: this.teamId,
      slug: this.teamSlug,
      requestBody: {
        name: projectName,
        framework: 'nextjs',
        ssoProtection: null,
      },
    });
  }

  async createAndCheckDeployment(
    projectName: string,
    repositoryName: string,
    branch: string,
    repositoryOwner: string,
    deploymentId?: string,
  ) {
    try {
      await this.ensureProjectByName(projectName);

      const created = await this.vercel.deployments.createDeployment({
        teamId: this.teamId,
        slug: this.teamSlug,
        requestBody: {
          name: projectName,
          project: projectName,
          target: 'preview',
          gitSource: {
            type: 'github',
            repo: repositoryName,
            ref: branch,
            org: repositoryOwner,
          },
          projectSettings: {
            framework: 'nextjs',
            installCommand: 'npm ci',
            buildCommand: 'next build',
          },
          deploymentId,
        },
      });

      const immediateUrl = created.url ? `https://${created.url}` : undefined;
      this.log.log(`Vercel deployment created: ${immediateUrl ?? created.id}`);

      const ready = await this.waitForDeploymentReady(created.id, 10 * 60_000);
      const finalUrl = ready.url ? `https://${ready.url}` : immediateUrl;
      this.log.log(`Vercel deployment ready: ${finalUrl ?? created.id}`);
      return { id: ready.id, url: finalUrl };
    } catch (error) {
      console.error(error instanceof Error ? `Error: ${error.message}` : String(error));
    }
  }

  async promoteToProduction(projectName: string, deploymentId: string) {
    try {
      const promoted = await this.vercel.deployments.createDeployment({
        teamId: this.teamId,
        slug: this.teamSlug,
        requestBody: {
          name: projectName,
          project: projectName,
          target: 'production',
          deploymentId,
        },
      });

      const finalUrl = promoted.url ? `https://${promoted.url}` : undefined;
      this.log.log(`Deployment promoted to production: ${finalUrl ?? promoted.id}`);
      return finalUrl;
    } catch (error) {
      console.error(
        error instanceof Error ? `Failed to promote deployment: ${error.message}` : String(error),
      );
      throw error;
    }
  }

  private async waitForDeploymentReady(id: string, timeoutMs: number) {
    const start = Date.now();
    for (;;) {
      const dep = await this.vercel.deployments.getDeployment({
        idOrUrl: id,
        teamId: this.teamId,
        slug: this.teamSlug,
      });

      if (dep.readyState === 'READY') return dep;
      if (dep.readyState === 'ERROR') {
        this.log.error(`Vercel deployment failed:`, dep);
        throw new Error(`Vercel deployment failed: ${dep.readyState}`);
      }
      if (Date.now() - start > timeoutMs) {
        throw new Error('Timed out waiting for Vercel deployment to be READY');
      }
      await new Promise((r) => setTimeout(r, 5000));
    }
  }

  async getProjectDomains(projectId: string) {
    try {
      const response = await this.vercel.projects.getProjectDomains({
        idOrName: projectId,
        teamId: this.teamId,
      });
      return response.domains || [];
    } catch (error) {
      this.log.error(`Failed to get domains for project ${projectId}:`, error);
      throw error;
    }
  }

  async addProjectDomain(projectId: string, domain: string) {
    try {
      const response = await this.vercel.projects.addProjectDomain({
        idOrName: projectId,
        teamId: this.teamId,
        requestBody: {
          name: domain,
        },
      });
      return response;
    } catch (error) {
      this.log.error(`Failed to add domain ${domain} to project ${projectId}:`, error);
      throw error;
    }
  }

  async getProjectDomainConfig(projectId: string, domain: string) {
    try {
      const response = await this.vercel.domains.getDomainConfig({
        domain,
        projectIdOrName: projectId,
        teamId: this.teamId,
      });
      return response;
    } catch (error) {
      this.log.error(`Failed to get domain config for ${domain} in project ${projectId}:`, error);
      throw error;
    }
  }

  async removeProjectDomain(projectId: string, domain: string) {
    try {
      await this.vercel.projects.removeProjectDomain({
        idOrName: projectId,
        domain,
        teamId: this.teamId,
      });
      return { success: true };
    } catch (error) {
      this.log.error(`Failed to remove domain ${domain} from project ${projectId}:`, error);
      throw error;
    }
  }
}

import fs from 'fs';

import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';

import { applyEditsSystemPrompt } from './prompts/applyEditsSystemPrompt';
import { fixCodeErrorsPrompt } from './prompts/fixCodeErrorsPrompt';
import { generateManifestPrompt } from './prompts/generateManifestPrompt';
import { optimizeUserPrompt } from './prompts/optimizeUserPrompt';
import { userAgentPrompt } from './prompts/userAgentPrompt';
import { welcomePrompt } from './prompts/welcomePrompt';
import { Message } from '../../../modules/messages/message.entity';

export enum SystemPrompts {
  OPTIMIZE = 'optimize',
  WELCOME = 'welcome',
  GENERATE_MANIFEST = 'generate_manifest',
  FIX_CODE_ERRORS = 'fix_code_errors',
  USER_AGENT = 'userAgentPrompt',
  APPLY_EDITS = 'applyEditsPrompt',
}

const SYSTEM_PROMPT_MAP: Record<SystemPrompts, string> = {
  [SystemPrompts.OPTIMIZE]: optimizeUserPrompt,
  [SystemPrompts.WELCOME]: welcomePrompt,
  [SystemPrompts.GENERATE_MANIFEST]: generateManifestPrompt,
  [SystemPrompts.FIX_CODE_ERRORS]: fixCodeErrorsPrompt,
  [SystemPrompts.USER_AGENT]: userAgentPrompt,
  [SystemPrompts.APPLY_EDITS]: applyEditsSystemPrompt,
};

type ChatRole = 'system' | 'user' | 'assistant';

export enum GenerateCodeType {
  CODE_GENERATE = 'code_generate',
  PROMPT_GENERATE = 'prompt_generate',
}

const OpenAiModels = {
  [GenerateCodeType.CODE_GENERATE]: process.env.OPENAI_API_CODE_GENERATE || 'gpt-4o-mini',
  [GenerateCodeType.PROMPT_GENERATE]: process.env.OPENAI_API_PROMPT_GENERATE || 'gpt-5-nano',
};

export type UserContentPart =
  | { type: 'input_text'; text: string }
  | { type: 'input_file'; file_id: string };

@Injectable()
export class OpenaiService {
  private readonly openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  async sendMessage(
    prompt: string,
    systemPrompts: SystemPrompts[] = [],
    history: Message[] = [],
    type: GenerateCodeType = GenerateCodeType.PROMPT_GENERATE,
    richUserContent?: UserContentPart[],
  ): Promise<string> {
    try {
      const model = OpenAiModels[type];

      const systemMessages = systemPrompts.map((key) => ({
        role: 'system' as ChatRole,
        content: SYSTEM_PROMPT_MAP[key].trim(),
      }));

      const userMessage = richUserContent
        ? { role: 'user' as ChatRole, content: richUserContent }
        : { role: 'user' as ChatRole, content: prompt };

      const historyMessages = history.map((item) => ({
        role: item.type,
        content: item.message,
      }));

      const resp = await this.openai.responses.create({
        model,
        input: [...historyMessages, ...systemMessages, userMessage],
      });

      return resp.output_text ?? '';
    } catch (error) {
      console.error('OpenAI error:', error);
      throw new Error('Failed to communicate with OpenAI API');
    }
  }

  async uploadFile(
    localPath: string,
    purpose: 'assistants' | 'fine-tune' = 'assistants',
  ): Promise<string> {
    try {
      const file = await this.openai.files.create({
        file: fs.createReadStream(localPath),
        purpose,
      });

      return file.id;
    } catch (error) {
      console.error('Failed to upload file:', error);
      throw new Error('Failed to upload file:');
    }
  }
}

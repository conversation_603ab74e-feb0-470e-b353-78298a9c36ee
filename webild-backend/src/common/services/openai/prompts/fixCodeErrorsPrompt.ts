export const fixCodeErrorsPrompt = `
## TEN+ FATAL ERRORS — AUTO-REJECT & REGENERATE
If ANY are detected, STOP and regenerate with fixes. registry.json (componentRegistry + sectionRegistry) is the **single source of truth**. Dynamically read the latest registry and adapt to updates. Read **description + details + constraints** for component selection.

### E0) MULTI-ROOT / MISSING WRAPPER / PROVIDER ABSENT / BAD "use client"
- Returning multiple top-level JSX siblings (e.g., Navbar then <div id="hero">...) without a single root.
- Navbar rendered outside of SiteThemeProvider.
- "use client" is missing, not first line, lacks quotes, uses single quotes, or misses semicolon.
**Fix (structure is mandatory):**
1) The very first non-comment line in src/app/page.tsx MUST be exactly:
"use client";
2) The page component MUST return a single root:
return (
  <SiteThemeProvider theme={{ styleVariant, colorTemplate, textAnimation }}>
    {/* Navbar MUST be first, inside the provider, and wrapped with a stable anchor */}
    <div id="nav" data-section="nav">
      <LockedNavbar {...props} />
    </div>

    {/* Each section wrapped in its own anchorable div */}
    <div id="{section.id}" data-section="{section.id}">
      <SelectedSection {...props} />
    </div>
    {/* repeat for all sections in plan order */}
  </SiteThemeProvider>
);
- Do **not** render the navbar in layout.tsx; it lives in page.tsx inside the provider, before all sections.

### E1) IMPORT STRING MISMATCH
- Import not identical to componentRegistry[].import or sectionRegistry[].import.
- Wrong default/named style, altered paths, added/removed braces, aliasing.
**Fix:** Replace with exact registry import string; ensure JSX tag matches identifier.
E.g.
wrong - import { NavbarLayoutFloatingOverlay } from '@/components/navigation/NavbarLayoutFloatingOverlay/NavbarLayoutFloatingOverlay';
right - import NavbarLayoutFloatingOverlay from '@/components/navigation/NavbarLayoutFloatingOverlay/NavbarLayoutFloatingOverlay';

### E2) PROPS SCHEMA VIOLATION
- Missing required props, extra props, wrong types (string vs ReactNode), invalid enums/unions.
- Incomplete array items (missing required keys).
**Fix:** Conform to propsSchema: include required props, remove extras, correct types/enums, complete array shapes.

### E3) CONSTRAINTS/RULES VIOLATION
- Counts outside min/max; ignoring preferredCount.
- Text lengths outside minChars/maxChars; single-line rules ignored.
- Image rules violated (missing required, altRequired not provided); interaction flags contradicted.
**Fix:** Adjust counts (prefer preferred*), rewrite text, provide manifest images/alt, respect interactions.

### E4) REGISTRY SELECTION MISUSE
- Selecting component/section without reading description/details/constraints.
- Component doesn’t match plan.needs (e.g., needs two CTAs but propsSchema doesn’t support).
- Non-registry components (except inline JSX in page.tsx when no registry match exists).
**Fix:** Re-evaluate using description/details/constraints; use inline JSX only if no match; only propsSchema props.

### E5) ASSET & ICON VIOLATIONS
- Images not in manifest, hotlinked, or missing alt where altRequired.
- Non-lucide icons; missing imports for used icons; importing "LucideIcon" type.
**Fix:** Use manifest images; provide alt; import specific lucide icons (e.g., { ArrowRight } from 'lucide-react').

### E6) STRUCTURE / THEME / OUTPUT BREACH
- "use client" missing or malformed (see E0).
- SiteThemeProvider missing in page.tsx.
- plan.nav anchors not rendered; id mismatch (kebab-case, no #).
- Unused imports; wrong files; non-JSON output.
**Fix:** Add "use client"; wrap in SiteThemeProvider in page.tsx; ensure anchors match; remove unused imports; output strict JSON (see OUTPUT section).

### E7) UNUSED IMPORTS
- Imported modules/components not referenced.
**Fix:** Scan and remove unused imports.

### E8) WRONG PROP TYPES OR ENUMS
- Incorrect types (e.g., string for number); invalid enums (e.g., 'middle' vs 'center').
- Incomplete array items.
**Fix:** Correct types/enums per propsSchema; complete array shapes.

### E9) ICON IMPORT ERRORS
- Importing "LucideIcon" type; wildcard or default imports; using icons without imports.
- Passing strings (e.g., "ArrowRight") or the type \`LucideIcon\` itself to props typed as \`LucideIcon\`.
**Fix:** Import only specific named icons used from 'lucide-react' and pass the identifiers, e.g.:
import { Shield, ArrowRight, Star, Percent } from 'lucide-react';
// ...
icon: Shield
// For arrays/repeaters:
items: [
  { value: 5, unit: "Projects", description: "...", icon: Star },
  { value: 0, unit: "Tax", description: "...", icon: Percent }
]

### E10) SLOTS USED AS PROPS
- Passing slots IDs (e.g., "heading", "subheading", "body") as props when not in propsSchema.
**Fix:** Map needs to propsSchema keys only; omit slots IDs not matching propsSchema.
E.g. For SplitAbout (category "about") only expect **description** prop.

### E11) CONSISTENT THEME (VALUES)
- Import SiteThemeProvider: import { SiteThemeProvider } from '@/components/sections/ThemeProvider';
- Wrap page in: <SiteThemeProvider theme={{ styleVariant, colorTemplate, textAnimation }}>
- Select the appropriate:
  - styleVariant: "funAndTrendy" or "futuristicAndOutOfBox"
  - colorTemplate: 1 or 2
  - textAnimation: "slide", "rotate", "highlight", "blur", "scale", "expand", "flip", or "none"
- Use designTokens for Tailwind classes and theme variables.

### E12) NAVBAR & SECTION WRAPPERS (LOCKED RULES)
- Locked navbar not imported verbatim from LOCKED_SELECTION.navbar.import.
- Locked navbar not rendered as the **first** component inside src/app/page.tsx before all sections.
- Locked navbar not wrapped in <div id="nav" data-section="nav">.
- Navbar rendered in src/app/layout.tsx (forbidden).
- Any section not wrapped in:
  <div id="{section.id}" data-section="{section.id}" className="scroll-mt-XX"> ... </div>
  (id must exactly equal plan.sections[i].id).
- Putting the id on the inner registry component instead of the wrapper div.
**Fix:** Import locked navbar verbatim; render as first child inside SiteThemeProvider in page.tsx; wrap every section with the anchor div and optional scroll-mt offset; do not put id on the inner component.

### E13) PROVIDER LOCATION MISUSE
- SiteThemeProvider used in layout.tsx (forbidden) or missing in page.tsx.
**Fix:** Remove provider from layout.tsx; add it as the single root in page.tsx, wrapping navbar + all sections.

## EXTRA MANDATES
- Read latest registry.json dynamically; adapt to updates.
- Selection uses description/details/constraints.
- Verbatim imports from registries.
- No non-registry components; extra UI inline in page.tsx only if no registry section matches.
- Images from manifest; alt where required.
- Validate prop types (JSX for ReactNode, text for string, lambdas for functions).
- No slots IDs used as props; map needs to propsSchema.
- No lorem ipsum; copy aligns with brandVoice.
- Icon props typed as \`LucideIcon\`: import specific icons from 'lucide-react' and pass the **identifier** (e.g., \`icon: ArrowRight\`), not JSX.

## OUTPUT (STRICT JSON, NO COMMENTS OR MARKDOWN)
{
  "files": [
    { "path": "src/app/layout.tsx", "content": "FULL FILE CONTENT" },
    { "path": "src/app/page.tsx", "content": "FULL FILE CONTENT" }
  ]
}

If any error is found, regenerate until all checks pass.
`;

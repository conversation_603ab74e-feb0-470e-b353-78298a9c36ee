import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NestInterceptor,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/sequelize';
import { Observable } from 'rxjs';
import { Op } from 'sequelize';

import { ProjectUser } from '../../modules/projects/project-user.entity';
import { Project } from '../../modules/projects/project.entity';
import { Subscription } from '../../modules/stripe/subscriptions.entity';

const ACTIVE_STATES = ['active', 'trialing'] as const;

@Injectable()
export class SitesLimitInterceptor implements NestInterceptor {
  constructor(
    private readonly cfg: ConfigService,
    @InjectModel(Subscription) private readonly subs: typeof Subscription,
    @InjectModel(Project) private readonly projects: typeof Project,
    @InjectModel(ProjectUser) private readonly projectUsers: typeof ProjectUser,
  ) {}

  async intercept(ctx: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Promise<Observable<any>> {
    const req = ctx.switchToHttp().getRequest<any>();

    const dbUser = req?.dbUser;
    const userId: string | undefined = dbUser?.id ?? dbUser?.dataValues?.id;
    if (!userId) {
      throw new UnauthorizedException('No user in request');
    }

    const now = new Date();
    const sub = await this.subs.findOne({
      where: {
        userId,
        status: { [Op.in]: ACTIVE_STATES as unknown as string[] },
        currentPeriodEnd: { [Op.gt]: now },
      },
      order: [['currentPeriodEnd', 'DESC']],
    });

    if (!sub) {
      throw new InternalServerErrorException("You don't have active subscription");
    }

    const sitesMax = Number(this.cfg.get<string>('STRIPE_MONTH_PROJECTS_LIMIT')!);

    const projectCount = await this.projects.count({
      include: [
        {
          model: this.projectUsers,
          required: true,
          attributes: [],
          where: { user_id: userId },
        },
      ],
      distinct: true,
      col: 'id',
    });

    if (projectCount >= sitesMax) {
      throw new ForbiddenException(
        `Site limit reached for your plan (${sitesMax}). Upgrade your plan or remove a deployed site.`,
      );
    }

    return next.handle();
  }
}

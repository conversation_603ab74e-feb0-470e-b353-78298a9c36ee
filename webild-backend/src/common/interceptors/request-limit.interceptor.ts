import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  UnauthorizedException,
  ForbiddenException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/sequelize';
import { Observable, tap } from 'rxjs';
import { Op } from 'sequelize';

import { Subscription } from '../../modules/stripe/subscriptions.entity';
import { UsageCounter } from '../../modules/stripe/usage-counters.entity';

const ACTIVE_STATES = ['active', 'trialing'] as const;

@Injectable()
export class RequestLimitInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RequestLimitInterceptor.name);

  constructor(
    private readonly cfg: ConfigService,
    @InjectModel(UsageCounter) private readonly counters: typeof UsageCounter,
    @InjectModel(Subscription) private readonly subs: typeof Subscription,
  ) {}

  async intercept(ctx: ExecutionContext, next: <PERSON>Handler): Promise<Observable<any>> {
    const req = ctx.switchToHttp().getRequest<any>();
    const messageLength: number = req?.body?.message?.length ?? 1;
    const dbUser = req?.dbUser;
    const userId: string | undefined = dbUser?.id ?? dbUser?.dataValues?.id;
    if (!userId) throw new UnauthorizedException('No user in request');

    const now = new Date();
    const sub = await this.subs.findOne({
      where: {
        userId,
        status: { [Op.in]: ACTIVE_STATES as unknown as string[] },
        currentPeriodEnd: { [Op.gt]: now },
      },
      order: [['currentPeriodEnd', 'DESC']],
    });

    if (!sub) {
      throw new InternalServerErrorException("You don't have active subscription");
    }

    const priceId = sub.priceId as string;
    const periodStart = sub.currentPeriodStart as Date;
    const periodEnd = sub.currentPeriodEnd as Date;

    const limit = Number(this.cfg.get<string>('STRIPE_MONTH_TOKEN_LIMIT')!);

    const [counter] = await this.counters.findOrCreate({
      where: { userId, periodStart },
      defaults: { userId, periodStart, periodEnd, used: 0, priceId },
    });

    if (counter.used >= limit) {
      throw new ForbiddenException('Monthly request quota exceeded');
    }

    return next.handle().pipe(
      tap(() => {
        void this.counters
          .increment(
            { used: messageLength },
            { where: { id: counter.id, used: { [Op.lt]: limit } } },
          )
          .then(([, affected]) => {
            if (affected === 0) {
              this.logger.warn(
                `Quota race: could not increment counter id=${counter.id} (limit reached).`,
              );
            }
          })
          .catch((e) => {
            this.logger.error('Failed to increment usage counter', e);
          });
      }),
    );
  }
}

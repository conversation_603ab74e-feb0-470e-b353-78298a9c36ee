import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Request } from 'express';

import { ProjectUser } from '../../modules/projects/project-user.entity';

@Injectable()
export class ProjectOwnershipGuard implements CanActivate {
  constructor(@InjectModel(ProjectUser) private readonly projectUserModel: typeof ProjectUser) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest<Request>();
    const dbUser = req['dbUser'];
    const projectId = req.params.projectId;

    if (!dbUser || !projectId) {
      throw new UnauthorizedException('Missing user or project ID');
    }

    const relation = await this.projectUserModel.findOne({
      where: {
        user_id: dbUser.id,
        project_id: projectId,
      },
    });

    if (!relation) {
      throw new UnauthorizedException('User does not have access to this project');
    }

    return true;
  }
}

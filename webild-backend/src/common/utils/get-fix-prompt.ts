import { UserContentPart } from '../services/openai/openai.service';

export function makeFixUserContent(params: {
  errorLogs: string;
  previousPatchJson?: unknown;
  registryJson: string;
  lockedJson: string;
  maxPrevChars?: number;
}): UserContentPart[] {
  const { errorLogs, previousPatch<PERSON><PERSON>, registry<PERSON><PERSON>, locked<PERSON>son, maxPrevChars = 120_000 } = params;

  let prev = '';
  if (previousPatchJson) {
    try {
      let s = JSON.stringify(previousPatchJson);
      if (s.length > maxPrevChars) s = s.slice(0, maxPrevChars) + '... [truncated]';
      prev = `\n\n--- PREVIOUS PATCH MANIFEST ---\n${s}\n--- END PREVIOUS PATCH MANIFEST ---\n`;
    } catch (error) {
      console.error(error);
    }
  }

  const text = `Fix the build errors for the attached project snapshot.
--- ERROR LOGS (verbatim) ---
${errorLogs}
--- END ERROR LOGS ---
${prev}
Use the COMPONENT REGISTRY below as the only allowed components/imports/props.
COMPONENT REGISTRY JSON:
${registryJson}
LOCKED_SELECTION JSON:
${lockedJson}
Return ONLY the strict JSON manifest with "files":[{"path","content"}].
`;
  return [{ type: 'input_text', text }];
}

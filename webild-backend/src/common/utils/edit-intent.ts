export type EditIntent = {
  navbar?: {
    action: 'none' | 'replace' | 'update';
    preferredName?: string;
  };
  sections: Array<{
    id?: string;
    category?: string;
    action: 'replace' | 'remove' | 'update';
    preferredName?: string;
  }>;
};

const CAT_WORDS: Record<string, string[]> = {
  hero: ['hero'],
  about: ['about'],
  how_to_buy: ['how to buy', 'how-to-buy', 'htb', 'buy steps', 'purchase'],
  tokenomics: ['tokenomics', 'token', 'supply'],
  faq: ['faq', 'questions', 'accordion'],
  roadmap: ['roadmap', 'timeline', 'milestones', 'process'],
  footer: ['footer'],
  navbar: ['navbar', 'navigation', 'menu', 'top bar', 'header'],
};

export function extractEditIntent(editPrompt: string, registryJson: string): EditIntent {
  const text = editPrompt.toLowerCase();
  const intent: EditIntent = { navbar: { action: 'none' }, sections: [] };

  const wantsReplace = /\b(change|switch|replace|use another|different)\b/.test(text);
  const wantsNavbar = CAT_WORDS.navbar.some((w) => text.includes(w));

  let preferredNavbarName: string | undefined;
  const reg = JSON.parse(registryJson);
  const allNames: string[] = [
    ...(reg?.componentRegistry?.map((c: any) => c.name) ?? []),
    ...(reg?.sectionRegistry?.map((s: any) => s.name) ?? []),
  ].filter(Boolean);

  const mentionedName = allNames.find((n) => text.includes(n.toLowerCase()));
  if (mentionedName && wantsNavbar) preferredNavbarName = mentionedName;

  if (wantsNavbar && wantsReplace) {
    intent.navbar = { action: 'replace', preferredName: preferredNavbarName };
  } else if (wantsNavbar) {
    intent.navbar = { action: 'update' };
  }

  for (const category of Object.keys(CAT_WORDS)) {
    if (category === 'navbar') continue;
    const hit = CAT_WORDS[category].some((w) => text.includes(w));
    if (!hit) continue;

    let preferredName: string | undefined = undefined;
    if (mentionedName) preferredName = mentionedName;

    const action: 'replace' | 'remove' | 'update' = /\b(remove|delete)\b/.test(text)
      ? 'remove'
      : wantsReplace
        ? 'replace'
        : 'update';

    intent.sections.push({ category, action, preferredName });
  }

  const idMatches = text.match(/(#|id\s*)([a-z0-9]+)/g) ?? [];
  idMatches.forEach((m) => {
    const id = m.replace(/^#|^id\s*/g, '');
    const sec = intent.sections.find((s) => s.id === id);
    if (!sec) intent.sections.push({ id, action: wantsReplace ? 'replace' : 'update' });
  });

  return intent;
}

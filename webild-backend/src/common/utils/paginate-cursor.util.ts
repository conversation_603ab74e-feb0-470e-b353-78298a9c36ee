import { FindOptions, Model, ModelStatic, Op } from 'sequelize';

import { CursorPaginationMetaDto } from '../dto/cursor-pagination.dto';

interface CursorPaginateOptions<T extends Model> {
  limit?: number;
  cursor?: string;
  where?: FindOptions<T>['where'];
  orderBy?: keyof T['_attributes'] & string;
  orderDirection?: 'ASC' | 'DESC';
  include?: FindOptions<T>['include'];
}

export async function paginateCursor<T extends Model>(
  model: ModelStatic<T>,
  options: CursorPaginateOptions<T> = {},
): Promise<{
  data: T[];
  meta: CursorPaginationMetaDto;
}> {
  const {
    limit = 10,
    cursor,
    where = {},
    orderBy = 'id' as keyof T['_attributes'] & string,
    orderDirection = 'ASC',
    include,
  } = options;

  const queryWhere = { ...where };
  if (cursor) {
    (queryWhere as any)[orderBy] = {
      [orderDirection === 'ASC' ? Op.gt : Op.lt]: cursor,
    };
  }

  const results = await model.findAll({
    where: queryWhere,
    order: [[orderBy, orderDirection]],
    limit: +limit + 1,
    include,
  });

  const paginatedData = results.slice(0, limit);
  const hasNextPage = results.length > limit;
  const nextCursor = hasNextPage ? (paginatedData.at(-1)?.get(orderBy) as string) : null;

  const totalCount = await model.count({ where, include });
  const totalPages = Math.ceil(totalCount / limit);

  return {
    data: paginatedData,
    meta: {
      hasNextPage,
      nextCursor,
      totalCount,
      totalPages,
    },
  };
}

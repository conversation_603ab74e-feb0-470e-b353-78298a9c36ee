init:
	@echo "🧹 Cleaning..."
	rm -rf node_modules
	rm -f package-lock.json
	bun install
	@if [ ! -f .env ]; then cp .env.example .env; fi

	@echo "🐳 Starting Docker containers..."
	cd docker && docker compose --env-file ../.env up -d --build

	@echo "⏳ Waiting 5 seconds for containers to be ready..."
	sleep 5

	@echo "🧼 Resetting database migrations..."
	npx sequelize-cli db:migrate:undo:all
	npx sequelize-cli db:migrate

	@echo "✅ Done."

start:
	cd docker && docker compose --env-file ../.env up -d
	bun run start:dev

start-server-dev:
	@echo "📦 Installing dependencies..."
	bun ci

	@echo "🐳 Starting Docker containers..."
	cd docker && docker compose --env-file ../.env up -d

	@echo "⏳ Waiting 5 seconds for containers to be ready..."
	sleep 5

	@echo "🗄️  Running pending database migrations..."
	npx sequelize-cli db:migrate

	@echo "🛠️  Building application..."
	bun run limited-build

	@echo "🚀 Starting server with PM2..."
	@if pm2 describe webild-backend > /dev/null; then \
	  echo "🔄 Process exists, deleting..."; \
	  pm2 delete webild-backend; \
	fi
	pm2 start dist/src/main.js --name webild-backend
	pm2 save || true

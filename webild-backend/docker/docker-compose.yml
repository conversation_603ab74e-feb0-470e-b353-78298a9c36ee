services:
  mysql:
    build:
      context: ./mysql
      dockerfile: Dockerfile
    container_name: mysql-db
    env_file:
      - ../.env
    ports:
      - "${MYSQL_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7
    container_name: redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:

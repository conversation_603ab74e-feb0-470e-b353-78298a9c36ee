<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2444_620)">
<ellipse cx="15.4706" cy="14.5166" rx="9.32913" ry="9.42602" transform="rotate(25.4025 15.4706 14.5166)" fill="url(#paint0_linear_2444_620)"/>
</g>
<g filter="url(#filter1_f_2444_620)">
<circle cx="15.9782" cy="16.1505" r="8.16858" transform="rotate(25.4025 15.9782 16.1505)" fill="url(#paint1_linear_2444_620)"/>
</g>
<g filter="url(#filter2_f_2444_620)">
<circle cx="15.9772" cy="16.149" r="8.16858" transform="rotate(-169.598 15.9772 16.149)" fill="url(#paint2_linear_2444_620)"/>
</g>
<g filter="url(#filter3_f_2444_620)">
<circle cx="16.2375" cy="16.4107" r="8.16858" transform="rotate(-169.598 16.2375 16.4107)" fill="url(#paint3_linear_2444_620)"/>
</g>
<defs>
<filter id="filter0_f_2444_620" x="3.88884" y="2.8737" width="23.1637" height="23.2858" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_2444_620"/>
</filter>
<filter id="filter1_f_2444_620" x="0.510355" y="0.683207" width="30.9358" height="30.9353" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_2444_620"/>
</filter>
<filter id="filter2_f_2444_620" x="3.45055" y="3.62194" width="25.0535" height="25.054" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.163372" result="effect1_foregroundBlur_2444_620"/>
</filter>
<filter id="filter3_f_2444_620" x="1.09686" y="1.26972" width="30.2814" height="30.2819" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.48526" result="effect1_foregroundBlur_2444_620"/>
</filter>
<linearGradient id="paint0_linear_2444_620" x1="15.9046" y1="16.1285" x2="22.8707" y2="23.6468" gradientUnits="userSpaceOnUse">
<stop stop-color="#5824B4"/>
<stop offset="0.951923" stop-color="#5824B4" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_2444_620" x1="12.5912" y1="15.3781" x2="24.8974" y2="26.1396" gradientUnits="userSpaceOnUse">
<stop offset="0.00961538" stop-color="#7436D9"/>
<stop offset="0.951923" stop-color="#7436D9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_2444_620" x1="19.3838" y1="18.2168" x2="11.3718" y2="6.18932" gradientUnits="userSpaceOnUse">
<stop stop-color="#8265D8" stop-opacity="0"/>
<stop offset="1" stop-color="#A273F6"/>
</linearGradient>
<linearGradient id="paint3_linear_2444_620" x1="24.5672" y1="22.6244" x2="19.1631" y2="9.77472" gradientUnits="userSpaceOnUse">
<stop stop-color="#8265D8" stop-opacity="0"/>
<stop offset="1" stop-color="#C0AAFF"/>
</linearGradient>
</defs>
</svg>

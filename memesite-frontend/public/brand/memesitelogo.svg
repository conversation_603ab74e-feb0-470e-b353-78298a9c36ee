<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 495 86" width="495" height="86">
	<title>memesitelogo</title>
	<defs>
		<radialGradient id="g1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(84.997,-1.536,1.536,84.997,387.06,12.254)">
			<stop offset="0" stop-color="#622cc2"/>
			<stop offset="1" stop-color="#ffffff"/>
		</radialGradient>
		<linearGradient id="g2" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.141,10.015,-10.007,3.139,385.7,-12.68)">
			<stop offset="0" stop-color="#5824b4" stop-opacity="1"/>
			<stop offset=".952" stop-color="#5824b4" stop-opacity="0"/>
		</linearGradient>
		<linearGradient id="g3" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(6.656,15.361,-15.361,6.656,383.732,-14.891)">
			<stop offset=".01" stop-color="#7436d9" stop-opacity="1"/>
			<stop offset=".952" stop-color="#7436d9" stop-opacity="0"/>
		</linearGradient>
		<filter x="-50%" y="-50%" width="200%" height="200%" id="f1"> <feGaussianBlur stdDeviation=".2"/> </filter>
		<linearGradient id="g4" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(5.846,13.596,-13.596,5.846,383.475,-15.402)">
			<stop offset="0" stop-color="#8265d8" stop-opacity="0"/>
			<stop offset="1" stop-color="#a273f6" stop-opacity="1"/>
		</linearGradient>
		<filter x="-50%" y="-50%" width="200%" height="200%" id="f2"> <feGaussianBlur stdDeviation="3.6"/> </filter>
		<linearGradient id="g5" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.067,13.942,-13.942,3.067,379.549,-20.22)">
			<stop offset="0" stop-color="#8265d8" stop-opacity="0"/>
			<stop offset="1" stop-color="#c0aaff" stop-opacity="1"/>
		</linearGradient>
	</defs>
	<style>
		.s0 { fill: url(#g1) } 
		.s1 { fill: url(#g2) } 
		.s2 { fill: url(#g3) } 
		.s3 { filter: url(#f1);fill: url(#g4) } 
		.s4 { filter: url(#f2);fill: url(#g5) } 
	</style>
	<path class="s0" d="m84.7 85h-11.9v-61.6h-0.4l-25.2 60.9h-9.7l-25.3-60.9h-0.4v61.6h-11.8v-83.7h15l27.1 66h0.5l27-66h15.1zm54.3-18h11.9c-1.7 11.2-12.3 19.1-26.3 19.1-17.9 0-28.7-12.1-28.7-31.4 0-19.1 11-31.9 28.2-31.9 17 0 27.5 12 27.5 30.5v4.3h-43.1v0.7c0 10.7 6.4 17.7 16.4 17.7 7.2 0 12.5-3.6 14.1-9zm-14.9-34.1c-8.7 0-14.9 6.7-15.5 15.9h30.5c-0.3-9.3-6.2-15.9-15-15.9zm37.3 52.1v-61.1h11.9v10.3h0.3c2.7-6.8 9.1-11.3 17.3-11.3 8.5 0 14.7 4.4 17.1 11.9h0.3c3.1-7.4 10.3-11.9 19-11.9 12.1 0 20 8 20 20.1v42h-12.6v-39c0-7.8-4.3-12.4-11.7-12.4-7.4 0-12.6 5.5-12.6 13.3v38.1h-12.3v-39.9c0-7-4.4-11.5-11.5-11.5-7.4 0-12.7 5.8-12.7 13.7v37.7zm138.4-18h11.9c-1.7 11.2-12.3 19.1-26.2 19.1-18 0-28.8-12.1-28.8-31.4 0-19.1 11-31.9 28.2-31.9 17 0 27.5 12 27.5 30.5v4.3h-43.1v0.7c0 10.7 6.4 17.7 16.5 17.7 7.1 0 12.4-3.6 14-9zm-14.9-34.1c-8.7 0-14.9 6.7-15.5 15.9h30.5c-0.3-9.3-6.2-15.9-15-15.9zm36.2 8.3c0-10.9 9.6-18.4 23.9-18.4 13.5 0 23.1 7.6 23.5 18.5h-11.8c-0.6-5.5-5.1-8.9-12.1-8.9-6.8 0-11.3 3.3-11.3 8.1 0 3.7 3 6.3 9.4 7.9l10.2 2.3c12.3 3 17 7.6 17 16.6 0 11.2-10.4 18.8-25.4 18.8-14.4 0-24-7.5-25-18.7h12.4c1 5.9 5.5 9.2 13.2 9.2 7.5 0 12.2-3.2 12.2-8.1 0-3.9-2.3-6-8.7-7.7l-10.9-2.6c-11.1-2.7-16.6-8.4-16.6-17zm58.9 43.8v-61.1h12.5v61.1zm6.3-70c-4 0-7.2-3.2-7.2-7.1 0-4 3.2-7.2 7.2-7.2 4.1 0 7.3 3.2 7.3 7.2 0 3.9-3.2 7.1-7.3 7.1zm22.7-5.5h12.6v14.4h11.6v10h-11.6v33.6c0 5.3 2.3 7.7 7.4 7.7 1.3 0 3.4-0.2 4.2-0.3v9.9c-1.4 0.3-4.2 0.6-7 0.6-12.3 0-17.2-4.7-17.2-16.5v-35h-8.8v-10h8.8zm73.5 57.6h11.9c-1.8 11.1-12.3 19.1-26.3 19.1-17.9 0-28.8-12.2-28.8-31.4 0-19.2 11-31.9 28.3-31.9 16.9 0 27.5 11.9 27.5 30.4v4.3h-43.1v0.8c0 10.6 6.3 17.6 16.4 17.6 7.1 0 12.4-3.6 14.1-8.9zm-14.9-34.1c-8.8 0-14.9 6.6-15.6 15.9h30.5c-0.3-9.4-6.2-15.9-14.9-15.9z"/>
	<g>
		<path fill-rule="evenodd" class="s1" d="m381.9-5.6c-4.8-2.3-6.8-8-4.5-12.9 2.3-4.8 8-6.8 12.7-4.6 4.8 2.3 6.8 8 4.5 12.8-2.3 4.9-8 6.9-12.7 4.7z"/>
	</g>
	<g>
		<path fill-rule="evenodd" class="s2" d="m382.9-5.1c-4.1-2-5.9-7-3.9-11.2 2-4.2 6.9-5.9 11.1-3.9 4.2 1.9 6 6.9 4 11.1-2 4.2-7 6-11.2 4z"/>
	</g>
	<g>
		<path fill-rule="evenodd" class="s3" d="m388-20.9c4.6 0.8 7.6 5.2 6.8 9.7-0.9 4.6-5.2 7.6-9.8 6.7-4.5-0.8-7.5-5.2-6.7-9.7 0.8-4.5 5.2-7.6 9.7-6.7z"/>
	</g>
	<g>
		<path fill-rule="evenodd" class="s4" d="m388.3-20.6c4.6 0.8 7.6 5.1 6.7 9.7-0.8 4.5-5.2 7.5-9.7 6.7-4.6-0.8-7.6-5.2-6.7-9.7 0.8-4.6 5.2-7.6 9.7-6.7z"/>
	</g>
</svg>
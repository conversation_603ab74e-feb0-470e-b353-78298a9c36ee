import { ChatMessage } from "../chat";
import { LabImage } from "@/data/labImages";

export interface ChatMessageProps {
  message: ChatMessage;
  isAnimated?: boolean;
  isThinking?: boolean;
  onAnimationComplete?: () => void;
  onAllTasksCompleted?: () => void;
  viewContext?: 'clone' | 'build' | 'lab' | 'imageEdit' | string;
  onImageApply?: () => void;
  onImageUse?: (image?: LabImage) => void;
}
declare global {
  interface SpeechRecognition extends EventTarget {
    continuous: boolean
    interimResults: boolean
    lang: string
    start(): void
    stop(): void
    abort(): void
    onstart: ((this: SpeechRecognition, ev: Event) => void) | null
    onresult: ((this: SpeechR<PERSON>ognition, ev: SpeechRecognitionEvent) => void) | null
    onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null
    onend: ((this: SpeechRecognition, ev: Event) => void) | null
  }

  interface SpeechRecognitionEvent extends Event {
    resultIndex: number
    results: SpeechRecognitionResultList
  }

  interface SpeechRecognitionErrorEvent extends Event {
    error: string
    message: string
  }

  interface SpeechRecognitionResultList {
    length: number
    item(index: number): SpeechRecognitionResult
    [index: number]: SpeechRecognitionResult
  }

  interface SpeechRecognitionResult {
    isFinal: boolean
    length: number
    item(index: number): SpeechRecognitionAlternative
    [index: number]: SpeechRecognitionAlternative
  }

  interface SpeechRecognitionAlternative {
    transcript: string
    confidence: number
  }

  interface SpeechSynthesisUtterance extends EventTarget {
    text: string
    lang: string
    voice: SpeechSynthesisVoice | null
    volume: number
    rate: number
    pitch: number
    onstart: ((this: SpeechSynthesisUtterance, ev: Event) => void) | null
    onend: ((this: SpeechSynthesisUtterance, ev: Event) => void) | null
    onerror: ((this: SpeechSynthesisUtterance, ev: Event) => void) | null
  }

  interface SpeechSynthesisVoice {
    voiceURI: string
    name: string
    lang: string
    localService: boolean
    default: boolean
  }

  interface SpeechSynthesis extends EventTarget {
    speak(utterance: SpeechSynthesisUtterance): void
    cancel(): void
    pause(): void
    resume(): void
    getVoices(): SpeechSynthesisVoice[]
    pending: boolean
    speaking: boolean
    paused: boolean
  }

  interface Window {
    SpeechRecognition: new () => SpeechRecognition
    webkitSpeechRecognition: new () => SpeechRecognition
    speechSynthesis: SpeechSynthesis
  }
}

export {}

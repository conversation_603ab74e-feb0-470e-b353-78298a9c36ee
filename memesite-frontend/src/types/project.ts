export interface ProjectData {
  id: string;
  mode: "build" | "clone" | "bob";
  prompt: string;
  createdAt: string;
}

export interface CreateProjectRequest {
  name: string;
}

export interface ProjectVersion {
  id: string;
  project_id: string;
  version_number: number;
  version_prompt: string;
  version_branch: string;
  version_status: string;
  feedback_info: string | null;
  feedback_score: number | null;
  createdAt: string;
  updatedAt: string;
}

export interface BaseProject {
  id: string;
  name: string;
  repositoryUrl: string;
  repositoryOwner: string;
  repositoryName: string;
  activeVersionId: string;
  publish_url: string | null;
  preview_url?: string | null;
  vercel_preview_deploy_id?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface BackendProject extends BaseProject {
  activeVersion: ProjectVersion;
}

export interface ProjectWithVersions extends BaseProject {
  versions: ProjectVersion[];
}

export interface CreateProjectResponse {
  code: number;
  data: BackendProject;
}

export interface GetProjectIDResponse {
  code: number;
  data: ProjectWithVersions;
}

export interface Message {
  id: string;
  message: string;
  type: "assistant" | "user";
  user_id: string;
  project_id: string;
  version_id: string;
  version_number: number;
  updatedAt: string;
  createdAt: string;
}

export interface CreateMessageResponse {
  code: number;
  data: {
    message: Message;
    isGenerating: boolean;
  };
}

export interface ProjectMessage {
  id: string;
  version_number: number;
  type: "user" | "assistant";
  message: string;
  is_system: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GetMessagesResponse {
  data: ProjectMessage[];
  meta: {
    nextCursor?: string;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
  };
}

export interface DeployResponseData {
    id: string;
    project_id: string;
    version_prompt: Record<string, unknown>;
    version_branch: string;
    version_status: string;
    feedback_info: Record<string, unknown> | null;
    feedback_score: number | null;
    createdAt: string;
    updatedAt: string;
}

export interface DeployProjectResponse {
  code: number;
  data: DeployResponseData;
  details: Record<string, unknown>;
}

export interface StripeCheckoutResponse {
  code: number;
  data: {
    url: string;
  };
}

export interface PricingPlan {
  name: string;
  price: string;
  period: string;
  features: string[];
  buttonText: string;
  planId: 'month' | 'year';
  popular?: boolean;
}

export interface CheckoutError extends Error {
  message: string;
}

export interface PlanLimitError {
  message: string;
  error: string;
  statusCode: 403;
}

// Image Generation APIs
export interface GenerateImageRequest {
  prompt: string;
}

export interface GenerateImageResponse {
  key: string;
  url: string;
  contentType: string;
}

export interface ImageItem {
  key: string;
  url: string;
}

export interface SaveImageRequest {
  key: string;
  url: string;
}

export interface SaveImageResponse {
  key: string;
  url: string;
}

export interface DeleteImageRequest {
  key: string;
  url: string;
}

export interface BulkDeleteImageRequest {
  keys: string[];
  urls: string[];
}

export interface DeleteImageResponse {
  deleted: boolean;
}

export interface BulkDeleteImageResponse {
  results: Array<{
    key: string;
    deleted: boolean;
  }>;
}

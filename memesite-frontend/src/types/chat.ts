export type MessageType = "text" | "tasks" | "iframe" | "image";

export interface TaskItem {
  text: string;
  completed?: boolean;
}

export interface TasksContent {
  title: string;
  tasks: TaskItem[];
}

export interface IframeContent {
  url: string;
  title?: string;
  completionTime?: number;
}

export interface ImageContent {
  imageUrl?: string;
  prompt: string;
  isGenerating?: boolean;
  generationTime?: number;
  key?: string; // S3 key for backend save operations
}

export interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string | TasksContent | IframeContent | ImageContent;
  type?: MessageType;
  showPreview?: boolean;
  taskCompletionTimes?: number[];
  iframeCompleted?: boolean;
  imageCompleted?: boolean;
  timestamp: Date;
  animationComplete?: boolean;
  animationStartTime?: number;
  manuallyStopped?: boolean;
}

export interface ChatMessageProps {
  message: ChatMessage;
  isAnimated?: boolean;
}

export type ViewType = "bilder" | "lab";
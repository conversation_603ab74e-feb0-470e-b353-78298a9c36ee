import { Clock, Folder, Users, Settings } from "lucide-react";

export const strs = {
  title: "Projects",
  newProject: "New Project",
  states: {
    noProjects: {
      title: "Ready to Build?",
    },
    notFound: {
      title: "Project Not Found",
      accessDenied: "Access Denied",
      goBack: "Go Back Home",
    },
    startGenerating: {
      title: "Start generating Images",
      subtitle: "Type a prompt below to generate images",
    },
    imageReplace: {
      title: "Select an image to replace",
      subtitle: "Click on an image in the preview to replace it with your selected image",
    },
    editMode: {
      title: "Select an element",
      subtitle: "Click on an element to start editing it visually",
    },
  },
  views: {
    bilder: "Bilder",
    lab: "The Lab",
  },
  lab: {
    dropZone: {
      addFiles: "Add Files",
      generateImage: "Generate Image",
      dragAndDrop: "Drag and drop images",
      remove: "Remove",
      crop: "Crop",
      cropPresets: {
        freeform: "Freeform",
        original: "Original",
        square: "1:1",
        landscape: "16:9",
        portrait: "9:16",
        landscape54: "5:4",
        portrait45: "4:5",
      },
      cancel: "Cancel",
      applyCrop: "Apply Crop",
      cropTitle: "Crop",
      cropPreview: "Crop preview",
    },
    imageMessage: {
      save: "Save",
      use: "Use",
    },
    actionBar: {
      selectAll: "Select All",
      deselectAll: "Deselect All",
      delete: "Delete",
      download: "Download",
      useIt: "Use it",
    },
  },
  imageEditOverlay: {
    tabs: {
      manage: "Manage Image",
      generate: "Generate Image",
    },
    labFlow: {
      replace: "Replace",
      cancel: "Cancel",
    },
  },
  confirmationPopup: {
    replaceImage: {
      title: "Replace Image?",
      description: "You're about to replace the current image.",
      cancelText: "Cancel",
      confirmText: "Replace Image",
    },
    removeImage: {
      title: "Remove Image?",
      description: "Are you sure you want to remove this image?",
      cancelText: "Cancel",
      confirmText: "Remove",
    },
  },
  buttons: {
    app: "App",
    publish: "Publish",
    analytics: "Analytics",
    closeAnalytics: "Close Analytics",
    edit: "Edit",
    saveChanges: "Save Changes",
    cancel: "Cancel",
    site: "Site",
  },
  alt: {
    desktopView: "Desktop View",
    mobileView: "Mobile View",
    browserIcon: "Browser Icon",
    refresh: "Refresh",
    expand: "Expand",
    arrowBack: "Back",
    arrowForward: "Forward",
    logoDot: "Logo Dot Image",
  },
  iframe: {
    title: "Webild Preview",
    urls: [
      "/preview",
      "/preview",
      "/preview",
    ],
  },
  chat: {
    thinking: "Building",
    assistant: "Memesite",
  },
  loading: {
    title: "Spinning up preview...",
    tips: [
      { icon: "/icons/sidebar.svg", text: "Chat with AI in the sidebar" },
      { icon: "/icons/target.svg", text: "Select specific elements to modify" },
      { icon: "/icons/picture.svg", text: "Upload images as a reference" },
    ],
  },
  sidebar: [
    {
      id: "bild",
      icon: Clock,
      label: "Bild",
      href: "/home",
      onClick: () => window.history.back(),
    },
    {
      id: "projects",
      icon: Folder,
      label: "Projects",
      href: "/projects",
      isActive: true,
    },
    {
      id: "community",
      icon: Users,
      label: "Community",
      href: "/community",
      isComingSoon: true,
    },
    {
      id: "settings",
      icon: Settings,
      label: "Settings",
      href: "/settings",
    },
  ],
};

import { strs } from '@/constants/pages/projects/strs';

export const CROP_CONSTANTS = {
    MIN_CROP_SIZE: 50,
    INITIAL_DELAY: 10,
    IMAGE_LOAD_DELAY: 200,
    Z_INDEX: 9999,
    TRANSITION_DURATION: 300,
    BUTTON_TRANSITION_DURATION: 500,
    MAX_IMAGE_HEIGHT: '60vh',
    BORDER_WIDTH: 2,
    BORDER_OFFSET: 1,
    HANDLE_SIZE: 3,
    HANDLE_OFFSET: 5,
    SHADOW_OPACITY: 0.5,
    SHADOW_SIZE: 9999,
} as const;

export const CROP_GRID = {
    COLS: 3,
    ROWS: 3,
} as const;

export type AspectRatio = 'freeform' | 'original' | 'square' | 'landscape' | 'portrait' | 'landscape54' | 'portrait45';

export const aspectRatioValues: Record<AspectRatio, number | null> = {
    'freeform': null,
    'original': null,
    'square': 1,
    'landscape': 16 / 9,
    'portrait': 9 / 16,
    'landscape54': 5 / 4,
    'portrait45': 4 / 5,
} as const;

export const aspectOptions: { key: AspectRatio; label: string }[] = [
    { key: 'freeform', label: strs.lab.dropZone.cropPresets.freeform },
    { key: 'original', label: strs.lab.dropZone.cropPresets.original },
    { key: 'square', label: strs.lab.dropZone.cropPresets.square },
    { key: 'landscape', label: strs.lab.dropZone.cropPresets.landscape },
    { key: 'portrait', label: strs.lab.dropZone.cropPresets.portrait },
    { key: 'landscape54', label: strs.lab.dropZone.cropPresets.landscape54 },
    { key: 'portrait45', label: strs.lab.dropZone.cropPresets.portrait45 },
] as const;
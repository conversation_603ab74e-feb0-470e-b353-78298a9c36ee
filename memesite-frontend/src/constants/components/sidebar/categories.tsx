import { Clock, Folder, MessageSquare, Settings } from "lucide-react";
import { SidebarCategory } from "@/types/sidebar";
import { strs } from "./strs";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export const getSidebarCategories = (
  pathname: string,
  router: AppRouterInstance
): SidebarCategory[] => [
  {
    id: "build",
    label: strs.categories.build,
    icon: <Clock className="h-[var(--text-sm)] w-auto" />,
    onClick: () => router.push("/home"),
    isActive: pathname === "/home",
  },
  {
    id: "projects",
    label: strs.categories.projects,
    icon: <Folder className="h-[var(--text-sm)] w-auto" />,
    onClick: () => router.push("/projects"),
    isActive: pathname === "/projects",
  },
  {
    id: "brand-engine",
    label: strs.categories.brandEngine,
    icon: <MessageSquare className="h-[var(--text-sm)] w-auto" />,
    onClick: () => router.push("/brand-engine"),
    isActive: pathname === "/brand-engine",
  },
];

export const getSettingsCategory = (router: AppRouterInstance): SidebarCategory => ({
  id: "settings",
  label: strs.categories.settings,
  icon: <Settings className="h-[var(--text-sm)] w-auto" />,
  onClick: () => router.push("/settings"),
});
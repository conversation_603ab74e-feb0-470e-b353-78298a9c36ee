import type { PricingPlan } from '@/types/project';

export const PRICING_ANIMATION_CONSTANTS = {
  BORDER_RADIUS: "9999px",
  DURATION: 3000,
  CLOSE_ANIMATION_DELAY: 300,
} as const;

export const PRICING_PLANS: PricingPlan[] = [
  {
    name: 'Monthly Plan',
    price: '$49.99',
    period: '/month',
    features: [
      'Tokens: 670,670',
      '<PERSON> joins your call & builds live',
      'Unlimited deployed sites',
      'Priority email support',
      'Early access to new features'
    ],
    buttonText: 'Choose Monthly',
    planId: 'month',
    popular: false
  },
  {
    name: 'Yearly Plan',
    price: '$379.99',
    period: '/year',
    features: [
      'Tokens: 670,670',
      '<PERSON> joins your call & builds live',
      'Unlimited deployed sites',
      'Priority email support',
      'Early access to new features'
    ],
    buttonText: 'Choose Yearly',
    planId: 'year',
    popular: true
  }
];
"use client";

import { memo } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";

const ProjectCardSkeleton = memo(() => {
    return (
        <div className="relative rounded black-card gradient-before-rounded shadow p-2 flex flex-col gap-2 animate-pulse">
            <div className="relative w-full aspect-3/2 bg-white/5 rounded-sm" />
            <div className="flex items-start justify-between p-2 pt-0">
                <div className="min-w-0 max-w-[80%] flex flex-col gap-2">
                    <div className="h-5 w-32 bg-white/5 rounded" />
                    <div className="h-3 w-24 bg-white/5 rounded" />
                </div>
                <div className="w-6 h-6 bg-white/5 rounded" />
            </div>
        </div>
    );
});

ProjectCardSkeleton.displayName = "ProjectCardSkeleton";

const ProjectsListSkeleton = memo(() => {
    return (
        <AnimationContainer className="h-full min-h-0 mask-fade-y">
            <div className="h-full overflow-y-auto">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2.5 pt-2.5">
                    {Array.from({ length: 6 }).map((_, index) => (
                        <ProjectCardSkeleton key={index} />
                    ))}
                </div>
            </div>
        </AnimationContainer>
    );
});

ProjectsListSkeleton.displayName = "ProjectsListSkeleton";

export default ProjectsListSkeleton;
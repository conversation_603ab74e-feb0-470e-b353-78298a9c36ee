"use client";

import { memo } from "react";

interface SelectableImageItemSkeletonProps {
    className?: string;
}

const SelectableImageItemSkeleton = memo(({
    className = ""
}: SelectableImageItemSkeletonProps) => {
    return (
        <div className={`relative w-full min-w-0 flex flex-col gap-3 pb-3 cursor-pointer black-box gradient-before-rounded-sm rounded-sm p-2 animate-pulse ${className}`}>
            <div className="relative w-full min-w-0 aspect-square bg-white/5 black-card gradient-before-rounded-sm rounded-sm">
                <div />
            </div>
            <div className="w-full min-w-0 px-3">
                <div className="h-[var(--text-xs)] rounded-sm w-full bg-white/5" />
            </div>
        </div>
    );
});

SelectableImageItemSkeleton.displayName = "SelectableImageItemSkeleton";

export default SelectableImageItemSkeleton;
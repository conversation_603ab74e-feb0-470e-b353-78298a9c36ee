"use client";

const MetricsGridSkeleton = () => {
    return (
        <div className="relative w-full grid grid-cols-3 border-b border-white/15">
            {[1, 2, 3].map((index) => (
                <div
                    key={index}
                    className="flex flex-col gap-2 items-center py-4"
                >
                    <div className="w-[3rem] h-[var(--text-sm)] bg-white/5 rounded-[3px] animate-pulse" />
                    <div className="w-[4rem] h-[calc(var(--text-2xl)*1.2)] bg-white/5 rounded-[3px] animate-pulse" />
                </div>
            ))}
        </div>
    );
};

export default MetricsGridSkeleton;
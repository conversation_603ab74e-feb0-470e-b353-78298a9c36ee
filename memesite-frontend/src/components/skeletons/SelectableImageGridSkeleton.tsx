"use client";

import { memo } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import SelectableImageItemSkeleton from "./SelectableImageItemSkeleton";

interface SelectableImageGridSkeletonProps {
    className?: string;
    itemCount?: number;
}

const SelectableImageGridSkeleton = memo(({
    className = "grid-cols-6",
    itemCount = 12
}: SelectableImageGridSkeletonProps) => {
    return (
        <AnimationContainer
            className="flex-1 min-h-0 mask-fade-y"
            animationType="fade"
        >
            <div className="relative h-full overflow-y-auto pb-35 pt-3">
                <div className={`grid gap-4 ${className}`}>
                    {Array.from({ length: itemCount }).map((_, index) => (
                        <SelectableImageItemSkeleton 
                            key={index}
                        />
                    ))}
                </div>
            </div>
        </AnimationContainer>
    );
});

SelectableImageGridSkeleton.displayName = "SelectableImageGridSkeleton";

export default SelectableImageGridSkeleton;
"use client";

import { memo } from "react";

const ImageDropZoneSkeleton = memo(() => {
    return (
        <div className="relative w-full h-50 2xl:h-70 3xl:h-80 black-card gradient-before-rounded-sm rounded-sm p-0 overflow-hidden animate-pulse">
            <div className="absolute inset-1 rounded-extra-sm bg-white/5" />
        </div>
    );
});

ImageDropZoneSkeleton.displayName = "ImageDropZoneSkeleton";

export default ImageDropZoneSkeleton;
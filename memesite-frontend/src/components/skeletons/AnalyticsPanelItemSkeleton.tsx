"use client";

const AnalyticsPanelItemSkeleton = () => {
    return (
        <div className="flex flex-col gap-1.5">
            <div className="relative w-full h-[calc(var(--text-2xl)*1.1)]">
                <div className="absolute inset-0 bg-white/5 rounded-[3px] animate-pulse" style={{ width: '100%' }} />
            </div>
            <div className="relative w-full h-[calc(var(--text-2xl)*1.1)]">
                <div className="absolute inset-0 bg-white/5 rounded-[3px] animate-pulse" style={{ width: '75%' }} />
            </div>
            <div className="relative w-full h-[calc(var(--text-2xl)*1.1)]">
                <div className="absolute inset-0 bg-white/5 rounded-[3px] animate-pulse" style={{ width: '50%' }} />
            </div>
            <div className="relative w-full h-[calc(var(--text-2xl)*1.1)]">
                <div className="absolute inset-0 bg-white/5 rounded-[3px] animate-pulse" style={{ width: '25%' }} />
            </div>
        </div>
    );
};

export default AnalyticsPanelItemSkeleton;
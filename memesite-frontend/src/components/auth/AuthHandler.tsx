"use client";

import { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useAuthContextStore } from '@/stores/authContextStore';
import { useProjectStore } from '@/stores/projectStore';
import { useChatStore } from '@/stores/chatStore';
import { useModalStore } from '@/stores/modalStore';
import { createMessageAPI } from '@/utils/api';

export default function AuthHandler() {
    const { isSignedIn, getToken } = useAuth();
    const router = useRouter();
    const {
        hasPendingRequest,
        getPendingRequest,
        clearPendingRequest,
        isProcessingPendingRequest,
        setProcessingPendingRequest
    } = useAuthContextStore();
    const { createProject } = useProjectStore();
    const { clearMessages } = useChatStore();
    const { openModal } = useModalStore();

    useEffect(() => {
        const handlePendingRequest = async () => {
            if (isSignedIn && hasPendingRequest() && !isProcessingPendingRequest) {
                const pendingRequest = getPendingRequest();

                if (pendingRequest) {
                    setProcessingPendingRequest(true);

                    try {
                        clearMessages();

                        const project = await createProject({
                            name: pendingRequest.message
                        }, getToken);

                        if (project) {
                            const token = await getToken({
                                template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
                            });

                            if (token) {
                                const projectResponse = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/projects/${project.id}`, {
                                    headers: {
                                        'Authorization': `Bearer ${token}`,
                                        'Content-Type': 'application/json'
                                    }
                                });

                                if (projectResponse.ok) {
                                    const projectData = await projectResponse.json();
                                    const versionId = projectData.data.activeVersionId;

                                    await createMessageAPI(project.id, versionId, pendingRequest.message, token);
                                }
                            }

                            // Don't clear processing state yet - let the project page handle it
                            clearPendingRequest();

                            router.push(`/projects/${project.id}`);
                        } else {
                            clearPendingRequest();
                            openModal('pricing');
                        }
                    } catch (error) {
                        console.error('Failed to create project from pending request:', error);
                        clearPendingRequest();
                    }
                }
            }
        };

        const timeoutId = setTimeout(handlePendingRequest, 100);

        return () => clearTimeout(timeoutId);
    }, [
        isSignedIn,
        hasPendingRequest,
        getPendingRequest,
        clearPendingRequest,
        isProcessingPendingRequest,
        setProcessingPendingRequest,
        createProject,
        clearMessages,
        getToken,
        router,
        openModal
    ]);

    if (isProcessingPendingRequest) {
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                    <h2 className="text-lg font-medium text-white">
                        Setting up your project...
                    </h2>
                    <p className="text-sm text-gray-400">
                        Please wait while we process your request.
                    </p>
                </div>
            </div>
        );
    }

    return null;
}

"use client";

import { memo, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { MessageSquare } from "lucide-react";
import Button from "@/components/ui/buttons/Button";
import DropdownSidebar from "@/components/ui/sidebar/DropdownSidebar";
import CategoryButton from "@/components/ui/sidebar/CategoryButton";
import { strs as sidebarStrs } from "@/constants/components/sidebar/strs";
import {
  getSidebarCategories,
  getSettingsCategory,
} from "@/constants/components/sidebar/categories";
import AnimationContainer from "./AnimationContainer";
import { useUIStore } from "@/stores/uiStore";

const AppSidebar = memo(() => {
  const router = useRouter();
  const pathname = usePathname();
  const { isSidebarOpen: isOpen, setSidebarOpen } = useUIStore();

  const sidebarCategories = getSidebarCategories(pathname, router);
  const settingsCategory = getSettingsCategory(router);

  // Debug logging
  console.log("AppSidebar render:", { isOpen, pathname });

  // Close sidebar on mobile when clicking outside or on escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        setSidebarOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll on mobile when sidebar is open
      if (window.innerWidth <= 767) {
        document.body.style.overflow = "hidden";
      }
    } else {
      // Restore body scroll
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, setSidebarOpen]);

  // Auto-close sidebar on mobile when route changes
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 767 && isOpen) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isOpen, setSidebarOpen]);

  // Close sidebar on mobile when route changes
  useEffect(() => {
    if (window.innerWidth <= 767 && isOpen) {
      setSidebarOpen(false);
    }
  }, [pathname, isOpen, setSidebarOpen]);

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden backdrop-blur-sm"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-[var(--padding-page-padding)] h-sidebar-height z-50 transition-all duration-700 ease-[cubic-bezier(0.625,0.05,0,1)]
          ${
            // Desktop behavior
            isOpen
              ? "md:left-6 md:w-15"
              : "md:-left-[calc(var(--width-15)+0rem)] md:w-15"
          }
          ${
            // Mobile behavior - full width sidebar with proper spacing
            isOpen ? "left-4 right-4 max-md:w-auto" : "-left-full max-md:w-auto"
          }`}
      >
        <AnimationContainer className="w-full h-full p-6 rounded black-box flex flex-col overflow-hidden">
          <div className="">
            <Button
              className="w-full flex items-center justify-center gap-2"
              styleClassName="black-button"
              onClick={() => router.push("/home")}
            >
              <MessageSquare className="h-[var(--text-sm)] w-auto" />
              {sidebarStrs.buttons.newChat}
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto">
            <DropdownSidebar className="pl-0" categories={sidebarCategories} />
          </div>

          <div className="pt-3 border-t border-white/10">
            <CategoryButton
              category={settingsCategory}
              isActive={false}
              isExpanded={false}
              onClick={() => router.push("/settings")}
            />
          </div>
        </AnimationContainer>
      </div>
    </>
  );
});

AppSidebar.displayName = "AppSidebar";

export default AppSidebar;

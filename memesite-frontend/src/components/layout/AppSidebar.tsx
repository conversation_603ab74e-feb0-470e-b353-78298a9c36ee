"use client";

import { memo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { MessageSquare } from "lucide-react";
import Button from "@/components/ui/buttons/Button";
import DropdownSidebar from "@/components/ui/sidebar/DropdownSidebar";
import CategoryButton from "@/components/ui/sidebar/CategoryButton";
import { strs as sidebarStrs } from "@/constants/components/sidebar/strs";
import {
  getSidebarCategories,
  getSettingsCategory,
} from "@/constants/components/sidebar/categories";
import AnimationContainer from "./AnimationContainer";
import { useUIStore } from "@/stores/uiStore";

const AppSidebar = memo(() => {
  const router = useRouter();
  const pathname = usePathname();
  const { isSidebarOpen: isOpen } = useUIStore();

  const sidebarCategories = getSidebarCategories(pathname, router);
  const settingsCategory = getSettingsCategory(router);

  return (
    <>
      <div
        className={`fixed top-[var(--padding-page-padding)] w-15 h-sidebar-height ${
          isOpen ? "left-6" : "-left-[calc(var(--width-15)+0rem)]"
        }`}
        style={{
          transition: "all 0.8s cubic-bezier(0.625, 0.05, 0, 1)",
        }}
      >
        <AnimationContainer className="w-full h-full p-6 rounded black-box flex flex-col overflow-hidden">
          <div className="">
            <Button
              className="w-full flex items-center justify-center gap-2"
              styleClassName="black-button"
              onClick={() => router.push("/home")}
            >
              <MessageSquare className="h-[var(--text-sm)] w-auto" />
              {sidebarStrs.buttons.newChat}
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto">
            <DropdownSidebar className="pl-0" categories={sidebarCategories} />
          </div>

          <div className="pt-3 border-t border-white/10">
            <CategoryButton
              category={settingsCategory}
              isActive={false}
              isExpanded={false}
              onClick={() => router.push("/settings")}
            />
          </div>
        </AnimationContainer>
      </div>
    </>
  );
});

AppSidebar.displayName = "AppSidebar";

export default AppSidebar;

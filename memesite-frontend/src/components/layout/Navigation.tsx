"use client";

import Image from "next/image";
import Link from "next/link";
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
  useAuth,
} from "@clerk/nextjs";
import Button from "../ui/buttons/Button";
import { strs } from "@/constants/components/navigation";
import { usePathname } from "next/navigation";
import AnimationContainer from "./AnimationContainer";
import { useUIStore } from "@/stores/uiStore";
import { ArrowUpRight, Menu, X } from "lucide-react";

export default function Navigation() {
  const pathname = usePathname();
  const { isSignedIn } = useAuth();
  const { toggleSidebar, isSidebarOpen } = useUIStore();
  const isHomePage = pathname === "/" || pathname === "/home";
  const shouldShowCenteredNav = isHomePage && !isSignedIn;
  const hasSidebar =
    isSignedIn && (pathname === "/home" || pathname === "/projects");

  // For testing - temporarily show hamburger on all signed-in pages
  const showHamburgerForTesting = isSignedIn;

  // Debug logging
  console.log("Navigation Debug:", {
    pathname,
    isSignedIn,
    hasSidebar,
    isSidebarOpen,
    shouldShowCenteredNav,
  });

  return (
    <AnimationContainer
      className={
        shouldShowCenteredNav
          ? "!fixed top-6 left-1/2 -translate-x-1/2 z-[60] black-box black-box-rounded-full rounded-full w-90 lg:w-75 h-[3.5rem] flex justify-between items-center pl-8 pr-[0.75rem] gap-4"
          : "fixed z-[60] w-full h-page-padding flex justify-between items-center p-6 gap-4"
      }
    >
      <div className="flex items-center gap-3">
        {/* Hamburger menu for mobile - ALWAYS SHOW FOR DEBUGGING */}
        {true && (
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("Hamburger clicked!", {
                isSidebarOpen,
                hasSidebar,
                isSignedIn,
              });
              toggleSidebar();
            }}
            className="p-3 rounded-lg hover:bg-white/10 transition-colors duration-200 relative z-[70] cursor-pointer touch-manipulation bg-red-500/20 border border-red-500"
            aria-label={isSidebarOpen ? "Close menu" : "Open menu"}
          >
            {isSidebarOpen ? (
              <X className="h-5 w-5 text-white pointer-events-none" />
            ) : (
              <Menu className="h-5 w-5 text-white pointer-events-none" />
            )}
          </button>
        )}

        {/* Logo with desktop sidebar toggle */}
        {hasSidebar ? (
          <div
            className="relative group hidden md:flex items-center gap-1 cursor-pointer"
            onClick={toggleSidebar}
          >
            <Image
              src="/brand/memesitelogo.svg"
              alt={strs.logoAlt}
              width={100}
              height={100}
              className="h-5 w-auto"
            />
            <ArrowUpRight
              className="h-5 w-auto text-white -mb-2 opacity-0 scale-50 rotate-[-25deg] group-hover:opacity-100 group-hover:scale-100 group-hover:rotate-0 transition-all duration-300"
              strokeWidth={2.5}
            />
          </div>
        ) : (
          <Link href="/home">
            <Image
              src="/brand/memesitelogo.svg"
              alt={strs.logoAlt}
              width={100}
              height={100}
              className="h-5 w-auto cursor-pointer"
            />
          </Link>
        )}

        {/* Logo for mobile when sidebar is available (non-clickable) */}
        {hasSidebar && (
          <Link href="/home" className="md:hidden">
            <Image
              src="/brand/memesitelogo.svg"
              alt={strs.logoAlt}
              width={100}
              height={100}
              className="h-5 w-auto"
            />
          </Link>
        )}
      </div>
      <div className="flex items-center gap-2">
        <SignedOut>
          <SignInButton>
            <Button styleClassName="black-button" className="px-6">
              {strs.logIn}
            </Button>
          </SignInButton>
          <SignUpButton>
            <Button styleClassName="black-button" className="px-6">
              {strs.signUp}
            </Button>
          </SignUpButton>
        </SignedOut>
        <SignedIn>
          <UserButton
            appearance={{
              elements: {
                userButtonPopoverFooter: {
                  display: "none",
                },
              },
            }}
          />
        </SignedIn>
      </div>
    </AnimationContainer>
  );
}

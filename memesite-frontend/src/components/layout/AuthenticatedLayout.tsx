"use client";

import { useAuth } from "@clerk/nextjs";
import { usePathname } from "next/navigation";
import AppSidebar from "./AppSidebar";
import { ProjectsProvider } from "@/components/widgets/projects/ProjectContext";
import { useUIStore } from "@/stores/uiStore";

export default function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isSignedIn } = useAuth();
  const pathname = usePathname();
  const { isSidebarOpen } = useUIStore();

  const shouldShowSidebar =
    isSignedIn && (pathname === "/home" || pathname === "/projects");

  if (!shouldShowSidebar) {
    return <>{children}</>;
  }

  return (
    <ProjectsProvider>
      <>
        <div className="fixed z-50">
          <AppSidebar />
        </div>
        <div
          className={
            isSidebarOpen ? "md:ml-[var(--width-sidebar-padding)] ml-0" : "ml-0"
          }
          style={{
            transition: "margin-left 0.8s cubic-bezier(0.625, 0.05, 0, 1)",
          }}
        >
          {children}
        </div>
      </>
    </ProjectsProvider>
  );
}

"use client";

import { memo, useState, useEffect, useCallback } from "react";
import { useAuth } from "@clerk/nextjs";
import Image from "next/image";
import SelectorButton from "@/components/ui/buttons/SelectorButton";
import Button from "@/components/ui/buttons/Button";
import ImageDropZone from "./images/ImageDropZone";
import SelectableImageGrid from "./images/SelectableImageGrid";
import LabView from "./views/LabView";
import ConfirmationPopup from "./states/ConfirmationPopup";
import { useImageEditOverlay } from "@/hooks/useImageEditOverlay";
import { useChatStore } from "@/stores/chatStore";
import { useImageEditConfirmations } from "@/hooks/useImageEditConfirmations";
import { strs } from "@/constants/pages/projects/strs";
import { LabImage } from "@/data/labImages";
import IconWithBackground from "@/components/ui/buttons/IconWithBackground";
import { ArrowDownUp } from "lucide-react";

interface ImageEditOverlayProps {
    isVisible: boolean;
    isFromLabFlow?: boolean;
    selectedImage?: LabImage | null;
    currentIframeImage?: LabImage | null;
    onIframeImageReplace?: (newImage: LabImage, targetSelector?: string) => void;
}

const editOptions = [
    { value: "manage", label: strs.imageEditOverlay.tabs.manage },
    { value: "generate", label: strs.imageEditOverlay.tabs.generate }
];

const ImageEditOverlay = memo(({ isVisible, isFromLabFlow = false, selectedImage, currentIframeImage, onIframeImageReplace }: ImageEditOverlayProps) => {
    const { getToken } = useAuth();
    const [showLabFlowContent, setShowLabFlowContent] = useState(false);
    const [persistedSelectedImage, setPersistedSelectedImage] = useState<LabImage | null>(null);
    const [isGridLoading, setIsGridLoading] = useState(true);
    const [hasInitialLoad, setHasInitialLoad] = useState(false);

    const handleImageCrop = (croppedImage: string) => {
        if (!currentImage) return;

        const updatedImage = {
            ...currentImage,
            url: croppedImage,
            src: croppedImage
        };

        setCurrentImage(updatedImage);

        if (showLabFlowContent && persistedSelectedImage) {
            setPersistedSelectedImage(updatedImage);
        }

        if (onIframeImageReplace) {
            onIframeImageReplace(updatedImage);
        }
    };

    const {
        activeTab,
        setActiveTab,
        currentImage,
        setCurrentImage,
        uploadedImages,
        fileInputRef,
        selectedImages,
        isDragging,
        handleDragOver,
        handleDragLeave,
        handleDrop,
        handleFileSelect,
        handleRemoveImage,
        handleImageToggle,
        openFilePicker,
        handleGeneratedImageUse,
        clearSelection
    } = useImageEditOverlay();

    const {
        labMessages,
        isLabLoading,
        isLabResponseReady,
        sendLabMessage,
        handleLabAnimationComplete
    } = useChatStore();

    const {
        showReplaceConfirmation,
        showRemoveConfirmation,
        handleImageToggleWithConfirmation,
        handleConfirmReplace,
        handleCancelReplace,
        handleRemoveClick,
        handleConfirmRemove,
        handleCancelRemove,
        handleCancel,
        handleReplace
    } = useImageEditConfirmations({
        uploadedImages,
        selectedImages,
        handleImageToggle,
        handleRemoveImage,
        setCurrentImage,
        currentImage,
        selectedImage,
        onIframeImageReplace,
        clearSelection
    });

    const handleLabMessage = useCallback((content: string) => {
        sendLabMessage(content, getToken);
    }, [sendLabMessage, getToken]);

    useEffect(() => {
        if (isVisible) {
            if (isFromLabFlow && selectedImage) {
                setShowLabFlowContent(true);
                setPersistedSelectedImage(selectedImage);
            }
            if (currentIframeImage) {
                setCurrentImage(currentIframeImage);
            }

            if (!hasInitialLoad) {
                setIsGridLoading(true);
                const timer = setTimeout(() => {
                    setIsGridLoading(false);
                    setHasInitialLoad(true);
                }, 1500);

                return () => clearTimeout(timer);
            } else {
                setIsGridLoading(false);
            }
        } else {
            setTimeout(() => {
                setShowLabFlowContent(false);
                setPersistedSelectedImage(null);
            }, 300);
        }
    }, [isVisible, isFromLabFlow, selectedImage, currentIframeImage, setCurrentImage, hasInitialLoad]);

    return (
        <>
            <div className={`absolute inset-0 z-50 transition-all duration-300 ${isVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
                } backdrop-blur-xs`}>
                <div className="absolute inset-0 flex flex-col">
                    <div className="relative w-full h-full black-box !bg-dark gradient-before-rounded-sm rounded-sm p-5 flex flex-col gap-3">
                        {!showLabFlowContent && (
                            <SelectorButton
                                options={editOptions}
                                activeValue={activeTab}
                                onValueChange={setActiveTab}
                            />
                        )}

                        {activeTab === "manage" ? (
                            <div className="flex-1 flex flex-col justify-between gap-0 overflow-hidden relative">
                                <ImageDropZone
                                    isDragging={isDragging}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                    onAddFiles={openFilePicker}
                                    fileInputRef={fileInputRef}
                                    onFileSelect={handleFileSelect}
                                    selectedImage={currentImage}
                                    onRemoveImage={handleRemoveClick}
                                    onImageCrop={handleImageCrop}
                                />
                                {showLabFlowContent && persistedSelectedImage ? (
                                    <>
                                        <div className="relative w-full h-30 mt-4 gradient-before-rounded-sm rounded-sm flex items-center justify-center" >
                                            <IconWithBackground icon={ArrowDownUp} className="h-20" />
                                        </div>
                                        <div className="relative w-full flex flex-col gap-3">
                                            <div className="relative w-full h-50 2xl:h-70 3xl:h-80 flex items-center justify-center black-card gradient-before-rounded-sm rounded-sm">
                                                <div className="absolute inset-1 rounded-extra-sm overflow-hidden" >
                                                    <Image
                                                        src={persistedSelectedImage.url || persistedSelectedImage.src || ''}
                                                        alt={persistedSelectedImage.name || persistedSelectedImage.key || 'Selected image'}
                                                        width={500}
                                                        height={500}
                                                        className=" w-full h-full object-cover"
                                                    />
                                                </div>
                                            </div>
                                            <div className="flex items-center justify-center gap-4">
                                                <Button
                                                    styleClassName="purple-button"
                                                    className="w-1/2"
                                                    onClick={handleReplace}
                                                >
                                                    {strs.imageEditOverlay.labFlow.replace}
                                                </Button>
                                                <Button
                                                    styleClassName="black-button"
                                                    className="w-1/2"
                                                    onClick={handleCancel}
                                                >
                                                    {strs.imageEditOverlay.labFlow.cancel}
                                                </Button>
                                            </div>
                                        </div>
                                    </>
                                ) : (
                                    <SelectableImageGrid
                                        images={uploadedImages}
                                        selectedImages={selectedImages}
                                        onToggleSelection={handleImageToggleWithConfirmation}
                                        className="grid-cols-2"
                                        showSelectText={true}
                                        isLoading={isGridLoading}
                                    />
                                )}
                            </div>
                        ) : (
                            <div className="flex-1 flex flex-col overflow-hidden">
                                <LabView
                                    messages={labMessages}
                                    isLoading={isLabLoading}
                                    isResponseReady={isLabResponseReady}
                                    onSendMessage={handleLabMessage}
                                    onAnimationComplete={handleLabAnimationComplete}
                                    onAllTasksCompleted={undefined}
                                    viewContext="imageEdit"
                                    onImageApply={() => setActiveTab("manage")}
                                    onImageUse={handleGeneratedImageUse}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <ConfirmationPopup
                isVisible={showReplaceConfirmation}
                onCancel={handleCancelReplace}
                onConfirm={handleConfirmReplace}
            />
            <ConfirmationPopup
                isVisible={showRemoveConfirmation}
                onCancel={handleCancelRemove}
                onConfirm={handleConfirmRemove}
                title={strs.confirmationPopup.removeImage.title}
                description={strs.confirmationPopup.removeImage.description}
                confirmText={strs.confirmationPopup.removeImage.confirmText}
            />
        </>
    );
});

ImageEditOverlay.displayName = "ImageEditOverlay";

export default ImageEditOverlay;
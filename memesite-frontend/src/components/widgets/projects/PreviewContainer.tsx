"use client";

import { memo, useState, useEffect } from "react";
import { useDevicePreview } from "@/hooks/useDevicePreview";
import { useEditModeStore } from "@/stores/editModeStore";
import { useUIStore } from "@/stores/uiStore";
import { ViewType } from "@/types/chat";
import { ProjectWithVersions } from "@/types/project";
import PreviewToolbar from "./preview/PreviewToolbar";
import PreviewFrame from "./preview/PreviewFrame";
import LabOverlay from "./preview/LabOverlay";
interface PreviewContainerProps {
    showPreview: boolean;
    previewStage: number;
    clonedUrl?: string;
    previewUrl?: string | null;
    onSendMessage?: (content: string) => void;
    isLoading?: boolean;
    activeView?: string;
    setActiveView?: (view: ViewType) => void;
    onShowImageEditOverlay?: () => void;
    onHideImageEditOverlay?: () => void;
    project?: ProjectWithVersions | null;
    mutateProject?: () => void;
}

const PreviewContainer = memo(({ showPreview, previewStage, clonedUrl, previewUrl, onSendMessage, isLoading, activeView, setActiveView, onShowImageEditOverlay, onHideImageEditOverlay, project, mutateProject }: PreviewContainerProps) => {
    const { activeDevice, setActiveDevice, deviceClasses } = useDevicePreview();
    const { isEditMode } = useEditModeStore();
    const { setAnalyticsVisible } = useUIStore();
    const [showAnalytics, setShowAnalytics] = useState(false);
    
    useEffect(() => {
        setAnalyticsVisible(showAnalytics);
    }, [showAnalytics, setAnalyticsVisible]);

    return (
        <div className="relative w-full h-full col-span-7 black-box gradient-before-rounded rounded p-5 gap-5 flex flex-col">
            <PreviewToolbar
                activeDevice={activeDevice}
                onDeviceChange={setActiveDevice}
                isLoading={isLoading}
                onAnalyticsClick={() => setShowAnalytics(!showAnalytics)}
                isAnalyticsVisible={showAnalytics}
                previewUrl={previewUrl}
                clonedUrl={clonedUrl}
                project={project}
                mutateProject={mutateProject}
            />
            <PreviewFrame
                deviceClasses={deviceClasses}
                activeDevice={activeDevice}
                showLoadingState={false}
                previewStage={previewStage}
                clonedUrl={clonedUrl}
                previewUrl={previewUrl}
                publishUrl={project?.publish_url}
                isEditMode={isEditMode}
                onSendMessage={onSendMessage}
                isLoading={isLoading}
                activeView={activeView}
                onIframeClick={onShowImageEditOverlay}
                onNonImageClick={onHideImageEditOverlay}
                showAnalytics={showAnalytics}
            />
            <div className={`absolute top-0 left-0 w-full h-full p-5 ${activeView !== 'lab' ? 'pointer-events-none' : ''}`}>
                <div className="relative w-full h-full" >
                    <LabOverlay isVisible={activeView === 'lab'} onViewChange={setActiveView} />
                </div>
            </div>
        </div>
    );
});

PreviewContainer.displayName = "PreviewContainer";

export default PreviewContainer;
"use client";

import AnalyticsPanelItem from "./AnalyticsPanelItem";
import { generatePanelsData } from "@/data/analyticsPanelsData";
import { analyticsStrs } from "@/constants/pages/projects/analytics";

interface AnalyticsPanelGridProps {
    timeRange: string;
    metricType: 'visitors' | 'pageViews' | 'bounceRate';
    isVisible?: boolean;
}

const AnalyticsPanelGrid = ({ timeRange, metricType, isVisible = false }: AnalyticsPanelGridProps) => {
    const data = generatePanelsData(timeRange, metricType);
    const { panelTitles, metricLabels } = analyticsStrs;
    const metricLabel = metricLabels[metricType];
    
    return (
        <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-5 my-5">
            <AnalyticsPanelItem title={panelTitles.pages} items={data.pages} metricLabel={metricLabel} isVisible={isVisible} />
            <AnalyticsPanelItem title={panelTitles.devices} items={data.devices} metricLabel={metricLabel} isVisible={isVisible} />
            <AnalyticsPanelItem title={panelTitles.referrers} items={data.referrers} metricLabel={metricLabel} isVisible={isVisible} />
            <AnalyticsPanelItem title={panelTitles.browsers} items={data.browsers} metricLabel={metricLabel} isVisible={isVisible} />
            <AnalyticsPanelItem title={panelTitles.countries} items={data.countries} metricLabel={metricLabel} isVisible={isVisible} />
            <AnalyticsPanelItem title={panelTitles.os} items={data.os} metricLabel={metricLabel} isVisible={isVisible} />
        </div>
    );
};

export default AnalyticsPanelGrid;
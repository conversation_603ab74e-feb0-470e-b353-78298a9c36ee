"use client";

import { useState, useEffect } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import AnalyticsPanelItemSkeleton from "@/components/skeletons/AnalyticsPanelItemSkeleton";

interface PanelItem {
    name: string;
    value: string;
}

interface AnalyticsPanelProps {
    title: string;
    items: PanelItem[];
    metricLabel: string;
    isVisible?: boolean;
}

const AnalyticsPanelItem = ({ title, items, metricLabel, isVisible = false }: AnalyticsPanelProps) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
    const maxValue = Math.max(...items.map(item => parseInt(item.value)));

    useEffect(() => {
        if (isVisible && !hasLoadedOnce) {
            setIsLoading(true);
            const timer = setTimeout(() => {
                setIsLoading(false);
                setHasLoadedOnce(true);
            }, 1000);
            
            return () => clearTimeout(timer);
        }
    }, [isVisible, hasLoadedOnce]);

    return (
        <div className="relative black-box gradient-before-rounded-sm p-5">
            <div className="flex flex-col gap-4">
                <div className="w-full flex justify-between items-center" >
                    <h3 className="text-xs text-white tracking-wider">{title}</h3>
                    <AnimationContainer
                        key={metricLabel}
                        animationType="fade"
                        className="text-xs text-white/75 tracking-wider"
                    >
                        {metricLabel}
                    </AnimationContainer>
                </div>
                {isLoading ? (
                    <AnalyticsPanelItemSkeleton />
                ) : (
                    <div className="flex flex-col gap-1.5">
                        {items.map((item) => {
                            const relativeWidth = (parseInt(item.value) / maxValue) * 100;
                            return (
                                <div key={item.name} className="relative w-full h-[calc(var(--text-2xl)*1.1)] group">
                                    <div
                                        className="absolute inset-0 bg-white/5 rounded-[3px] transition-all duration-500"
                                        style={{ width: `${relativeWidth}%` }}
                                    />
                                    <AnimationContainer
                                        key={item.value}
                                        animationType="fade"
                                        className="relative flex w-full justify-between items-center h-full px-2"
                                    >
                                        <span className="text-xs text-white/75 truncate">{item.name}</span>
                                        <span className="text-xs text-white font-medium ml-2">{item.value}</span>
                                    </AnimationContainer>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AnalyticsPanelItem;
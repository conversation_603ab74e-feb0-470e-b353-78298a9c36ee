"use client";

interface MenuItemProps {
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    onClick?: () => void;
    isSelected?: boolean;
}

const MenuItem = ({
    icon: Icon,
    label,
    onClick,
    isSelected = false
}: MenuItemProps) => (
    <button
        className={`group cursor-pointer flex w-full items-center gap-3 rounded-xl p-3 text-xs font-medium hover:bg-white/5 transition-colors duration-400 ${
            isSelected ? 'bg-white/5' : ''
        }`}
        onClick={onClick}
    >
        <Icon className="h-[var(--text-base)] w-auto" />
        <span>{label}</span>
    </button>
);

export default MenuItem;
"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, Calendar } from "lucide-react";
import IconButton from "@/components/ui/buttons/IconButton";
import AnimationContainer from "@/components/layout/AnimationContainer";
import MenuItem from "./MenuItem";
import { analyticsStrs } from "@/constants/pages/projects/analytics";

interface TimeSelectorProps {
    selectedPeriod: string;
    selectedValue: string;
    onPeriodSelect: (option: { label: string; value: string }) => void;
}

const TimeSelector = ({ selectedPeriod, onPeriodSelect }: TimeSelectorProps) => {
    const [open, setOpen] = useState(false);
    const triggerRef = useRef<HTMLDivElement | null>(null);
    const menuRef = useRef<HTMLDivElement | null>(null);

    const openClasses = [
        "opacity-100",
        "translate-y-0",
        "scale-100",
        "pointer-events-auto",
    ];
    const closedClasses = [
        "opacity-0",
        "translate-y-1",
        "scale-95",
        "pointer-events-none",
    ];

    const toggleMenu = () => setOpen((prev) => !prev);

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (
                menuRef.current &&
                !menuRef.current.contains(e.target as Node) &&
                triggerRef.current &&
                !triggerRef.current.contains(e.target as Node)
            ) {
                setOpen(false);
            }
        };

        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") setOpen(false);
        };

        document.addEventListener("click", handleClickOutside);
        document.addEventListener("keydown", handleEscape);

        return () => {
            document.removeEventListener("click", handleClickOutside);
            document.removeEventListener("keydown", handleEscape);
        };
    }, []);

    const handleSelect = (option: { label: string; value: string }) => {
        onPeriodSelect(option);
        setOpen(false);
    };

    return (
        <div className="relative" ref={triggerRef}>
            <IconButton
                icon="/icons/calendar.svg"
                alt=""
                useChildren
                onClick={toggleMenu}
            >
                <AnimationContainer
                    key={selectedPeriod}
                    className="flex items-center gap-4 ml-3 text-white"
                    animationType="fade"
                >
                    <span className="text-xs">{selectedPeriod}</span>
                    <ChevronDown className="h-[var(--text-sm)] w-auto" />
                </AnimationContainer>
            </IconButton>

            <div
                ref={menuRef}
                className={`absolute z-10 right-0 top-12 w-full rounded-md bg-black black-card gradient-before-rounded p-1.5 origin-top-right transition ease-out duration-200 ${
                    open ? openClasses.join(" ") : closedClasses.join(" ")
                }`}
            >
                <div className="flex flex-col gap-0">
                    {analyticsStrs.timeOptions.map((option) => (
                        <MenuItem
                            key={option.value}
                            icon={Calendar}
                            label={option.label}
                            onClick={() => handleSelect(option)}
                            isSelected={selectedPeriod === option.label}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default TimeSelector;
"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts"

import { formatNumber } from "@/utils/formatters";
import CustomTooltip from "./CustomTooltip";
import { filterChartData, getDataKey, calculateYAxisWidth } from "./utils";

type MetricType = 'visitors' | 'pageViews' | 'bounceRate';

interface LineChartProps {
    timeRange?: string;
    metricType?: MetricType;
}

export function LineChart({ timeRange = "7d", metricType = "visitors" }: LineChartProps) {
    const filteredData = filterChartData(timeRange);
    const dataKey = getDataKey(metricType);
    const yAxisWidth = calculateYAxisWidth(filteredData, metricType);

    return (
        <div className="w-full h-full [&_*]:outline-none [&_*]:focus:outline-none">
            <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                    data={filteredData}
                    margin={{ top: 10, right: 5, left: 0, bottom: 14 }}
                >
                    <defs>
                        <linearGradient id="fillVisitors" x1="0" y1="0" x2="0" y2="1">
                            <stop
                                offset="5%"
                                stopColor="#C4ACFF"
                                stopOpacity={0.8}
                            />
                            <stop
                                offset="95%"
                                stopColor="#C4ACFF"
                                stopOpacity={0}
                            />
                        </linearGradient>
                        <linearGradient id="fadeGradient" x1="0" y1="0" x2="1" y2="0">
                            <stop offset="0%" stopColor="black" stopOpacity={0} />
                            <stop offset="2.5%" stopColor="black" stopOpacity={0} />
                            <stop offset="10%" stopColor="white" stopOpacity={1} />
                            <stop offset="90%" stopColor="white" stopOpacity={1} />
                            <stop offset="97.5%" stopColor="black" stopOpacity={0} />
                            <stop offset="100%" stopColor="black" stopOpacity={0} />
                        </linearGradient>
                        <mask id="fadeMask">
                            <rect x="0" y="0" width="100%" height="100%" fill="url(#fadeGradient)" />
                        </mask>
                    </defs>
                    <CartesianGrid
                        vertical={false}
                        stroke="rgba(255, 255, 255, 0.075)"
                    />
                    <XAxis
                        dataKey="date"
                        tickLine={false}
                        axisLine={false}
                        tick={{ fill: 'rgba(255, 255, 255, 0.5)', fontSize: 10 }}
                        tickMargin={20}
                        minTickGap={32}
                        tickFormatter={(value) => {
                            const date = new Date(value)
                            return date.toLocaleDateString("en-US", {
                                month: "short",
                                day: "numeric",
                            })
                        }}
                    />
                    <YAxis
                        tickLine={false}
                        axisLine={false}
                        tick={{ fill: 'rgba(255, 255, 255, 0.5)', fontSize: 10 }}
                        width={yAxisWidth}
                        tickFormatter={(value) => metricType === 'bounceRate' ? `${value}%` : formatNumber(value)}
                    />
                    <Tooltip
                        content={<CustomTooltip metricType={metricType} />}
                        cursor={{ stroke: 'rgba(255, 255, 255, 0.1)' }}
                    />
                    <Area
                        dataKey={dataKey}
                        type="monotone"
                        fill="none"
                        stroke="#C4ACFF"
                        strokeWidth={2}
                        mask="url(#fadeMask)"
                        activeDot={{ fill: '#FFFFFF', r: 5 }}
                    />
                </AreaChart>
            </ResponsiveContainer>
        </div>
    )
}
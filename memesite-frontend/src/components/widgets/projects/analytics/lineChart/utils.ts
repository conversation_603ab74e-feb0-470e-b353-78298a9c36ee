import { chartData } from "@/data/analyticsData";
import { formatNumber } from "@/utils/formatters";

type MetricType = 'visitors' | 'pageViews' | 'bounceRate';

export const filterChartData = (timeRange: string) => {
    return chartData.filter((item) => {
        const date = new Date(item.date);
        const today = new Date();
        let daysToSubtract = 7;
        if (timeRange === "30d") {
            daysToSubtract = 30;
        } else if (timeRange === "90d") {
            daysToSubtract = 90;
        }
        const startDate = new Date(today);
        startDate.setDate(startDate.getDate() - daysToSubtract);
        return date >= startDate;
    });
};

export const getDataKey = (metricType: MetricType): keyof Pick<ChartDataItem, 'visitors' | 'pageViews' | 'bounceRate'> => {
    return metricType === 'pageViews' ? 'pageViews' : metricType === 'bounceRate' ? 'bounceRate' : 'visitors';
};

interface ChartDataItem {
    date: string;
    visitors: number;
    pageViews: number;
    bounceRate: number;
}

export const calculateYAxisWidth = (filteredData: ChartDataItem[], metricType: MetricType): number => {
    const dataKey = getDataKey(metricType);
    const maxValue = Math.max(...filteredData.map(item => item[dataKey]));
    const formattedMax = metricType === 'bounceRate' ? `${maxValue}%` : formatNumber(maxValue);
    
    let multiplier = 7;
    if (formattedMax.length === 2) {
        multiplier = 8;
    } else if (formattedMax.length === 3) {
        multiplier = 10;
    }
    
    return formattedMax.length * multiplier;
};
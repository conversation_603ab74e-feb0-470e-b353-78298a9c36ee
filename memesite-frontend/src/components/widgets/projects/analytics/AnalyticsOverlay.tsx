"use client";

import { memo, useState, useMemo, useEffect } from "react";
import { ViewType } from "@/types/chat";
import { LineChart } from "./lineChart/LineChart";
import TimeSelector from "./components/TimeSelector";
import MetricsGrid from "./components/MetricsGrid";
import AnalyticsPanelGrid from "./panels/AnalyticsPanelGrid";
import LineChartSkeleton from "@/components/skeletons/LineChartSkeleton";
import MetricsGridSkeleton from "@/components/skeletons/MetricsGridSkeleton";
import { analyticsStrs } from "@/constants/pages/projects/analytics";
import { generateOnlineCount } from "@/data/analyticsOnlineData";
import AnimationContainer from "@/components/layout/AnimationContainer";

interface AnalyticsOverlayProps {
    isVisible: boolean;
    onViewChange?: (view: ViewType) => void;
}

const AnalyticsOverlay = memo(({ isVisible }: AnalyticsOverlayProps) => {
    const [selectedPeriod, setSelectedPeriod] = useState("Last 7 Days");
    const [selectedValue, setSelectedValue] = useState("7d");
    const [activeMetric, setActiveMetric] = useState(0);
    const [isChartLoading, setIsChartLoading] = useState(true);
    const [hasChartLoadedOnce, setHasChartLoadedOnce] = useState(false);

    const onlineCount = useMemo(() => generateOnlineCount(), []);

    useEffect(() => {
        if (isVisible && !hasChartLoadedOnce) {
            setIsChartLoading(true);
            const timer = setTimeout(() => {
                setIsChartLoading(false);
                setHasChartLoadedOnce(true);
            }, 1500);

            return () => clearTimeout(timer);
        }
    }, [isVisible, hasChartLoadedOnce]);

    const handlePeriodSelect = (option: { label: string; value: string }) => {
        setSelectedPeriod(option.label);
        setSelectedValue(option.value);
    };

    return (
        <div
            className={`absolute inset-0 w-full h-full flex flex-col gap-0 bg-background rounded-sm z-10 p-5 transition-opacity duration-300 ${isVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
                }`}
        >
            <div className="w-full flex items-center justify-between" >
                <div className="flex items-center gap-5" >
                    <h2 className="text-xl text-white" >{analyticsStrs.title}</h2>
                    <div className="h-[var(--text-base)] w-px bg-white/25" />
                    <div className="flex items-center gap-2" >
                        <div
                            className={`h-1.5 aspect-square rounded-full ${onlineCount > 0 ? 'bg-green-600' : 'bg-white/5'}`}
                            style={onlineCount > 0 ? {
                                animation: 'circleAnimation 2s ease-in-out infinite'
                            } : undefined}
                        />
                        <h3 className="text-xs text-white" >{onlineCount} {analyticsStrs.onlineLabel}</h3>
                    </div>
                </div>
                <TimeSelector
                    selectedPeriod={selectedPeriod}
                    selectedValue={selectedValue}
                    onPeriodSelect={handlePeriodSelect}
                />
            </div>
            <div className="h-full min-h-0 mask-fade-y" >
                <div className="h-full overflow-y-auto">
                    <div className="relative w-full h-fit flex flex-col gap-5 mt-5 overflow-hidden black-box gradient-before-rounded-sm p-5 pt-0">
                        {isChartLoading ? (
                            <MetricsGridSkeleton />
                        ) : (
                            <AnimationContainer animationType="fade" className="w-full">
                                <MetricsGrid
                                    activeMetric={activeMetric}
                                    onMetricSelect={setActiveMetric}
                                    timeRange={selectedValue}
                                />
                            </AnimationContainer>
                        )}
                        <div className="relative w-full h-80 min-h-0 overflow-hidden" >
                            {isChartLoading ? (
                                <LineChartSkeleton />
                            ) : (
                                <AnimationContainer animationType="fade" className="w-full h-full">
                                    <LineChart
                                        timeRange={selectedValue}
                                        metricType={analyticsStrs.metrics[activeMetric].key as 'visitors' | 'pageViews' | 'bounceRate'}
                                    />
                                </AnimationContainer>
                            )}
                        </div>
                    </div>
                    <AnalyticsPanelGrid
                        timeRange={selectedValue}
                        metricType={analyticsStrs.metrics[activeMetric].key as 'visitors' | 'pageViews' | 'bounceRate'}
                        isVisible={isVisible}
                    />
                </div>
            </div>
        </div>
    );
});

AnalyticsOverlay.displayName = "AnalyticsOverlay";

export default AnalyticsOverlay;
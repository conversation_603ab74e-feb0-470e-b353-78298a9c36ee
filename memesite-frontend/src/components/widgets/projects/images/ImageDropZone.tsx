
"use client";

import { memo, DragEvent, useState } from "react";
import Image from "next/image";
import AnimationContainer from "@/components/layout/AnimationContainer";
import IconButton from "@/components/ui/buttons/IconButton";
import Button from "@/components/ui/buttons/Button";
import ImageDropZoneSkeleton from "@/components/skeletons/ImageDropZoneSkeleton";
import ImageCropOverlay from "@/components/widgets/projects/images/imageCropOverlay/ImageCropOverlay";
import { strs } from "@/constants/pages/projects/strs";
import { LabImage } from "@/data/labImages";

interface ImageDropZoneProps {
    isDragging: boolean;
    onDragOver: (e: DragEvent<HTMLDivElement>) => void;
    onDragLeave: (e: DragEvent<HTMLDivElement>) => void;
    onDrop: (e: DragEvent<HTMLDivElement>) => void;
    onAddFiles: () => void;
    fileInputRef: React.RefObject<HTMLInputElement | null>;
    onFileSelect: (files: FileList | null) => void;
    selectedImage?: LabImage | null;
    onRemoveImage?: () => void;
    onImageCrop?: (croppedImage: string) => void;
    isLoading?: boolean;
}

const ImageDropZone = memo(({
    isDragging,
    onDragOver,
    onDragLeave,
    onDrop,
    onAddFiles,
    fileInputRef,
    onFileSelect,
    selectedImage,
    onRemoveImage,
    onImageCrop,
    isLoading = false
}: ImageDropZoneProps) => {
    const [showOverlay, setShowOverlay] = useState(false);
    const [showCropOverlay, setShowCropOverlay] = useState(false);

    if (isLoading) {
        return <ImageDropZoneSkeleton />;
    }

    return (
        <>
            <div
                className={`relative w-full black-card gradient-before-rounded-sm rounded-sm h-50 2xl:h-70 3xl:h-80 ${selectedImage ? 'p-0 overflow-hidden' : 'p-6'
                    } border border-transparent transition-all ${isDragging ? 'border-white/20' : ''
                    }`}
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                onDrop={onDrop}
                onMouseEnter={() => selectedImage && setShowOverlay(true)}
                onMouseLeave={() => selectedImage && setShowOverlay(false)}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => onFileSelect(e.target.files)}
                />

                <AnimationContainer
                    key={selectedImage ? 'image' : 'empty'}
                    className="relative w-full h-full flex flex-col items-center justify-center"
                    animationType="fade"
                >
                    {selectedImage ? (
                        <div className="absolute inset-1 rounded-extra-sm overflow-hidden" >
                            <Image
                                src={selectedImage.url || selectedImage.src || ''}
                                alt={selectedImage.name || selectedImage.key || 'Selected image'}
                                width={500}
                                height={500}
                                className="w-full h-full object-cover"
                                unoptimized={(selectedImage.src || '').startsWith('data:')}
                            />
                            <div className={`absolute inset-0 bg-black/80 flex items-center justify-center gap-3 transition-opacity duration-300 ${showOverlay ? 'opacity-100' : 'opacity-0'
                                }`}>
                                <Button
                                    styleClassName="black-button"
                                    onClick={onRemoveImage}
                                >
                                    {strs.lab.dropZone.remove}
                                </Button>
                                <Button
                                    styleClassName="black-button"
                                    onClick={() => setShowCropOverlay(true)}
                                >
                                    {strs.lab.dropZone.crop}
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="flex items-center justify-center gap-3">
                                <IconButton
                                    icon="/icons/clone.svg"
                                    alt={strs.lab.dropZone.addFiles}
                                    label={strs.lab.dropZone.addFiles}
                                    onClick={onAddFiles}
                                />
                            </div>

                            <div className="mt-3 text-center">
                                <p className="text-xs text-white/50">{strs.lab.dropZone.dragAndDrop}</p>
                            </div>
                        </>
                    )}
                </AnimationContainer>
            </div>

            {showCropOverlay && (
                <ImageCropOverlay
                    onClose={() => setShowCropOverlay(false)}
                    image={selectedImage?.src || null}
                    onCrop={(croppedImage) => {
                        if (onImageCrop) {
                            onImageCrop(croppedImage);
                        }
                        setShowCropOverlay(false);
                    }}
                />
            )}
        </>
    );
});

ImageDropZone.displayName = "ImageDropZone";

export default ImageDropZone;
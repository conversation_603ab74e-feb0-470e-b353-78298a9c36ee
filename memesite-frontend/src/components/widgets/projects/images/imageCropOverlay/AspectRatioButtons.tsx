import React, { memo } from 'react';
import { AspectRatio, aspectOptions } from '@/constants/pages/projects/imageCropOverlay';

interface AspectRatioButtonsProps {
    selectedAspect: AspectRatio;
    onAspectChange: (aspect: AspectRatio) => void;
}

const AspectRatioButtonsComponent: React.FC<AspectRatioButtonsProps> = ({
    selectedAspect,
    onAspectChange,
}) => {
    return (
        <div className="flex gap-2">
            {aspectOptions.map((option) => (
                <button
                    key={option.key}
                    onClick={() => onAspectChange(option.key)}
                    className="relative cursor-pointer black-card px-4 py-3 text-xs rounded-full overflow-hidden"
                >
                    <div className={`absolute z-10 inset-0 purple-button pointer-events-none transition-opacity duration-500 ${selectedAspect === option.key ? 'opacity-100' : 'opacity-0'}`} />
                    <span className="relative z-20">{option.label}</span>
                </button>
            ))}
        </div>
    );
};

export const AspectRatioButtons = memo(AspectRatioButtonsComponent);
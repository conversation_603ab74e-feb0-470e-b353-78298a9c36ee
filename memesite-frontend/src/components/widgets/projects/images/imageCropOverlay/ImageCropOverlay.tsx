import React, { useRef, useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { X } from "lucide-react";
import { strs } from "@/constants/pages/projects/strs";
import { CROP_CONSTANTS } from "@/constants/pages/projects/imageCropOverlay";
import { useCropArea } from "@/hooks/useCropArea";
import { AspectRatioButtons } from "./AspectRatioButtons";
import { CropHandles } from "./CropHandles";
import { CropGrid } from "./CropGrid";
import Button from "@/components/ui/buttons/Button";

interface ImageCropOverlayProps {
  image: string | null;
  onClose: () => void;
  onCrop: (croppedImage: string) => void;
}

const ImageCropOverlay: React.FC<ImageCropOverlayProps> = ({
  image,
  onClose,
  onCrop,
}) => {
  const [imageDimensions, setImageDimensions] = useState({
    width: 0,
    height: 0,
  });
  const [isVisible, setIsVisible] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  const {
    cropArea,
    selectedAspect,
    imageLoaded,
    handleMouseDown,
    handleImageLoad,
    handleAspectChange,
    handleApplyCrop,
  } = useCropArea({ imageContainerRef, imageDimensions, isVisible });

  useEffect(() => {
    setTimeout(() => setIsVisible(true), CROP_CONSTANTS.INITIAL_DELAY);
    return () => setIsVisible(false);
  }, []);

  // Reset loading state when image changes
  useEffect(() => {
    if (image) {
      setImageLoading(true);
    }
  }, [image]);

  const onApplyCrop = () => {
    if (image) {
      handleApplyCrop(image, (croppedImage) => {
        onCrop(croppedImage);
        onClose();
      });
    }
  };

  if (!image) return null;

  return createPortal(
    <div
      className={`fixed top-0 left-0 w-full h-full z-[9999] flex items-center justify-center transition-opacity duration-300 ${
        isVisible ? "opacity-100 visible" : "opacity-0 invisible"
      } backdrop-blur-sm`}
      onClick={onClose}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className={`relative w-50 black-card gradient-before-rounded-sm rounded-sm h-fit flex flex-col p-6 transition-all duration-300 ${
          isVisible ? "scale-100 opacity-100" : "scale-95 opacity-0"
        }`}
      >
        <div className="relative z-10 w-full flex justify-between items-center text-white mb-4">
          <h1 className="text-base">{strs.lab.dropZone.cropTitle}</h1>
          <button onClick={onClose}>
            <X className="h-[var(--text-base)] w-auto" />
          </button>
        </div>

        <AspectRatioButtons
          selectedAspect={selectedAspect}
          onAspectChange={handleAspectChange}
        />

        <div className="w-full h-px bg-white/20 my-6" />

        <div className="flex-1 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            <div ref={imageContainerRef} className="relative">
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded">
                  <div className="w-8 h-8 border-2 border-purple border-t-transparent rounded-full animate-spin" />
                </div>
              )}
              <img
                src={image}
                alt={strs.lab.dropZone.cropPreview}
                className={`block max-w-full max-h-[60vh] transition-opacity duration-200 ${
                  imageLoading ? "opacity-0" : "opacity-100"
                }`}
                onLoad={(e) => {
                  const img = e.currentTarget;
                  setImageDimensions({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                  });
                  handleImageLoad(e);
                  setImageLoading(false);
                }}
              />

              {imageLoaded && (
                <div
                  className="absolute border-2 border-purple cursor-move"
                  style={{
                    left: cropArea.x - CROP_CONSTANTS.BORDER_OFFSET,
                    top: cropArea.y - CROP_CONSTANTS.BORDER_OFFSET,
                    width: cropArea.width + CROP_CONSTANTS.BORDER_WIDTH,
                    height: cropArea.height + CROP_CONSTANTS.BORDER_WIDTH,
                    boxShadow: `0 0 0 ${CROP_CONSTANTS.SHADOW_SIZE}px rgba(0, 0, 0, ${CROP_CONSTANTS.SHADOW_OPACITY})`,
                  }}
                  onMouseDown={handleMouseDown}
                >
                  <CropGrid />
                  <CropHandles onMouseDown={handleMouseDown} />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="w-full h-px bg-white/20 mt-6 mb-4" />

        <div className="w-full justify-end flex gap-4">
          <Button styleClassName="black-button" onClick={onClose}>
            {strs.lab.dropZone.cancel}
          </Button>
          <Button styleClassName="purple-button" onClick={onApplyCrop}>
            {strs.lab.dropZone.applyCrop}
          </Button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ImageCropOverlay;

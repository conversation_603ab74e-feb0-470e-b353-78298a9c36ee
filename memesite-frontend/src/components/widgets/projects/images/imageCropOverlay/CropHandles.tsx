import React, { memo } from 'react';

type ResizeAction = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

interface CropHandlesProps {
    onMouseDown: (e: React.MouseEvent<HTMLDivElement>, action: ResizeAction) => void;
}

const CropHandlesComponent: React.FC<CropHandlesProps> = ({ onMouseDown }) => {
    return (
        <>
            <div
                className="absolute w-3 h-3 bg-purple border border-purple-light rounded-full -top-[5px] -left-[5px] cursor-nw-resize"
                onMouseDown={(e) => onMouseDown(e, 'top-left')}
            />
            <div
                className="absolute w-3 h-3 bg-purple border border-purple-light rounded-full -top-[5px] -right-[5px] cursor-ne-resize"
                onMouseDown={(e) => onMouseDown(e, 'top-right')}
            />
            <div
                className="absolute w-3 h-3 bg-purple border border-purple-light rounded-full -bottom-[5px] -left-[5px] cursor-sw-resize"
                onMouseDown={(e) => onMouseDown(e, 'bottom-left')}
            />
            <div
                className="absolute w-3 h-3 bg-purple border border-purple-light rounded-full -bottom-[5px] -right-[5px] cursor-se-resize"
                onMouseDown={(e) => onMouseDown(e, 'bottom-right')}
            />
        </>
    );
};

export const CropHandles = memo(CropHandlesComponent);
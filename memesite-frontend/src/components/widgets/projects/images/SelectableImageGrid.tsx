"use client";

import { memo } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import SelectableImageItem from "./SelectableImageItem";
import NoImages from "@/components/widgets/projects/states/NoImages";
import SelectableImageGridSkeleton from "@/components/skeletons/SelectableImageGridSkeleton";
import { LabImage } from "@/data/labImages";

interface SelectableImageGridProps {
    images: LabImage[];
    selectedImages: Set<string>;
    onToggleSelection: (imageName: string) => void;
    className?: string;
    showSelectText?: boolean;
    isLoading?: boolean;
}

const SelectableImageGrid = memo(({
    images,
    selectedImages,
    onToggleSelection,
    className = "grid-cols-6",
    showSelectText = false,
    isLoading = false
}: SelectableImageGridProps) => {
    if (isLoading) {
        return (
            <SelectableImageGridSkeleton
                className={className}
                itemCount={12}
            />
        );
    }

    return (
        <AnimationContainer
            key={images.length}
            className="flex-1 min-h-0 mask-fade-y"
            animationType="fade"
        >
            <div className="relative h-full overflow-y-auto pb-35 pt-3">
                {images.length === 0 ? (
                    <NoImages />
                ) : (
                    <div className={`grid gap-4 ${className}`}>
                        {images.map((image) => (
                            <SelectableImageItem
                                key={image.key || image.name || image.url}
                                image={image}
                                isSelected={selectedImages.has(image.key || image.name || '')}
                                onToggleSelection={onToggleSelection}
                                showSelectText={showSelectText}
                            />
                        ))}
                    </div>
                )}
            </div>
        </AnimationContainer>
    );
});

SelectableImageGrid.displayName = "SelectableImageGrid";

export default SelectableImageGrid;
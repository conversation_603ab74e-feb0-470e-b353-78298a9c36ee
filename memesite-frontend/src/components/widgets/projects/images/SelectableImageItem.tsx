"use client";

import { memo } from "react";
import Image from "next/image";
import { Check } from "lucide-react";
import { LabImage } from "@/data/labImages";
import { useLabImageItem } from "@/hooks/useLabImageItem";

interface SelectableImageItemProps {
    image: LabImage;
    isSelected: boolean;
    onToggleSelection: (imageId: string) => void;
    showSelectText?: boolean;
}

const SelectableImageItem = memo(({
    image,
    isSelected,
    onToggleSelection,
    showSelectText = false
}: SelectableImageItemProps) => {
    const {
        checkmarkClasses,
        checkIconClasses,
        purpleOverlayClasses,
        borderClasses,
        showSelectOverlay
    } = useLabImageItem({ isSelected, showSelectText });

    return (
        <div
            className="relative w-full min-w-0 flex flex-col gap-3 pb-3 cursor-pointer black-box gradient-before-rounded-sm rounded-sm p-2 group"
            onClick={() => onToggleSelection(image.key || image.name || '')}
        >
            <div className={`absolute z-10 top-3 left-3 h-6 aspect-square black-button gradient-before-rounded-xl rounded-full flex items-center justify-center transition-all duration-300 ease-in-out ${checkmarkClasses}`}>
                <Check className={`relative z-10 h-[30%] text-white transition-all duration-300 ease-in-out ${checkIconClasses}`} />
                <div className={`absolute z-0 w-full h-full purple-button gradient-before-rounded-xl rounded-full transition-all duration-300 ease-in-out ${purpleOverlayClasses}`} />
            </div>
            <div className={`relative w-full min-w-0 aspect-square bg-white/5 rounded-sm border-2 overflow-hidden transition-all duration-300 ease-in-out ${borderClasses}`}>
                {showSelectOverlay && (
                    <div className="absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-4 py-2 black-button gradient-before-rounded-sm rounded-sm flex items-center justify-center transition-all duration-600 ease-in-out opacity-0 group-hover:opacity-100">
                        <span className="text-xs text-white">Use</span>
                    </div>
                )}
                <div className={`absolute z-10 top-0 left-0 w-full h-full bg-purple/20 transition-opacity duration-300 ease-in-out ${purpleOverlayClasses}`} />
                <Image
                    src={image.url || image.src || ''}
                    alt={image.name || image.key || 'Image'}
                    fill
                    className="object-cover"
                    unoptimized={(image.src || '').startsWith('data:')}
                />
            </div>
            <div className="w-full min-w-0 px-3">
                <p className="text-xs text-white/75 truncate">{image.name || image.key || 'Untitled'}</p>
            </div>
        </div>
    );
});

SelectableImageItem.displayName = "SelectableImageItem";

export default SelectableImageItem;
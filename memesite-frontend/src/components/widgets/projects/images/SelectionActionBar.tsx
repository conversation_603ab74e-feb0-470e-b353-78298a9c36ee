"use client";

import { memo } from "react";
import { X } from "lucide-react";
import AnimationContainer from "@/components/layout/AnimationContainer";

interface ActionButton {
    icon: React.ComponentType<{ className?: string; strokeWidth?: number }>;
    label: string;
    onClick: () => void;
}

interface SelectionActionBarProps {
    selectedCount: number;
    actionButtons: ActionButton[];
    onClearSelection: () => void;
    hideExtras?: boolean;
}

const SelectionActionBar = memo(({
    selectedCount,
    actionButtons,
    onClearSelection,
    hideExtras = false
}: SelectionActionBarProps) => {
    return (
        <div className={`!absolute bottom-6 left-6 right-6 black-box gradient-before-rounded-sm rounded-sm flex items-center justify-center p-6 transition-all duration-300 ease-in-out ${selectedCount > 0 ? 'opacity-100 translate-y-0 pointer-events-auto' : 'opacity-0 translate-y-4 pointer-events-none'
            }`}>
            {!hideExtras && (
                <p className="absolute top-1/2 -translate-y-1/2 left-6 text-sm text-white">
                    {selectedCount || 0} selected
                </p>
            )}
            <AnimationContainer
                key={actionButtons.length}
                animationType="fade"
                className="flex gap-4"
            >
                {actionButtons.map((button) => {
                    const Icon = button.icon;
                    return (
                        <div
                            key={button.label}
                            className="flex flex-col gap-0.5 justify-center items-center cursor-pointer hover:opacity-75 transition-opacity"
                            onClick={button.onClick}
                        >
                            <Icon className="h-4 w-auto text-white" strokeWidth={1.25} />
                            <p className="text-xs text-white/50">{button.label}</p>
                        </div>
                    );
                })}
            </AnimationContainer>
            {!hideExtras && (
                <X
                    className="absolute top-1/2 -translate-y-1/2 right-6 h-4 w-auto text-white cursor-pointer hover:opacity-75 transition-opacity"
                    onClick={onClearSelection}
                />
            )}
        </div>
    );
});

SelectionActionBar.displayName = "SelectionActionBar";

export default SelectionActionBar;
import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON>, <PERSON><PERSON>, Edit3 } from "lucide-react";

export default function ProjectActions() {
  const [open, setOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);

  const openClasses = [
    "opacity-100",
    "translate-y-0",
    "scale-100",
    "pointer-events-auto",
  ];
  const closedClasses = [
    "opacity-0",
    "translate-y-1",
    "scale-95",
    "pointer-events-none",
  ];

  const toggleMenu = () => setOpen((prev) => !prev);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(e.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") setOpen(false);
    };

    document.addEventListener("click", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  return (
    <>
      <button
        ref={triggerRef}
        aria-haspopup="true"
        aria-expanded={open}
        onClick={(e) => {
          e.stopPropagation();
          toggleMenu();
        }}
        className="inline-flex h-[var(--text-base)] w-[var(--text-base)] cursor-pointer items-center justify-center rounded-full transition-all my-auto"
      >
        <MoreHorizontal className="w-full" />
      </button>

      <div
        ref={menuRef}
        className={`absolute z-10 right-3 top-8 w-[var(--width-10)] rounded-md bg-black black-card gradient-before-rounded p-1.5 origin-top-right transition ease-out duration-200 ${
          open ? openClasses.join(" ") : closedClasses.join(" ")
        }`}
      >
        <div className="flex flex-col gap-0">
          <MenuItem icon={Trash2} label="Delete" />
          <MenuItem icon={Copy} label="Duplicate" />
          <MenuItem icon={Scan} label="Preview" />
          <MenuItem icon={Edit3} label="Edit" />
        </div>
      </div>
    </>
  );
}

const MenuItem = ({
  icon: Icon,
  label,
}: {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}) => (
  <button className="group cursor-pointer flex w-full items-center gap-3 rounded-xl p-3 text-xs font-medium hover:bg-white/5 transition-colors duration-400">
    <Icon className="h-[var(--text-base)] w-auto" />
    <span>{label}</span>
  </button>
);

"use client";

import * as React from "react";
import ProjectActions from "./ProjectActions";
import Image from "next/image";

const ProjectCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { children?: React.ReactNode }
>(({ className = "", children, ...props }, ref) => (
  <div
    ref={ref}
    className={
      "relative rounded black-card gradient-before-rounded shadow p-2 cursor-pointer flex flex-col gap-2" +
      className
    }
    {...props}
  >
    {children}
  </div>
));
ProjectCard.displayName = "ProjectCard";

const ProjectCardImage = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { src: string; alt: string }
>(({ className = "", alt, src, ...props }, ref) => (
  <div
    ref={ref}
    className={
      "relative w-full aspect-3/2 bg-background rounded-sm " +
      className
    }
    {...props}
  >
    <Image
      src={src}
      alt={alt}
      width={200}
      height={200}
      className="rounded-sm object-cover absolute h-full w-full inset-0"
    />
  </div>
));
ProjectCardImage.displayName = "ProjectCardImage";

const ProjectCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title: string;
    description?: string;
  }
>(({ className = "", title, description, ...props }, ref) => (
  <div
    ref={ref}
    className={
      "flex items-start justify-between p-2 pt-0 relative min-w-0" + className
    }
    {...props}
  >
    <div className="min-w-0 max-w-[80%]" >
      <h3 className="text-md font-medium truncate min-w-0">{title}</h3>
      {description && <p className="text-xs text-white/50">{description}</p>}
    </div>
    <ProjectActions />
  </div>
));
ProjectCardContent.displayName = "ProjectCardContent";

export { ProjectCard, ProjectCardImage, ProjectCardContent };
export default ProjectCard;

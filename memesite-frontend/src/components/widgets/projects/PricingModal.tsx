"use client";

import { useState, useEffect, useCallback, memo } from 'react';
import { useAuth } from '@clerk/nextjs';
import Image from 'next/image';
import { createStripeCheckoutSessionAPI } from '@/utils/api';
import { useModalStore } from '@/stores/modalStore';
import { Check, X } from 'lucide-react';
import Button from '@/components/ui/buttons/Button';
import MovingBorder from '@/components/ui/animation/MovingBorder';
import type { PricingPlan, CheckoutError } from '@/types/project';
import { PRICING_PLANS, PRICING_ANIMATION_CONSTANTS } from '@/constants/pricing';


function PricingModal() {
    const { getToken } = useAuth();
    const { closeModal, modalData } = useModalStore();
    const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
    const [isVisible, setIsVisible] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Get projectId from modal data if available
    const projectId = modalData?.projectId as string | undefined;

    const handleClose = useCallback(() => {
        setIsVisible(false);
        setTimeout(() => closeModal(), PRICING_ANIMATION_CONSTANTS.CLOSE_ANIMATION_DELAY);
    }, [closeModal]);

    useEffect(() => {
        const handleEscapeKey = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                handleClose();
            }
        };

        document.addEventListener('keydown', handleEscapeKey);
        return () => {
            document.removeEventListener('keydown', handleEscapeKey);
        };
    }, [handleClose]);

    const handlePlanSelect = useCallback(async (plan: PricingPlan) => {
        setLoadingPlan(plan.planId);
        setError(null);

        try {
            const token = await getToken({
                template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
            });

            if (!token) {
                throw new Error('Authentication token not found');
            }

            const response = await createStripeCheckoutSessionAPI(plan.planId, token, projectId);

            if (!response?.data?.url) {
                throw new Error('Invalid checkout session response');
            }

            window.location.href = response.data.url;
        } catch (err) {
            const error = err as CheckoutError;
            console.error('Failed to create checkout session:', error);
            setError('Failed to process your request. Please try again.');
            setLoadingPlan(null);
        }
    }, [getToken]);

    return (
        <div 
            className={`fixed inset-0 bg-black z-50 transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}
            role="dialog"
            aria-modal="true"
            aria-labelledby="pricing-modal-title"
            aria-describedby="pricing-modal-description"
        >
            <Image 
                src="/images/staticfaded.webp" 
                alt="" 
                fill
                className="absolute top-0 left-0 object-cover"
                priority
            />
            <div className="w-full h-full flex flex-col gap-6 items-center justify-center" >
                <Button
                    onClick={handleClose}
                    className="absolute top-4 right-4 h-8 px-0 aspect-square flex items-center justify-center"
                    aria-label="Close pricing modal"
                >
                    <X className="h-[40%] w-auto text-white" />
                </Button>
                <div className="relative z-10 text-center">
                    <h2 id="pricing-modal-title" className="text-5xl font-medium gradient-text-white leading-[1.15]">Choose your plan</h2>
                    <p id="pricing-modal-description" className="text-sm lg:text-xl text-white/75">
                        You&apos;ve reached your site limit. Upgrade your plan to continue building.
                    </p>
                    {error && (
                        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm">
                            {error}
                        </div>
                    )}
                </div>

                <div className="relative z-0 grid md:grid-cols-2 gap-6">
                    {PRICING_PLANS.map((plan) => (
                        <div
                            key={plan.name}
                            className="relative flex flex-col gap-6 p-6 rounded w-25 black-box gradient-before-rounded"
                        >
                            <div className="flex flex-col gap-1" >
                                <div className="w-full flex justify-between items-center h-8" >
                                    <h3 className="text-lg text-white/75">
                                        {plan.name}
                                    </h3>
                                    {plan.name === 'Yearly Plan' && (
                                        <div className="relative overflow-hidden rounded-full flex items-center w-fit px-6 h-8 black-button gradient-before-rounded-lg" >
                                            <p className="relative z-20 text-xs" >Save 35%</p>
                                            <div className="absolute z-10 inset-0.5 black-button-gradient" style={{ borderRadius: `calc(${PRICING_ANIMATION_CONSTANTS.BORDER_RADIUS} * 1.7)` }} />
                                            <div
                                                className="absolute z-0 inset-0"
                                                style={{ borderRadius: `calc(${PRICING_ANIMATION_CONSTANTS.BORDER_RADIUS} * 1)` }}
                                            >
                                                <MovingBorder duration={PRICING_ANIMATION_CONSTANTS.DURATION} rx="30%" ry="30%">
                                                    <div
                                                        className={`h-20 w-20 bg-[radial-gradient(#6D33FC_5%,transparent_30%)] opacity-[0.8]`}
                                                    />
                                                </MovingBorder>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                <div className="flex items-end">
                                    <h2 className="text-4xl font-medium gradient-text-white" >
                                        {plan.price}
                                    </h2>
                                    <span className="text-lg text-white/75">{plan.period}</span>
                                </div>
                            </div>

                            <div className="w-full h-px bg-white/10" />

                            <ul className="flex flex-col gap-1">
                                {plan.features.map((feature, index) => (
                                    <li key={index} className="flex items-center gap-1 text-white/75">
                                        <Check className="h-[var(--text-sm)] w-auto" />
                                        <p className="text-base text-white/75" >{feature}</p>
                                    </li>
                                ))}
                            </ul>

                            <Button
                                onClick={() => handlePlanSelect(plan)}
                                disabled={loadingPlan === plan.planId}
                                styleClassName={plan.popular ? 'purple-button' : 'black-button'}
                                aria-label={`Select ${plan.name} for ${plan.price}${plan.period}`}
                            >
                                {loadingPlan === plan.planId ? 'Loading...' : plan.buttonText}
                            </Button>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

export default memo(PricingModal);
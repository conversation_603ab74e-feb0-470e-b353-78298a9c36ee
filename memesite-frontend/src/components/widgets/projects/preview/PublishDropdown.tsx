"use client";

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { deployProjectAPI } from '@/utils/api';
import { ProjectWithVersions } from '@/types/project';

interface PublishDropdownProps {
    project: ProjectWithVersions;
    isOpen: boolean;
    onClose: () => void;
    onDeploy?: () => void;
}

export default function PublishDropdown({ 
    project, 
    isOpen, 
    onClose, 
    onDeploy
}: PublishDropdownProps) {
    const { getToken } = useAuth();
    const [isDeploying, setIsDeploying] = useState(false);
    const [deployError, setDeployError] = useState<string | null>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    const handleDeploy = async () => {
        if (isDeploying) return;

        setIsDeploying(true);
        setDeployError(null);

        try {
            const token = await getToken({
                template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
            });

            if (!token) {
                throw new Error('Authentication token not found');
            }

            await deployProjectAPI(project.id, token);
            
            if (onDeploy) {
                onDeploy();
            }
            
            onClose();
        } catch (error: unknown) {
            setDeployError(error instanceof Error ? error.message : 'Failed to deploy project');
        } finally {
            setIsDeploying(false);
        }
    };

    const handleCopyUrl = async (url: string) => {
        try {
            await navigator.clipboard.writeText(url);
        } catch (error) {
            console.error('Failed to copy URL:', error);
        }
    };

    if (!isOpen) return null;

    return (
        <div
            ref={dropdownRef}
            className="absolute z-10 right-3 top-12 w-[var(--width-30)] rounded-md bg-black black-card gradient-before-rounded p-1.5 origin-top-right transition ease-out duration-200"
        >
            <div className="space-y-3">
                {/* Preview URL Section */}
                {project.preview_url && (
                    <div className="p-3 rounded-sm glass-box">
                        <div className="text-xs text-gray-400 mb-2">Preview URL</div>
                        <div className="flex items-center justify-between mb-2">
                            <div className="text-sm text-white truncate mr-2 font-mono">
                                {project.preview_url}
                            </div>
                            <button
                                onClick={() => handleCopyUrl(project.preview_url!)}
                                className="text-xs text-blue-400 hover:text-blue-300 transition-colors px-2 py-1 rounded"
                            >
                                Copy
                            </button>
                        </div>
                        <a
                            href={project.preview_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                            Open Preview →
                        </a>
                    </div>
                )}

                {/* Published URL Section */}
                {project.publish_url && (
                    <div className="p-3 rounded-sm green-box">
                        <div className="text-xs text-gray-200 mb-2">Published URL</div>
                        <div className="flex items-center justify-between mb-2">
                            <div className="text-sm text-white truncate mr-2 font-mono">
                                {project.publish_url}
                            </div>
                            <button
                                onClick={() => handleCopyUrl(project.publish_url!)}
                                className="text-xs text-green-200 hover:text-white transition-colors px-2 py-1 rounded"
                            >
                                Copy
                            </button>
                        </div>
                        <a
                            href={project.publish_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-green-200 hover:text-white transition-colors"
                        >
                            Open Live Site →
                        </a>
                    </div>
                )}

                {/* Deploy Button */}
                {project.preview_url && !project.publish_url && (
                    <button
                        onClick={handleDeploy}
                        disabled={isDeploying}
                        className="w-full p-3 purple-button rounded-full relative text-white text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isDeploying ? 'Deploying...' : 'Deploy to Production'}
                    </button>
                )}

                {/* Error Message */}
                {deployError && (
                    <div className="p-3 rounded-sm bg-red-900/20 border border-red-500/30">
                        <div className="text-xs text-red-400">{deployError}</div>
                    </div>
                )}

                {/* No URLs Available */}
                {!project.preview_url && !project.publish_url && (
                    <div className="p-3 text-center">
                        <div className="text-sm text-gray-400">
                            No preview available yet
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

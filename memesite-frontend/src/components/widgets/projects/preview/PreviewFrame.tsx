"use client";

import { memo, useRef } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import { strs } from "@/constants/pages/projects/strs";
import LogoAnimation from "@/components/ui/animation/LogoAnimation";
import LoadingTipItem from "./LoadingTipItem";
import PopupInput from "@/components/ui/input/PopupInput";
import { useIframeUrl } from "@/hooks/useIframeUrl";
import { useIframeInspector } from "@/hooks/useIframeInspector";
import AnalyticsOverlay from "../analytics/AnalyticsOverlay";
import MovingBorder from "@/components/ui/animation/MovingBorder";
import AnimatedText from "@/components/ui/animation/AnimatedText";

interface PreviewFrameProps {
    deviceClasses: string;
    activeDevice: string;
    showLoadingState: boolean;
    previewStage: number;
    clonedUrl?: string;
    previewUrl?: string | null;
    publishUrl?: string | null;
    isEditMode?: boolean;
    onSendMessage?: (content: string) => void;
    isLoading?: boolean;
    activeView?: string;
    onIframeClick?: () => void;
    onNonImageClick?: () => void;
    showAnalytics?: boolean;
    borderRadius?: string;
    duration?: number;
}

const PreviewFrame = memo(({
    deviceClasses,
    activeDevice,
    showLoadingState,
    previewStage,
    clonedUrl,
    previewUrl,
    publishUrl,
    isEditMode,
    onSendMessage,
    isLoading,
    activeView,
    onIframeClick,
    onNonImageClick,
    showAnalytics = false,
    borderRadius = "var(--vw-0_5)",
    duration = 3000
}: PreviewFrameProps) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);

    const iframeUrl = useIframeUrl({
        showLoadingState,
        previewUrl,
        publishUrl,
        clonedUrl,
        previewStage
    });

    useIframeInspector({
        iframeRef,
        isEditMode,
        iframeUrl,
        onImageElementSelected: onIframeClick,
        onNonImageElementSelected: onNonImageClick
    });

    return (
        <div className="w-full h-full flex items-center justify-center relative">
            <AnimationContainer
                key={`${activeDevice}-${showLoadingState ? 'loading' : 'iframe'}`}
                className={`${deviceClasses} ${activeView === 'lab' ? 'pointer-events-none' : ''}`}
                animationType="fade"
            >
                {showLoadingState ? (
                    <div className="w-full h-full flex items-center justify-center">
                        <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-6">
                            <div
                                className="absolute z-0 inset-0"
                                style={{ borderRadius: `calc(${borderRadius} * 0.96)` }}
                            >
                                <MovingBorder duration={duration} rx="30%" ry="30%">
                                    <div
                                        className={`h-20 w-20 bg-[radial-gradient(#6D33FC_40%,transparent_60%)] opacity-[0.8]`}
                                    />
                                </MovingBorder>
                            </div>
                            <div className="absolute z-10 inset-0.5 bg-dark" style={{ borderRadius: `calc(${borderRadius} * 1.7)` }} />
                            <div className="relative z-20 flex flex-col gap-2 justify-center items-center">
                                <LogoAnimation className="h-5 w-auto cursor-pointer" />
                                <AnimatedText
                                    text={strs.loading.title}
                                    className="text-sm text-white"
                                    continuous={true}

                                />
                            </div>
                            <div className="relative z-20 flex flex-col gap-3 justify-center">
                                {strs.loading.tips.map((tip, index) => (
                                    <LoadingTipItem key={index} {...tip} />
                                ))}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="w-full h-full relative">
                        <iframe
                            ref={iframeRef}
                            key={iframeUrl}
                            src={iframeUrl}
                            className="w-full h-full rounded"
                            title={strs.iframe.title}
                        />
                        {isEditMode && (
                            <div
                                className="absolute inset-0 z-10"
                                onClick={() => {
                                    if (onIframeClick) {
                                        onIframeClick();
                                    }
                                }}
                            />
                        )}
                    </div>
                )}
            </AnimationContainer>
            <PopupInput onSendMessage={onSendMessage} isLoading={isLoading} />
            <AnalyticsOverlay isVisible={showAnalytics} />
        </div>
    );
});

PreviewFrame.displayName = "PreviewFrame";

export default PreviewFrame;
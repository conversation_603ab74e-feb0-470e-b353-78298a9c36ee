"use client";

import { memo, useState } from "react";
import IconButton from "@/components/ui/buttons/IconButton";
import SelectorButton from "@/components/ui/buttons/SelectorButton";
import { deviceOptions } from "./DeviceOptions";
import { strs } from "@/constants/pages/projects/strs";
import type { DeviceType } from "@/hooks/useDevicePreview";
import { useEditModeStore } from "@/stores/editModeStore";
import { useProjectChangesStore } from "@/stores/projectChangesStore";
import { useUIStore } from "@/stores/uiStore";
import PublishDropdown from "./PublishDropdown";
import { ProjectWithVersions } from "@/types/project";

interface PreviewToolbarProps {
    activeDevice: DeviceType;
    onDeviceChange: (device: DeviceType) => void;
    isLoading?: boolean;
    onAnalyticsClick?: () => void;
    isAnalyticsVisible?: boolean;
    previewUrl?: string | null;
    clonedUrl?: string;
    project?: ProjectWithVersions | null;
    mutateProject?: () => void;
}

const PreviewToolbar = memo(({ activeDevice, onDeviceChange, isLoading, onAnalyticsClick, isAnalyticsVisible, previewUrl, clonedUrl, project, mutateProject }: PreviewToolbarProps) => {
    const { isEditMode, toggleEditMode, isTextEditing } = useEditModeStore();
    const { changes, applyChanges, clearChanges } = useProjectChangesStore();
    const [isPublishDropdownOpen, setIsPublishDropdownOpen] = useState(false);

    // Get isGenerating state from global store
    const isGenerating = useUIStore(state => state.isGenerating);
    const hasReceivedApiResponse = useUIStore(state => state.hasReceivedApiResponse);

    // Check version status - only enable buttons when status is 'executed' AND not waiting for more context
    const versionStatus = project?.versions?.[0]?.version_status;
    // Disable buttons when isGenerating is false (waiting for more context)
    const areButtonsEnabled = versionStatus === 'executed' && !(hasReceivedApiResponse && isGenerating === false);

    const handleEditToggle = async () => {
        if (isEditMode && changes.length > 0) {
            await applyChanges();
        } else if (isEditMode) {
            clearChanges();
        }
        toggleEditMode();
    };

    const getEditButtonLabel = () => {
        if (!isEditMode) return strs.buttons.edit;
        if (isTextEditing || changes.length > 0) return strs.buttons.saveChanges;
        return strs.buttons.cancel;
    };

    const handleOpenSite = () => {
        let urlToOpen = '';
        if (previewUrl) {
            urlToOpen = previewUrl;
        } else if (clonedUrl) {
            urlToOpen = clonedUrl;
        } else {
            urlToOpen = '/preview';
        }
        window.open(urlToOpen, '_blank');
    };

    return (
        <div className="relative w-full flex justify-between items-center">
            <div className={`flex items-center gap-3 ${isLoading || isAnalyticsVisible ? 'opacity-50 pointer-events-none' : ''}`}>
                <IconButton
                    icon="/icons/cursor.svg"
                    alt={strs.alt.browserIcon}
                    label={getEditButtonLabel()}
                    onClick={handleEditToggle}
                    disabled={!(!!project?.preview_url || !!project?.publish_url) || isLoading || !areButtonsEnabled}
                />
                <IconButton
                    icon="/icons/browser.svg"
                    alt={strs.alt.browserIcon}
                    label={strs.buttons.site}
                    onClick={handleOpenSite}
                    className={isEditMode ? 'opacity-50 pointer-events-none' : ''}
                    disabled={!(!!project?.preview_url || !!project?.publish_url) || isLoading || !areButtonsEnabled}
                />
            </div>

            <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${isLoading || isAnalyticsVisible || isEditMode ? 'opacity-50 pointer-events-none' : ''}`}>
                <SelectorButton
                    options={deviceOptions}
                    activeValue={activeDevice}
                    onValueChange={(value: string) => onDeviceChange(value as DeviceType)}
                    wrapperClassName=" !p-0"
                    className="!w-fit !h-8"
                    buttonClassName="!h-8 !w-[2.21rem] !px-0 aspect-square"
                />
            </div>

            <div className={`flex items-center gap-3 ${isLoading || isEditMode ? 'opacity-50 pointer-events-none' : ''}`}>
                <IconButton
                    icon="/icons/analytics.svg"
                    alt={strs.alt.browserIcon}
                    label={isAnalyticsVisible ? strs.buttons.closeAnalytics : strs.buttons.analytics}
                    onClick={onAnalyticsClick}
                    disabled={!(!!project?.preview_url || !!project?.publish_url) || isLoading || !areButtonsEnabled}
                />
                <div className="relative">
                    <IconButton
                        icon="/icons/rocket.svg"
                        alt={strs.alt.browserIcon}
                        label={strs.buttons.publish}
                        onClick={() => setIsPublishDropdownOpen(!isPublishDropdownOpen)}
                        disabled={!(!!project?.preview_url || !!project?.publish_url) || isLoading || !areButtonsEnabled}
                    />
                    {project && (
                        <PublishDropdown
                            project={project}
                            isOpen={isPublishDropdownOpen}
                            onClose={() => setIsPublishDropdownOpen(false)}
                            onDeploy={mutateProject}
                        />
                    )}
                </div>
            </div>
        </div>
    );
});

PreviewToolbar.displayName = "PreviewToolbar";

export default PreviewToolbar;
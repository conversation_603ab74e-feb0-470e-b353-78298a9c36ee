"use client";

import { memo, useState, useEffect } from "react";
import { ViewType } from "@/types/chat";
import { LabImage } from "@/data/labImages";
import { useLabImages } from "@/hooks/useLabImages";
import { useBackendImages } from "@/hooks/useBackendImages";
import { useImageSelection } from "@/hooks/useImageSelection";
import { useFileDrop } from "@/hooks/useFileDrop";
import { useLabActions } from "@/hooks/useLabActions";
import { useChatStore } from "@/stores/chatStore";
import ImageDropZone from "../images/ImageDropZone";
import SelectableImageGrid from "../images/SelectableImageGrid";
import SelectionActionBar from "../images/SelectionActionBar";
import { MousePointerClick } from "lucide-react";

interface LabOverlayProps {
  isVisible: boolean;
  onViewChange?: (view: ViewType) => void;
}

const LabOverlay = memo(({ isVisible, onViewChange }: LabOverlayProps) => {
  const [isGridLoading, setIsGridLoading] = useState(true);
  const [selectedImageForDropzone, setSelectedImageForDropzone] =
    useState<LabImage | null>(null);
  const { labMessages } = useChatStore();
  const {
    uploadedImages,
    fileInputRef,
    handleFileSelect,
    deleteImages,
    downloadImages,
    openFilePicker,
  } = useLabImages();

  const {
    backendImages,
    refreshImages,
    deleteBackendImage,
    deleteBulkBackendImages,
    saveBackendImage,
  } = useBackendImages(isVisible); // Only fetch when overlay is visible

  // Only show backend images from GET /images API
  const allImages = backendImages;

  const { selectedImages, toggleImageSelection, clearSelection, selectAll } =
    useImageSelection();

  const { isDragging, handleDragOver, handleDragLeave, handleDrop } =
    useFileDrop(handleFileSelect);

  // Handle dropzone image selection from grid
  const handleImageSelectForDropzone = (imageName: string) => {
    const image = allImages.find((img) => (img.key || img.name) === imageName);
    if (image) {
      setSelectedImageForDropzone(image);
    }
  };

  // Handle dropzone image removal
  const handleRemoveDropzoneImage = () => {
    setSelectedImageForDropzone(null);
  };

  // Handle dropzone image cropping
  const handleDropzoneImageCrop = (croppedImage: string) => {
    if (selectedImageForDropzone) {
      const updatedImage = {
        ...selectedImageForDropzone,
        url: croppedImage,
        src: croppedImage,
      };
      setSelectedImageForDropzone(updatedImage);
    }
  };

  const handleDeleteImages = async (imageNames: Set<string>) => {
    // Separate backend and uploaded images
    const backendImagesToDelete: LabImage[] = [];
    const uploadedImagesToDelete = new Set<string>();

    Array.from(imageNames).forEach((imageName) => {
      const backendImage = backendImages.find(
        (img) => (img.key || img.name) === imageName
      );
      const uploadedImage = uploadedImages.find(
        (img) => (img.name || img.key) === imageName
      );

      if (backendImage) {
        backendImagesToDelete.push(backendImage);
      } else if (uploadedImage) {
        uploadedImagesToDelete.add(imageName);
      }
    });

    // Delete backend images (single or bulk)
    if (backendImagesToDelete.length === 1) {
      const image = backendImagesToDelete[0];
      await deleteBackendImage(image.key, image.url || image.src || "");
    } else if (backendImagesToDelete.length > 1) {
      await deleteBulkBackendImages(backendImagesToDelete);
    }

    // Delete uploaded images locally
    if (uploadedImagesToDelete.size > 0) {
      deleteImages(uploadedImagesToDelete);
    }

    clearSelection();
  };

  const handleDownloadImages = () => {
    downloadImages(selectedImages, allImages);
  };

  const handleSaveImage = async (key: string, url: string) => {
    const success = await saveBackendImage(key, url);
    if (success) {
      clearSelection();
    }
  };

  const { actionButtons } = useLabActions({
    selectedImages,
    uploadedImages: allImages,
    selectAll: () => selectAll(allImages),
    deleteImages: () => handleDeleteImages(selectedImages),
    downloadImages: handleDownloadImages,
    clearSelection,
    onViewChange,
    onSaveImage: handleSaveImage,
    allImages,
  });

  useEffect(() => {
    if (isVisible) {
      setIsGridLoading(true);
      // Only refresh on initial load, not on every visibility change
      if (backendImages.length === 0) {
        refreshImages();
      }

      const timer = setTimeout(() => {
        setIsGridLoading(false);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [isVisible, backendImages.length, refreshImages]);

  // Refresh images when new lab messages are generated (completed images)
  useEffect(() => {
    const hasCompletedImages = labMessages.some(
      (msg) =>
        msg.type === "image" && msg.imageCompleted && msg.role === "assistant"
    );

    if (hasCompletedImages) {
      refreshImages();
    }
  }, [labMessages, refreshImages]);

  return (
    <div
      className={`absolute inset-0 w-full h-full bg-background rounded-sm z-10 transition-opacity duration-300 ${
        isVisible
          ? "opacity-100 pointer-events-auto"
          : "opacity-0 pointer-events-none"
      }`}
    >
      <div className="w-full h-full p-0 flex flex-col gap-3">
        <ImageDropZone
          isDragging={isDragging}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onAddFiles={openFilePicker}
          fileInputRef={fileInputRef}
          onFileSelect={handleFileSelect}
          selectedImage={selectedImageForDropzone}
          onRemoveImage={handleRemoveDropzoneImage}
          onImageCrop={handleDropzoneImageCrop}
        />

        <SelectableImageGrid
          images={allImages}
          selectedImages={selectedImages}
          onToggleSelection={toggleImageSelection}
          isLoading={isGridLoading}
        />

        <SelectionActionBar
          selectedCount={selectedImages.size}
          actionButtons={[
            ...actionButtons,
            ...(selectedImages.size === 1
              ? [
                  {
                    label: "Use in Dropzone",
                    onClick: () => {
                      const selectedImageName = Array.from(selectedImages)[0];
                      handleImageSelectForDropzone(selectedImageName);
                      clearSelection();
                    },
                    icon: MousePointerClick,
                  },
                ]
              : []),
          ]}
          onClearSelection={clearSelection}
        />
      </div>
    </div>
  );
});

LabOverlay.displayName = "LabOverlay";

export default LabOverlay;

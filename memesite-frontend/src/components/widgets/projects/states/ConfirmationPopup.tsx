"use client";

import { memo, useEffect, useState } from "react";
import { createPortal } from "react-dom";
import Button from "@/components/ui/buttons/Button";
import { strs } from "@/constants/pages/projects/strs";

interface ConfirmationPopupProps {
    isVisible: boolean;
    onCancel: () => void;
    onConfirm: () => void;
    title?: string;
    description?: string;
    cancelText?: string;
    confirmText?: string;
    confirmButtonStyle?: string;
}

const ConfirmationPopup = memo(({
    isVisible,
    onCancel,
    onConfirm,
    title = strs.confirmationPopup.replaceImage.title,
    description = strs.confirmationPopup.replaceImage.description,
    cancelText = strs.confirmationPopup.replaceImage.cancelText,
    confirmText = strs.confirmationPopup.replaceImage.confirmText,
    confirmButtonStyle = "purple-button"
}: ConfirmationPopupProps) => {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
        return () => setMounted(false);
    }, []);

    useEffect(() => {
        if (isVisible) {
            document.body.style.overflow = "hidden";

            const handleEscape = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    onCancel();
                }
            };

            document.addEventListener('keydown', handleEscape);

            return () => {
                document.body.style.overflow = "unset";
                document.removeEventListener('keydown', handleEscape);
            };
        } else {
            document.body.style.overflow = "unset";
        }
    }, [isVisible, onCancel]);

    if (!mounted) return null;

    const popupContent = (
        <div
            className={`fixed top-0 left-0 w-full h-full z-[9999] flex items-center justify-center transition-opacity duration-300 ${isVisible ? 'opacity-100 visible' : 'opacity-0 invisible'
                } backdrop-blur-sm`}
            onClick={onCancel}
        >
            <div
                onClick={(e) => e.stopPropagation()}
                className={`relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-4 text-center max-w-md transition-all duration-300 ${isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
                    }`}
            >
                <h2 className="text-lg text-white">{title}</h2>
                <p className="text-sm text-white/75">{description}</p>
                <div className="flex items-center justify-center gap-3 mt-2">
                    <Button
                        styleClassName="black-button"
                        className="w-1/2"
                        onClick={onCancel}
                    >
                        {cancelText}
                    </Button>
                    <Button
                        styleClassName={confirmButtonStyle}
                        className="w-1/2"
                        onClick={onConfirm}
                    >
                        {confirmText}
                    </Button>
                </div>
            </div>
        </div>
    );

    return createPortal(popupContent, document.body);
});

ConfirmationPopup.displayName = "ConfirmationPopup";

export default ConfirmationPopup;
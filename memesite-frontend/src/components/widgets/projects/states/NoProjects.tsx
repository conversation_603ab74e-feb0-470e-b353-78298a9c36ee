import Button from "@/components/ui/buttons/Button";
import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { strs } from "@/constants/pages/projects/strs";

export default function NoProjects() {
  const router = useRouter();
  return (
    <div className="flex-1 h-full pb-10 flex items-center justify-center">
      <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 text-center">
        <h2 className="text-lg font-medium">
          {strs.states.noProjects.title}
        </h2>

        <Button
          className="flex items-center gap-2 px-6"
          styleClassName="black-button"
          onClick={() => router.push("/home")}
        >
          <Plus className="h-4 w-4" />
          {strs.newProject}
        </Button>
      </div>
    </div>
  )
}
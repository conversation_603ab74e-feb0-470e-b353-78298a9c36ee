"use client";

import { MessageCircle } from "lucide-react";
import IconWith<PERSON>ackground from "@/components/ui/buttons/IconWithBackground";

export default function MoreContextNeeded() {
    return (
        <div className="w-full h-full flex items-center justify-center">
            <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 items-center text-center">
                <IconWithBackground icon={MessageCircle} />
                <div className="flex flex-col gap-0">
                    <h2 className="text-lg text-white">
                        More context needed
                    </h2>
                    <p className="text-xs text-white/50">
                        Please provide more details to continue building your website
                    </p>
                </div>
            </div>
        </div>
    );
}

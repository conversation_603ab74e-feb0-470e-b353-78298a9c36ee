"use client";

import { Spark<PERSON> } from "lucide-react";
import IconWith<PERSON><PERSON>ground from "@/components/ui/buttons/IconWithBackground";
import { strs } from "@/constants/pages/projects/strs";

export default function StartGenerating() {
    return (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 items-center text-center">
                <IconWithBackground icon={Sparkles} />
                <div className="flex flex-col gap-0">
                    <h2 className="text-lg text-white">
                        {strs.states.startGenerating.title}
                    </h2>
                    <p className="text-xs text-white/50">
                        {strs.states.startGenerating.subtitle}
                    </p>
                </div>
            </div>
        </div>
    );
}
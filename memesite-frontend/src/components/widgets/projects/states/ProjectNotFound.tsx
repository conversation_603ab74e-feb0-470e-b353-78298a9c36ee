import Button from "@/components/ui/buttons/Button";
import { strs } from "@/constants/pages/projects/strs";

export default function ProjectNotFound({ projectError }: { projectError: string | Error | null }) {
  const errorMessage = projectError instanceof Error ? projectError.message : projectError;

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="relative overflow-hidden p-8 black-card rounded-sm gradient-before-rounded-sm flex flex-col gap-6 text-center">
        <div className="flex flex-col" >
          <h1 className="text-xl font-semibold">
            {errorMessage === 'Invalid Project' ? strs.states.notFound.title : strs.states.notFound.accessDenied}
          </h1>
          <p className="text-sm">
            {errorMessage || 'Project not found'}
          </p>
        </div>
        <Button
          onClick={() => window.location.href = '/home'}
        >
          {strs.states.notFound.goBack}
        </Button>
      </div>
    </div>
  )
}
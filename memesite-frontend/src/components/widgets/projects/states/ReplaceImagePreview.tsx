"use client";

import { MousePointerClick, X } from "lucide-react";
import Image from "next/image";
import IconWith<PERSON>ackground from "@/components/ui/buttons/IconWithBackground";
import { LabImage } from "@/data/labImages";
import { useLabStore } from "@/stores/labStore";
import { useEditModeStore } from "@/stores/editModeStore";

interface ReplaceImagePreviewProps {
    title: string;
    subtitle: string;
    selectedImage?: LabImage | null;
    isVisible?: boolean;
}

export default function ReplaceImagePreview({ title, subtitle, selectedImage, isVisible = true }: ReplaceImagePreviewProps) {
    const { clearSelectedImage } = useLabStore();
    const { setEditMode } = useEditModeStore();

    const handleRemoveImage = () => {
        setEditMode(false);
        setTimeout(() => {
            clearSelectedImage();
        }, 300);
    };

    return (
        <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${isVisible ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
            }`}>
            <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 items-center text-center">
                <IconWithBackground icon={MousePointerClick} />
                <div className="flex flex-col gap-0">
                    <h2 className="text-lg text-white">
                        {title}
                    </h2>
                    <p className="text-xs text-white/50">
                        {subtitle}
                    </p>
                </div>
                {selectedImage && (
                    <div className="mt-2 w-full h-30 relative rounded-sm overflow-hidden group">
                        <Image
                            src={selectedImage.url || selectedImage.src || ''}
                            alt={selectedImage.name || selectedImage.key || 'Selected image'}
                            fill
                            className="relative z-0 object-cover"
                        />
                        <div
                            onClick={handleRemoveImage}
                            className="absolute z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-6 aspect-square black-box gradient-before-rounded-lg rounded-full overflow-hidden flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer"
                        >
                            <X className="h-[40%] w-auto text-white" />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
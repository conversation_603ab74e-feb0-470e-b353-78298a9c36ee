"use client";

import { ImageIcon } from "lucide-react";
import IconWith<PERSON>ackground from "@/components/ui/buttons/IconWithBackground";

export default function NoImages() {
    return (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="relative overflow-hidden p-8 black-card gradient-before-rounded-sm rounded-sm flex flex-col gap-3 items-center text-center">
                <IconWithBackground icon={ImageIcon} />
                <div className="flex flex-col gap-0">
                    <h2 className="text-lg text-white">
                        No Images Yet
                    </h2>
                    <p className="text-xs text-white/50">
                        Generate or upload images to get started
                    </p>
                </div>
            </div>
        </div>
    );
}

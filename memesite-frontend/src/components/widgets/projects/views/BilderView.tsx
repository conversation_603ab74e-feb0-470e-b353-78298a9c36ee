"use client";

import { forwardRef } from "react";
import InputSection from "@/components/ui/input/InputSection";
import ChatMessageComponent from "@/components/ui/ChatMessage";
import type { ChatMessage } from "@/types/chat";
import {
    shouldAnimate,
    shouldShowThinking,
    getAnimationCompleteHandler,
    getTasksCompleteHandler
} from "@/utils/chat";
import { useMessageAnimationState } from "@/hooks/useMessageAnimationState";
import { useLabStore } from "@/stores/labStore";
import { useEditModeStore } from "@/stores/editModeStore";

interface BilderViewProps {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    isInputEnabled?: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
}

const BilderView = forwardRef<HTMLDivElement, BilderViewProps>(({
    messages,
    isLoading,
    isResponseReady,
    isInputEnabled = true,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted
}, ref) => {
    const { isAnyMessageAnimating } = useMessageAnimationState(messages, isLoading, isResponseReady);
    const { selectedImageForUse, clearSelectedImage } = useLabStore();
    const { setEditMode } = useEditModeStore();

    const handleSubmit = (value: string) => {
        if (selectedImageForUse) {
            clearSelectedImage();
            setEditMode(false);
        }
        onSendMessage(value);
    };

    return (
        <>
            <div ref={ref} className="relative w-full flex-1 min-h-0 overflow-y-auto mask-fade-y">
                <div className="relative py-6 pb-20">
                    {messages.map((message, index) => (
                        <ChatMessageComponent
                            key={message.id}
                            message={message}
                            isAnimated={shouldAnimate(message, index, messages.length, isLoading, isResponseReady)}
                            isThinking={shouldShowThinking(message, index, messages.length, isLoading)}
                            onAnimationComplete={getAnimationCompleteHandler(message, index, messages.length, onAnimationComplete)}
                            onAllTasksCompleted={getTasksCompleteHandler(message, index, messages.length, onAllTasksCompleted)}
                            viewContext="build"
                        />
                    ))}
                </div>
            </div>
            <div className="relative w-full h-fit">
                <InputSection
                    enableConversation
                    leftButtonsClassName="hidden"
                    rightButtonsClassName="w-full justify-between"
                    textareaMaxLines={3}
                    onSubmit={handleSubmit}
                    isLoading={(isLoading || isAnyMessageAnimating) && isInputEnabled}
                    disabled={!isInputEnabled}
                />
            </div>
        </>
    );
});

BilderView.displayName = "BilderView";

export default BilderView;

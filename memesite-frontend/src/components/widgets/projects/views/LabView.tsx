"use client";

import { forwardRef } from "react";
import InputSection from "@/components/ui/input/InputSection";
import ChatMessageComponent from "@/components/ui/ChatMessage";
import StartGenerating from "@/components/widgets/projects/states/StartGenerating";
import type { ChatMessage } from "@/types/chat";
import type { LabImage } from "@/data/labImages";
import {
    shouldAnimate,
    shouldShowThinking,
    getAnimationCompleteHandler,
    getTasksCompleteHandler
} from "@/utils/chat";
import { useMessageAnimationState } from "@/hooks/useMessageAnimationState";

interface LabViewProps {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
    viewContext?: string;
    onImageApply?: () => void;
    onImageUse?: (image?: LabImage) => void;
}

const LabView = forwardRef<HTMLDivElement, LabViewProps>(({
    messages,
    isLoading,
    isResponseReady,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted,
    viewContext = "lab",
    onImageApply,
    onImageUse
}, ref) => {
    const { isAnyMessageAnimating } = useMessageAnimationState(messages, isLoading, isResponseReady);

    return (
        <>
            <div ref={ref} className="relative w-full flex-1 min-h-0 overflow-y-auto py-6 pb-20 mask-fade-y">
                {messages.length === 0 && !isLoading && (
                    <StartGenerating />
                )}
                {messages.map((message, index) => (
                    <ChatMessageComponent
                        key={message.id}
                        message={message}
                        isAnimated={shouldAnimate(message, index, messages.length, isLoading, isResponseReady)}
                        isThinking={shouldShowThinking(message, index, messages.length, isLoading)}
                        onAnimationComplete={getAnimationCompleteHandler(message, index, messages.length, onAnimationComplete)}
                        onAllTasksCompleted={getTasksCompleteHandler(message, index, messages.length, onAllTasksCompleted)}
                        viewContext={viewContext}
                        onImageApply={onImageApply}
                        onImageUse={onImageUse}
                    />
                ))}
            </div>
            <div className="relative w-full h-fit">
                <InputSection
                    enableConversation
                    leftButtonsClassName="hidden"
                    rightButtonsClassName="w-full justify-between"
                    textareaMaxLines={3}
                    onSubmit={onSendMessage}
                    isLoading={isLoading || isAnyMessageAnimating}
                />
            </div>
        </>
    );
});

LabView.displayName = "LabView";

export default LabView;
"use client";

import { forwardRef, useRef, useImperativeHandle } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import ChatContainer, { ChatContainerRef } from "@/components/widgets/projects/ChatContainer";
import PreviewContainer from "@/components/widgets/projects/PreviewContainer";
import MoreContextNeeded from "@/components/widgets/projects/states/MoreContextNeeded";
import type { ChatMessage, ViewType } from "@/types/chat";
import { ProjectWithVersions } from "@/types/project";

interface BuildViewProps {
    activeView: ViewType;
    setActiveView: (view: ViewType) => void;
    messages: ChatMessage[];
    isLoading: boolean;
    isExecuting: boolean;
    isResponseReady: boolean;
    isInputEnabled?: boolean;
    isGenerating?: boolean | null;
    hasReceivedApiResponse?: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
    shouldShowPreview: boolean;
    previewStage: number;
    clonedUrl?: string;
    previewUrl?: string | null;
    projectStatus: 'pending' | 'success' | 'failed' | 'executed' | null;
    project?: ProjectWithVersions | null;
    mutateProject?: () => void;
}

const BuildView = forwardRef<ChatContainerRef, BuildViewProps>(({
    activeView,
    setActiveView,
    messages,
    isLoading,
    isExecuting,
    isResponseReady,
    isInputEnabled = true,
    isGenerating = null,
    hasReceivedApiResponse = false,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted,
    shouldShowPreview,
    previewStage,
    clonedUrl,
    previewUrl,
    projectStatus,
    project,
    mutateProject
}, ref) => {
    const chatContainerRef = useRef<ChatContainerRef>(null);
    
    useImperativeHandle(ref, () => ({
        scrollToBottom: () => chatContainerRef.current?.scrollToBottom(),
        showImageEditOverlay: () => chatContainerRef.current?.showImageEditOverlay(),
        hideImageEditOverlay: () => chatContainerRef.current?.hideImageEditOverlay()
    }));

    const renderPreview = () => {
        if (projectStatus === 'failed') {
            return (
                <div className="col-span-7 h-full white-box rounded p-2.5 flex items-center justify-center">
                    <h2 className="text-2xl font-bold text-red-500">Build failed</h2>
                </div>
            );
        }

        // Show "More context needed" when API explicitly returns isGenerating: false
        // This means the AI needs more context from the user to continue
        if (hasReceivedApiResponse && isGenerating === false) {
            return (
                <div className="col-span-7 h-full white-box rounded p-2.5">
                    <MoreContextNeeded />
                </div>
            );
        }

        return <PreviewContainer
            showPreview={shouldShowPreview}
            previewStage={previewStage}
            clonedUrl={clonedUrl}
            previewUrl={previewUrl}
            onSendMessage={onSendMessage}
            isLoading={isLoading || isExecuting}
            activeView={activeView}
            setActiveView={setActiveView}
            onShowImageEditOverlay={() => chatContainerRef.current?.showImageEditOverlay()}
            onHideImageEditOverlay={() => chatContainerRef.current?.hideImageEditOverlay()}
            project={project}
            mutateProject={mutateProject}
        />;
    };

    return (
        <AnimationContainer
            className="p-6 pt-page-padding h-screen max-h-screen grid grid-cols-10 gap-6"
            animationType="full"
        >
            <ChatContainer
                ref={chatContainerRef}
                activeView={activeView}
                setActiveView={setActiveView}
                messages={messages}
                isLoading={isLoading || isExecuting}
                isResponseReady={isResponseReady}
                isInputEnabled={isInputEnabled}
                previewUrl={previewUrl}
                clonedUrl={clonedUrl}
                onSendMessage={onSendMessage}
                onAnimationComplete={onAnimationComplete}
                onAllTasksCompleted={onAllTasksCompleted}
            />
            {renderPreview()}
        </AnimationContainer>
    );
});

BuildView.displayName = "BuildView";

export default BuildView;
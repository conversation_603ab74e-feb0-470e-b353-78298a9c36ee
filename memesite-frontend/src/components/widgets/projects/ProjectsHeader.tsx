"use client";

import { memo } from "react";
import { Plus, Table2 } from "lucide-react";
import Button from "@/components/ui/buttons/Button";
import { strs } from "@/constants/pages/projects/strs";

const ProjectsHeader = memo(() => {
  return (
    <div className="flex flex-col w-full justify-between">
      <div className="flex items-center justify-between gap-4">
        <h1 className="text-base text-white font-medium flex items-center justify-center">
          <Table2 className="inline-block mr-2 h-[var(--text-base)] w-auto" />
          {strs.title}
        </h1>
        <Button className="flex items-center pl-[0.3rem] gap-2 white-button-shadow">
          <div className="button-circle black-circle">
            <Plus className="h-[var(--text-sm)] w-auto" />
          </div>
          {strs.newProject}
        </Button>
      </div>
      {/* <div className="relative py-2 border-2 rounded-full mx-2 border-neutral-200">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <Search className="h-[var(--text-sm)] w-auto text-neutral-400" />
        </div>
        <input
          type="text"
          placeholder="Search for a project"
          className="block w-full border-0 outline-none focus:ring-0 pl-9 pr-3 py-2 rounded-md leading-5 focus:outline-none sm:text-sm"
        />
      </div> */}
    </div>
  );
});

ProjectsHeader.displayName = "ProjectsHeader";

export default ProjectsHeader;

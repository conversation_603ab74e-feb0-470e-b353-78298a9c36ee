"use client";

import { memo } from "react";
import {
  ProjectCard,
  ProjectCardImage,
  ProjectCardContent,
} from "@/components/widgets/projects/project/ProjectCard";
import { useProjectsContext } from "./ProjectContext";
import { timeAgo } from "@/utils/timeago";
import ProjectsFetchError from "./states/ProjectsFetchError";
import NoProjects from "./states/NoProjects";
import AnimationContainer from "@/components/layout/AnimationContainer";
import ProjectsListSkeleton from "@/components/skeletons/ProjectsListSkeleton";

const ProjectsContent = memo(() => {
  const { projects, isLoading, error } = useProjectsContext();

  if (isLoading) {
    return <ProjectsListSkeleton />;
  }

  if (error) {
    return <ProjectsFetchError error={error} />;
  }

  if (!projects || projects.length === 0) {
    return <NoProjects />;
  }

  return (
    <AnimationContainer className="h-full min-h-0 mask-fade-y">
      <div className="h-full overflow-y-auto">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2.5 pt-2.5">
          {projects.map((project) => {
            return (
              <ProjectCard key={project.id}>
                <ProjectCardImage
                  src="/images/background.webp"
                  alt={project.name}
                />
                <ProjectCardContent
                  title={project.name}
                  description={`Created ${timeAgo(project.createdAt)}`}
                />
              </ProjectCard>
            );
          })}
        </div>
      </div>
    </AnimationContainer>
  );
});

ProjectsContent.displayName = "ProjectsContent";

export default ProjectsContent;

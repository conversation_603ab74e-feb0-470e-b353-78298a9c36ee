"use client";

import { useRef, forwardRef, useImperative<PERSON><PERSON>le, useCallback, useEffect, useState, useMemo } from "react";
import { useAuth } from "@clerk/nextjs";
import SelectorButton from "@/components/ui/buttons/SelectorButton";
import BilderView from "./views/BilderView";
import LabView from "./views/LabView";
import ViewWrapper from "./views/ViewWrapper";
import ImageEditOverlay from "./ImageEditOverlay";
import ReplaceImagePreview from "./states/ReplaceImagePreview";
import type { ChatMessage, ViewType } from "@/types/chat";
import { viewOptions } from "@/constants/pages/projects/options";
import { strs } from "@/constants/pages/projects/strs";
import { useChatStore } from "@/stores/chatStore";
import { useEditModeStore } from "@/stores/editModeStore";
import { useLabStore } from "@/stores/labStore";
import { useUIStore } from "@/stores/uiStore";
import { useIframeImageBridge } from "@/hooks/useIframeImageBridge";

interface ChatContainerProps {
    activeView: ViewType;
    setActiveView: (view: ViewType) => void;
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    isInputEnabled?: boolean;
    previewUrl?: string | null;
    clonedUrl?: string;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
}

export interface ChatContainerRef {
    scrollToBottom: () => void;
    showImageEditOverlay: () => void;
    hideImageEditOverlay: () => void;
}

const ChatContainer = forwardRef<ChatContainerRef, ChatContainerProps>(({
    activeView,
    setActiveView,
    messages,
    isLoading,
    isResponseReady,
    isInputEnabled = true,
    previewUrl,
    clonedUrl,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted
}, ref) => {
    const { getToken } = useAuth();
    const chatContainerRef = useRef<HTMLDivElement>(null);
    const labChatContainerRef = useRef<HTMLDivElement>(null);
    const [showImageEditOverlay, setShowImageEditOverlay] = useState(false);
    const [isOverlayFromLabFlow, setIsOverlayFromLabFlow] = useState(false);

    const {
        labMessages,
        isLabLoading,
        isLabResponseReady,
        sendLabMessage,
        handleLabAnimationComplete
    } = useChatStore();

    const { isEditMode, isFromLabFlow } = useEditModeStore();
    const { selectedImageForUse, clearSelectedImage } = useLabStore();
    const { isAnalyticsVisible } = useUIStore();
    const { handleIframeImageReplace, currentIframeImage, isImageElement } = useIframeImageBridge();

    const scrollToBottom = useCallback(() => {
        const targetRef = activeView === 'lab' ? labChatContainerRef : chatContainerRef;
        if (targetRef.current) {
            targetRef.current.scrollTo({
                top: targetRef.current.scrollHeight,
                behavior: 'smooth'
            });
        }
    }, [activeView]);

    useImperativeHandle(ref, () => ({
        scrollToBottom,
        showImageEditOverlay: () => {
            const isLabFlowImage = isFromLabFlow && !!selectedImageForUse;
            const isIframeImage = isEditMode && isImageElement;

            setIsOverlayFromLabFlow(isLabFlowImage || isIframeImage);

            if (showImageEditOverlay) {
                setShowImageEditOverlay(false);
                setTimeout(() => {
                    setShowImageEditOverlay(true);
                }, 50);
            } else {
                setShowImageEditOverlay(true);
            }
        },
        hideImageEditOverlay: () => {
            if (isFromLabFlow && selectedImageForUse) {
                clearSelectedImage();
            }
            setShowImageEditOverlay(false);
            setIsOverlayFromLabFlow(false);
        }
    }));

    const handleLabMessage = useCallback((content: string) => {
        sendLabMessage(content, getToken);
        setTimeout(() => {
            scrollToBottom();
        }, 100);
    }, [sendLabMessage, getToken, scrollToBottom]);

    const handleLabAnimation = useCallback((messageId: string) => {
        handleLabAnimationComplete(messageId);
    }, [handleLabAnimationComplete]);

    useEffect(() => {
        if (activeView === 'lab' && labMessages.length > 0) {
            setTimeout(() => {
                scrollToBottom();
            }, 100);
        }
    }, [labMessages.length, activeView, scrollToBottom]);

    useEffect(() => {
        if (activeView === 'bilder' && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.type === 'tasks' && lastMessage.role === 'assistant') {
                setTimeout(() => {
                    scrollToBottom();
                }, 100);
            }
        }
    }, [messages, activeView, scrollToBottom]);

    useEffect(() => {
        if (activeView === 'lab' && isLabLoading) {
            // Only scroll once when loading starts, not continuously
            scrollToBottom();
        }
    }, [activeView, isLabLoading, scrollToBottom]);

    useEffect(() => {
        if (!isEditMode) {
            setShowImageEditOverlay(false);
            setIsOverlayFromLabFlow(false);
            if (!isFromLabFlow) {
                clearSelectedImage();
            }
        }
    }, [isEditMode, isFromLabFlow, clearSelectedImage]);

    useEffect(() => {
        if (!showImageEditOverlay) {
            setIsOverlayFromLabFlow(false);
        }
    }, [showImageEditOverlay]);

    const selectorOptions = useMemo(() => {
        // Lab tab should be enabled when there's a preview URL or cloned URL available
        const hasPreviewAvailable = !!(previewUrl || clonedUrl);

        return viewOptions.map(option => ({
            ...option,
            disabled: option.value === 'lab' && (isLoading || isEditMode || isAnalyticsVisible || !hasPreviewAvailable)
        }));
    }, [isLoading, isEditMode, isAnalyticsVisible, previewUrl, clonedUrl]);

    return (
        <div className="col-span-3 h-full rounded py-0 flex flex-col gap-0 min-h-0 relative">
            <SelectorButton
                options={selectorOptions}
                activeValue={activeView}
                onValueChange={(value: string) => {
                    if (value === 'lab' && isEditMode) {
                        useEditModeStore.getState().setEditMode(false);
                    }
                    setActiveView(value as ViewType);
                }}
            />
            <div className="h-full w-full flex flex-col gap-0 min-h-0 relative">
                <ReplaceImagePreview
                    title={strs.states.imageReplace.title}
                    subtitle={strs.states.imageReplace.subtitle}
                    selectedImage={selectedImageForUse}
                    isVisible={isEditMode && isFromLabFlow && !!selectedImageForUse && activeView === 'bilder'}
                />

                <ViewWrapper viewType="bilder" activeView={activeView} shouldHide={isEditMode && isFromLabFlow && !!selectedImageForUse}>
                    <BilderView
                        ref={chatContainerRef}
                        messages={messages}
                        isLoading={isLoading}
                        isResponseReady={isResponseReady}
                        isInputEnabled={isInputEnabled}
                        onSendMessage={onSendMessage}
                        onAnimationComplete={onAnimationComplete}
                        onAllTasksCompleted={onAllTasksCompleted}
                    />
                </ViewWrapper>

                <ViewWrapper viewType="lab" activeView={activeView}>
                    <LabView
                        ref={labChatContainerRef}
                        messages={labMessages}
                        isLoading={isLabLoading}
                        isResponseReady={isLabResponseReady}
                        onSendMessage={handleLabMessage}
                        onAnimationComplete={handleLabAnimation}
                        onAllTasksCompleted={undefined}
                    />
                </ViewWrapper>
            </div>
            <ImageEditOverlay
                isVisible={showImageEditOverlay}
                isFromLabFlow={isOverlayFromLabFlow}
                selectedImage={selectedImageForUse}
                currentIframeImage={currentIframeImage}
                onIframeImageReplace={handleIframeImageReplace}
            />
        </div>
    );
});

ChatContainer.displayName = "ChatContainer";

export default ChatContainer;
"use client"

import InputSection from "@/components/ui/input/InputSection"
import { strs } from "@/constants/pages/home/<USER>/strs"
import { useAuth } from "@clerk/nextjs"

export default function Hero() {
  const { isSignedIn } = useAuth()

  return (
    <>
      <div className="relative h-[100svh] lg:h-screen flex flex-col py-6 gap-0 lg:gap-[0.5rem] items-center justify-center">
        <div className="relative z-10 flex flex-col text-center items-center gap-1 text-black">
          <h1 className="text-5xl font-medium gradient-text-white py-1.5">{strs.title}</h1>
          <h2 className="text-sm lg:text-xl text-white/75">{strs.subtitle}</h2>
        </div>
        <div className="relative z-10 w-full h-fit p-5 flex items-center justify-center">
          <div className="relative z-10 w-90 lg:w-50 2xl:w-60 h-fit">
            <InputSection />
          </div>
        </div>
        <div className="absolute top-0 left-0 w-full h-full" >
          <img
            src="/images/staticfaded.webp"
            alt=""
            className={`absolute z-0 top-0 right-0 w-full min-w-screen h-full object-cover`}
          />
          {/* <video
            src="/videos/lights.mp4"
            className={`absolute z-0 top-0 right-0 w-full min-w-[100vw] h-full object-cover opacity-50`}
            style={isSignedIn ? { maskImage: 'linear-gradient(to right, transparent 0%, transparent 20%, black 50%, black 100%)' } : undefined}
            autoPlay
            loop
            muted
          /> */}
        </div>
      </div>
    </>
  )
}
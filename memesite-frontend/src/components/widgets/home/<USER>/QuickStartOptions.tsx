import { strs } from "@/constants/pages/home/<USER>/strs"

export default function QuickStartOptions() {
  return (
    <div className="p-5 mt-2 flex flex-col gap-3">
      <h4 className="text-sm text-black">{strs.quickStartTitle}</h4>
      <div className="grid grid-cols-4 gap-5 text-black">
        {strs.quickStartOptions.map((option, index) => (
          <button 
            key={index} 
            className="h-8 bg-[rgba(255,255,255,0.39)] rounded-full flex items-center justify-center text-sm cursor-pointer border-none outline-none"
            aria-label={`Create ${option}`}
          >
            {option}
          </button>
        ))}
      </div>
    </div>
  )
}
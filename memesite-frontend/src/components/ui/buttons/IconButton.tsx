"use client";

import { memo, ReactNode } from "react";
import Button from "./Button";
import Image from "next/image";

interface IconButtonProps {
    icon: string;
    alt: string;
    label?: string;
    children?: ReactNode;
    useChildren?: boolean;
    onClick?: () => void;
    className?: string;
    disabled?: boolean | undefined;
}

const IconButton = memo(({ icon, alt, label, children, useChildren = false, onClick, className = "", 
    disabled = false
 }: IconButtonProps) => {
    return (
        <Button disabled={disabled} className={`flex items-center pl-[0.4rem] gap-2 ${className}`} onClick={onClick}>
            <div className="button-circle black-circle">
                <Image 
                    src={icon}
                    alt={alt}
                    width={50} 
                    height={50} 
                    className="w-[40%] h-[40%] object-contain" 
                />
            </div>
            {useChildren ? children : label}
        </Button>
    );
});

IconButton.displayName = "IconButton";

export default IconButton;
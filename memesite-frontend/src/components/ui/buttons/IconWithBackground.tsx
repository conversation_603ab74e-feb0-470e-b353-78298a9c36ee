"use client";

import { memo } from "react";
import Image from "next/image";
import { LucideIcon } from "lucide-react";

interface IconWithBackgroundProps {
    icon: LucideIcon;
    className?: string;
}

const IconWithBackground = memo(({ 
    icon: Icon, 
    className = ""
}: IconWithBackgroundProps) => {
    return (
        <div className={`relative h-15 aspect-square flex items-center justify-center overflow-hidden rounded-sm black-card gradient-before-rounded-sm ${className}`}>
            <Icon className="relative z-10 h-[40%] w-auto text-white" strokeWidth={1.25} />
            <Image
                src="/images/smallstaticfaded.webp"
                alt="Background"
                width={1000}
                height={1000}
                className="absolute inset-0 p-1 rounded-sm z-0 object-cover"
            />
        </div>
    );
});

IconWithBackground.displayName = "IconWithBackground";

export default IconWithBackground;
"use client";

import { memo } from "react";
import { ButtonProps } from "@/types/components/Button";
import { useButtonEffects } from "@/hooks/useButtonEffects";

export default memo(function Button({ 
  children, 
  className = '', 
  styleClassName = 'black-button',
  onClick,
  disabled = false
}: ButtonProps) {
  const { handleMouseEnter, handleClick, buttonClassName } = useButtonEffects();

  return (
    <button
      disabled={disabled}
      className={`button text-xs ${styleClassName} ${className} ${buttonClassName} ${disabled ? 'opacity-80 cursor-not-allowed select-none pointer-events-none' : ''}`}
      onClick={(e) => handleClick(e, onClick)}
      onMouseEnter={handleMouseEnter}
    >
      {children}
    </button>
  )
});
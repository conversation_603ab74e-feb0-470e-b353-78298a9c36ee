"use client"

import Image from "next/image"
import { Play } from "lucide-react"
import { useButtonEffects } from "@/hooks/useButtonEffects"
import { useAuth } from "@clerk/nextjs"
import AnimationContainer from "@/components/layout/AnimationContainer"
import { useUIStore } from "@/stores/uiStore"

interface ShowreelButtonProps {
  imageSrc?: string
  text?: string
  className?: string
  onClick?: () => void
}

export default function ShowreelButton({ 
  imageSrc = "/images/thumbnailtest.png",
  text = "Watch Showreel",
  className = "",
  onClick
}: ShowreelButtonProps) {
  const { handleMouseEnter, handleClick, buttonClassName } = useButtonEffects<HTMLDivElement>()
  const { isSignedIn } = useAuth()
  const { isBobModeActive, setPendingBobMode, setIsBobModeActive } = useUIStore()


  const handleBobClick = () => {
    if (isSignedIn) {
      if (!isBobModeActive) {
        setPendingBobMode(true)
        setIsBobModeActive(true)
        const inputSection = document.querySelector('.white-box')
        if (inputSection) {
          inputSection.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      } else {
        setIsBobModeActive(false)
      }
    } else if (onClick) {
      onClick()
    }
  }

  return (
    <AnimationContainer
      key={isSignedIn ? "signed-in" : "signed-out"}
      className={className}
      animationType="fade"
    >
      <div
        className={`rounded-full black-button w-fit flex items-center gap-3 pr-6 cursor-pointer ${buttonClassName} ${isSignedIn ? 'h-10 pl-[0.32rem]' : 'h-11 pl-[0.35rem]'} ${isSignedIn && isBobModeActive ? 'white-button-shadow' : ''}`}
        onMouseEnter={handleMouseEnter}
        onClick={(e) => handleClick(e, handleBobClick)}
      >
        {isSignedIn ? (
          <>
            <div className="button-circle black-circle">
              <Image src="/icons/wand.svg" alt="Wand Icon" width={50} height={50} className="w-[40%] h-[40%] object-contain" />
            </div>
            <p className="text-xs text-white">Bild With Bob</p>
          </>
        ) : (
          <>
            <div className="relative h-9 aspect-2/1 rounded overflow-hidden flex items-center justify-center">
              <Play className="relative z-10 w-5 h-auto text-white drop-shadow-lg drop-shadow-black" fill="white" />
              <Image src={imageSrc} width={100} height={100} alt="" className="absolute top-0 left-0 w-full h-full object-cover rounded" />
              <div className="absolute inset-0 bg-[rgba(0,0,0,0.05)] pointer-events-none" />
              <div className="absolute inset-0 rounded shadow-[inset_0_0_10px_rgba(0,0,0,0.1)] pointer-events-none" />
            </div>
            <p className="text-sm text-white">{text}</p>
          </>
        )}
      </div>
    </AnimationContainer>
  )
}
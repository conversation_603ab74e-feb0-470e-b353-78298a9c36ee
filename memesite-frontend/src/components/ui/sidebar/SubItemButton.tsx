"use client";

import type { SidebarSubItem } from "@/types/sidebar";
import { getSubItemButtonClass } from "@/utils/sidebar";

interface SubItemButtonProps {
    subItem: SidebarSubItem;
    isActive: boolean;
    onClick: () => void;
}

const SubItemButton = ({ subItem, isActive, onClick }: SubItemButtonProps) => {
    return (
        <button
            onClick={onClick}
            className={getSubItemButtonClass(isActive)}
        >
            <span className="text-xs text-white whitespace-nowrap flex-grow text-left">
                {subItem.label}
            </span>
            {subItem.count !== undefined && (
                <div className="flex justify-center items-center">
                    <span className="text-xs text-white">
                        {subItem.count}
                    </span>
                </div>
            )}
        </button>
    );
};

export default SubItemButton;
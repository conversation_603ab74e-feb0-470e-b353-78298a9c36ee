"use client";

import { ChevronDown } from "lucide-react";
import type { SidebarCategory } from "@/types/sidebar";
import { getCategoryButtonClass, getChevronClass } from "@/utils/sidebar";

interface CategoryButtonProps {
    category: SidebarCategory;
    isActive: boolean;
    isExpanded: boolean;
    onClick: () => void;
}

const CategoryButton = ({ category, isActive, isExpanded, onClick }: CategoryButtonProps) => {
    const hasSubItems = category.subItems && category.subItems.length > 0;
    const finalIsActive = category.isActive !== undefined ? category.isActive : isActive;
    
    return (
        <button
            onClick={category.isComingSoon ? undefined : onClick}
            className={`group ${getCategoryButtonClass(finalIsActive)} ${category.isComingSoon ? 'cursor-not-allowed' : ''}`}
            disabled={category.isComingSoon}
        >
            <div className="z-10 flex justify-between items-center px-5 relative text-white">
                <div className="flex items-center gap-3 flex-grow relative overflow-hidden">
                    <div className={`h-[var(--text-sm)] w-auto flex-shrink-0 ${category.isComingSoon ? 'opacity-50' : ''}`}>
                        {category.icon}
                    </div>
                    <span className={`text-sm ${category.isComingSoon ? 'opacity-50' : ''}`}>
                        {category.label}
                    </span>
                </div>
                {hasSubItems && (
                    <ChevronDown className={getChevronClass(isExpanded)} />
                )}
            </div>
            <div className={`absolute inset-0 transition-opacity duration-[400ms] ${finalIsActive ? 'opacity-100' : category.isComingSoon ? 'opacity-0' : 'opacity-0 group-hover:opacity-100'}`}>
                <div className="w-full h-full black-button" />
            </div>
        </button>
    );
};

export default CategoryButton;
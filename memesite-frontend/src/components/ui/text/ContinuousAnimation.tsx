"use client";

import { memo } from "react";
import { ANIMATION_CONSTANTS } from "@/constants/animation";

interface ContinuousAnimationProps {
    text: string;
    className?: string;
    useFadedAnimation?: boolean;
}

const ContinuousAnimation = memo(({ text, className = "", useFadedAnimation = false }: ContinuousAnimationProps) => {
    const animationClass = useFadedAnimation ? "continuous-color-faded-animation" : "continuous-color-animation";

    return (
        <div className={className}>
            {text.split('').map((char, index) => (
                <span
                    key={index}
                    className={`${animationClass} inline-block`}
                    style={{ animationDelay: `${index * ANIMATION_CONSTANTS.CONTINUOUS_DELAY_MULTIPLIER}s` }}
                >
                    {char === " " ? "\u00A0" : char}
                </span>
            ))}
        </div>
    );
});

ContinuousAnimation.displayName = "ContinuousAnimation";

export default ContinuousAnimation;
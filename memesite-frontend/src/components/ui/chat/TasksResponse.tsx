"use client";

import { memo } from "react";
import { Check } from "lucide-react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import { useTasksResponse } from "@/hooks/useTasksResponse";
import type { TasksContent } from "@/types/chat";

interface TasksResponseProps {
    content: TasksContent;
    isAnimating: boolean;
    onAnimationComplete: () => void;
    taskCompletionTimes?: number[];
    onAllTasksCompleted?: () => void;
}

const TasksResponse = memo(({ content, isAnimating, onAnimationComplete, taskCompletionTimes, onAllTasksCompleted }: TasksResponseProps) => {
    const { completedTasks, isPaused } = useTasksResponse({
        content,
        isAnimating,
        onAnimationComplete,
        taskCompletionTimes,
        onAllTasksCompleted
    });

    return (
        <AnimationContainer
            className="relative w-full p-5 flex flex-col gap-5 black-button black-button-rounded text-white rounded"
            animationType="fade"
        >
            <h3 className="text-sm">{content.title}</h3>
            
            <div className="flex flex-col gap-2">
                {content.tasks.map((task, index) => {
                    const isCompleted = completedTasks.includes(index);
                    return (
                        <div key={index} className="flex gap-3 items-center">
                            <div className="relative h-4 aspect-square flex items-center justify-center flex-shrink-0">
                                <AnimationContainer
                                    key={isCompleted ? 'completed' : 'loading'}
                                    className="absolute inset-0"
                                    animationType="fade"
                                >
                                    {isCompleted ? (
                                        <div className="h-full w-full purple-button flex items-center justify-center rounded-full">
                                            <Check className="h-[0.55rem] w-auto text-black" strokeWidth={3} />
                                        </div>
                                    ) : (
                                        <div className={`h-full aspect-square rounded-full border border-black border-t-blue ${!isPaused ? 'animate-spin' : ''}`} />
                                    )}
                                </AnimationContainer>
                            </div>
                            <p className="text-sm">{task.text}</p>
                        </div>
                    );
                })}
            </div>
        </AnimationContainer>
    );
});

TasksResponse.displayName = "TasksResponse";

export default TasksResponse;
"use client";

import Image from "next/image";
import { ImageContent } from "@/types/chat";
import { LabImage } from "@/data/labImages";
import AnimationContainer from "@/components/layout/AnimationContainer";
import Button from "@/components/ui/buttons/Button";
import { useImageMessage } from "@/hooks/useImageMessage";
import { strs } from "@/constants/pages/projects/strs";

interface ImageMessageProps {
    content: ImageContent;
    onImageComplete?: () => void;
    wasStopped?: boolean;
    viewContext?: string;
    onImageApply?: () => void;
    onImageUse?: (image?: LabImage) => void;
}

const ImageMessage = ({ content, onImageComplete, wasStopped, viewContext, onImageApply, onImageUse }: ImageMessageProps) => {
    const {
        imageLoaded,
        isApplied,
        isUsed,
        isSaving,
        handleApply,
        handleUse
    } = useImageMessage({ content, onImageComplete, wasStopped, viewContext, onImageApply, onImageUse });

    return (
        <AnimationContainer
            key={imageLoaded ? "loaded" : "generating"}
            animationType="fade"
            className="w-full"
        >
            <div className="w-full">
                {!imageLoaded ? (
                    <Image
                        src="/images/generatingimage.webp"
                        alt="Generating image"
                        width={512}
                        height={512}
                        className="w-full h-auto"
                    />
                ) : wasStopped ? (
                    <div className="relative w-full h-50 black-card gradient-before-rounded rounded flex items-center justify-center ">
                        <p className="text-sm text-white">Generation stopped</p>
                    </div>
                ) : content.imageUrl ? (
                    <>
                        <Image
                            src={content.imageUrl}
                            alt={content.prompt}
                            width={512}
                            height={512}
                            className="w-full h-full object-cover rounded-sm"
                        />
                        {!isUsed && (
                            <div className="flex gap-2 mt-3">
                                {viewContext === "imageEdit" ? (
                                    !isApplied && (
                                        <Button
                                            styleClassName="black-button"
                                            className="w-full"
                                            onClick={handleApply}
                                        >
                                            {strs.lab.imageMessage.save}
                                        </Button>
                                    )
                                ) : (
                                    <>
                                        {!isApplied && (
                                            <Button
                                                styleClassName="black-button"
                                                className="flex-1"
                                                onClick={handleApply}
                                            >
                                                {strs.lab.imageMessage.save}
                                            </Button>
                                        )}
                                        <Button
                                            styleClassName="black-button"
                                            className={!isApplied ? "flex-1" : "w-full"}
                                            onClick={handleUse}
                                        >
                                            {strs.lab.imageMessage.use}
                                        </Button>
                                    </>
                                )}
                            </div>
                        )}
                    </>
                ) : (
                    <div className="w-full h-64 flex items-center justify-center bg-white/5 rounded-sm">
                        <p className="text-xs text-white/50">Failed to generate image</p>
                    </div>
                )}
            </div>
        </AnimationContainer>
    );
};

export default ImageMessage;
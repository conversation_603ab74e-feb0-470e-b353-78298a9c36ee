"use client";

import { memo, useEffect, useRef } from "react";
import AnimatedText from "../animation/AnimatedText";
import AnimationContainer from "@/components/layout/AnimationContainer";
import TasksResponse from "./TasksResponse";
import IframeMessage from "../iframe/IframeMessage";
import Image from "next/image";
import { strs } from "@/constants/pages/projects/strs";
import type { MessageType, TasksContent, IframeContent, ImageContent } from "@/types/chat";
import type { LabImage } from "@/data/labImages";
import ImageMessage from "./ImageMessage";

interface AssistantMessageProps {
    content: string | TasksContent | IframeContent | ImageContent;
    type?: MessageType;
    isAnimating: boolean;
    isThinking: boolean;
    onAnimationComplete: () => void;
    taskCompletionTimes?: number[];
    onAllTasksCompleted?: () => void;
    iframeCompleted?: boolean;
    imageCompleted?: boolean;
    manuallyStopped?: boolean;
    viewContext?: 'clone' | 'build' | 'lab' | 'imageEdit' | string;
    onImageApply?: () => void;
    onImageUse?: (image?: LabImage) => void;
}

const AssistantHeader = memo(({ showThinking }: { showThinking: boolean }) => (
    <AnimationContainer 
        key={showThinking ? "thinking" : "static"}
        className="mb-2 flex items-center"
        animationType="fade"
    >
        <Image 
            src="/brand/memesitedot.svg" 
            alt={strs.alt.logoDot} 
            width={50} 
            height={50} 
            className="w-[var(--text-3xl)] aspect-square" 
        />
        {showThinking ? (
            <AnimatedText 
                text={strs.chat.thinking}
                className="text-sm text-white"
                continuous={true}
            />
        ) : (
            <p className="text-sm text-white">{strs.chat.assistant}</p>
        )}
    </AnimationContainer>
));

AssistantHeader.displayName = "AssistantHeader";

const AssistantMessage = memo(({ content, type = "text", isAnimating, isThinking, onAnimationComplete, taskCompletionTimes, onAllTasksCompleted, iframeCompleted, imageCompleted, manuallyStopped, viewContext = 'build', onImageApply, onImageUse }: AssistantMessageProps) => {
    const hasContent = Boolean(content);
    const hasCalledComplete = useRef(false);
    
    useEffect(() => {
        if (type === "iframe" && iframeCompleted && !hasCalledComplete.current && onAnimationComplete) {
            hasCalledComplete.current = true;
            onAnimationComplete();
        }
        if (type === "image" && imageCompleted && !hasCalledComplete.current && onAnimationComplete) {
            hasCalledComplete.current = true;
            onAnimationComplete();
        }
    }, [type, iframeCompleted, imageCompleted, onAnimationComplete]);
    
    return (
        <>
            <AssistantHeader showThinking={isThinking || isAnimating} />
            {hasContent && (
                <div key="message-content">
                    {type === "tasks" ? (
                        <TasksResponse 
                            content={content as TasksContent}
                            isAnimating={isAnimating}
                            onAnimationComplete={onAnimationComplete}
                            taskCompletionTimes={taskCompletionTimes}
                            onAllTasksCompleted={onAllTasksCompleted}
                        />
                    ) : type === "iframe" ? (
                        <IframeMessage content={content as IframeContent} isCompleted={iframeCompleted} viewContext={viewContext as 'clone' | 'build'} />
                    ) : type === "image" ? (
                        <ImageMessage 
                            content={content as ImageContent} 
                            onImageComplete={imageCompleted ? undefined : onAnimationComplete}
                            wasStopped={manuallyStopped}
                            viewContext={viewContext}
                            onImageApply={onImageApply}
                            onImageUse={onImageUse}
                        />
                    ) : (
                        <AnimatedText 
                            text={content as string} 
                            className="text-sm leading-relaxed text-white/75"
                            onComplete={isAnimating ? onAnimationComplete : undefined}
                            instant={!isAnimating}
                        />
                    )}
                </div>
            )}
        </>
    );
});

AssistantMessage.displayName = "AssistantMessage";

export default AssistantMessage;
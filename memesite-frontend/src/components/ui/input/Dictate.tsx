"use client"

import { useCallback } from 'react'
import { Mic, MicOff } from 'lucide-react'
import Button from '../buttons/Button'
import { DictateProps } from '@/types/components/Dictate'
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition'

export default function Dictate({
  onTextChange,
  onStart,
  onStop,
  onError,
  onListeningChange,
  className = "",
  disabled = false,
  language = "en-US"
}: DictateProps) {
  const {
    isListening,
    isSupported,
    startListening,
    stopListening,
    reset
  } = useSpeechRecognition({
    language,
    onStart,
    onStop,
    onError,
    onResult: onTextChange,
    onListeningChange
  })

  const toggleListening = useCallback(() => {
    if (!isSupported || disabled) return

    if (isListening) {
      stopListening()
    } else {
      reset()
      startListening()
    }
  }, [isListening, isSupported, disabled, startListening, stopListening, reset])

  if (!isSupported) {
    return null
  }

  return (
    <Button
      className={`h-8 aspect-square px-0 flex items-center justify-center relative ${
        isListening ? 'white-button-shadow' : ''
      } ${className}`}
      onClick={toggleListening}
    >
      {isListening ? (
        <>
          <MicOff className="h-1/2 w-1/2" strokeWidth={1} />
          <div className="absolute inset-0 rounded-full bg-red-500/20 animate-pulse" />
        </>
      ) : (
        <Mic className="h-1/2 w-1/2" strokeWidth={1} />
      )}
    </Button>
  )
}

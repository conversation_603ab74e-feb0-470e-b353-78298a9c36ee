"use client";

import { memo } from "react";
import Image from "next/image";
import { X } from "lucide-react";
import { LabImage } from "@/data/labImages";

interface ImagePreviewProps {
    image: LabImage | null;
    onRemove: () => void;
}

const ImagePreview = memo(({ image, onRemove }: ImagePreviewProps) => {
    return (
        <div className={`relative flex items-center gap-3 p-0 rounded-sm transition-all duration-600 ease-in-out overflow-hidden ${image ? 'opacity-100 h-15 mb-2' : 'opacity-0 h-0 p-0 mb-0'
            }`}>
            {image && (
                <>
                    <div className="relative h-15 aspect-square rounded-sm overflow-hidden bg-white/10 group">
                        <div className="absolute z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-6 aspect-square black-box gradient-before-rounded-lg rounded-full overflow-hidden flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer" >
                            <X className="h-[40%] w-auto text-white" onClick={onRemove} />
                        </div>
                        <Image
                            src={image.url || image.src || ''}
                            alt={image.name || image.key || 'Image'}
                            fill
                            className="object-cover"
                            unoptimized={(image.src || '').startsWith('data:')}
                        />
                    </div>
                </>
            )}
        </div>
    );
});

ImagePreview.displayName = "ImagePreview";

export default ImagePreview;
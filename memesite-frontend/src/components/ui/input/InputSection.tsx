"use client";

import { memo } from "react";
import Button from "@/components/ui/buttons/Button";
import AutoExpandTextarea from "@/components/ui/input/AutoExpandTextarea";
import AnimationContainer from "@/components/layout/AnimationContainer";
import Dictate from "@/components/ui/input/Dictate";
import ImagePreview from "./ImagePreview";
import { ArrowUp, Plus, Square, AudioWaveform } from "lucide-react";
import Image from "next/image";
import { strs } from "@/constants/pages/home/<USER>/strs";
import { InputSectionProps } from "@/types/components/InputSection";
import { useInputSection } from "@/hooks/useInputSection";

export default memo(function InputSection({
  containerClassName = "",
  leftButtonsClassName = "",
  rightButtonsClassName = "",
  onSubmit,
  onInputChange,
  textareaMaxLines,
  isLoading = false,
  enableConversation = false,
  disabled = false,
}: InputSectionProps) {
  const {
    inputValue,
    isCloneMode,
    isDictating,
    textareaRef,
    isSignedIn,
    placeholder,
    handleSubmit,
    handleKeyDown,
    handleInputChange,
    handleCloneClick,
    handleVoiceInput,
    handleDictatingChange,
    handleImageRemove,
  } = useInputSection({
    onSubmit,
    onInputChange,
    isLoading,
    enableConversation,
  });

  return (
    <div
      className={`relative w-full black-input gradient-before-rounded-lg p-5 rounded-lg flex flex-col gap-0 ${containerClassName}`}
    >
      <ImagePreview image={null} onRemove={handleImageRemove} />
      <div className="flex flex-col gap-6">
        <AutoExpandTextarea
          ref={textareaRef}
          className="w-full h-fit bg-transparent border-none text-base text-white outline-none"
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          maxLines={textareaMaxLines}
          disabled={isLoading || disabled}
        />
        <div className="w-full flex items-center justify-between">
          <div className={`flex gap-3 ${leftButtonsClassName}`}>
            <Button
              className={`flex items-center pl-[0.3rem] gap-2 ${
                isCloneMode ? "white-button-shadow" : ""
              }`}
              onClick={handleCloneClick}
            >
              <div className="button-circle black-circle">
                <Image
                  src="/icons/clone.svg"
                  alt={strs.alt.clone}
                  width={50}
                  height={50}
                  className="w-[40%] h-[40%] object-contain"
                />
              </div>
              {strs.cloneButton}
            </Button>
          </div>
          <div className={`flex gap-3 ${rightButtonsClassName}`}>
            <Button className="h-8 aspect-square px-0 flex items-center justify-center white-button-shadow">
              <Plus className="h-1/2 w-1/2" strokeWidth={1} />
            </Button>
            <div className="flex gap-3">
              <Dictate
                onTextChange={handleVoiceInput}
                onListeningChange={handleDictatingChange}
              />
              {!isDictating && (
                <Button
                  className="h-8 aspect-square px-0 flex items-center justify-center"
                  styleClassName="purple-button"
                  onClick={handleSubmit}
                  disabled={isLoading || disabled}
                >
                  <AnimationContainer
                    key={
                      isLoading
                        ? "loading"
                        : inputValue.trim() ||
                          !isSignedIn ||
                          !enableConversation
                        ? "arrow"
                        : "audio"
                    }
                    className="w-full h-full flex items-center justify-center relative"
                    animationType="fade"
                  >
                    {isLoading ? (
                      <>
                        <div className="absolute h-5 aspect-square rounded-full border border-white/30 border-t-white animate-spin transition-opacity duration-400 group-hover:opacity-0" />
                        <div className="absolute h-3 aspect-square rounded-full bg-white animate-[circleAnimation_2s_ease-in-out_0.5s_infinite] transition-opacity duration-400 group-hover:opacity-0" />
                        <Square
                          className="absolute z-50 h-3.25 aspect-square fill-white opacity-0 group-hover:opacity-100 transition-opacity duration-400"
                          strokeWidth={0}
                        />
                      </>
                    ) : inputValue.trim() ||
                      !isSignedIn ||
                      !enableConversation ? (
                      <ArrowUp className="h-1/2 w-1/2" strokeWidth={1} />
                    ) : (
                      <AudioWaveform className="h-1/2 w-1/2" strokeWidth={1} />
                    )}
                  </AnimationContainer>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

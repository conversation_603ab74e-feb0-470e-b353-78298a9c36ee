"use client";

import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { ArrowUp, Trash, Square } from "lucide-react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import Button from "@/components/ui/buttons/Button";
import { usePopupStore } from "@/stores/popupStore";
import { useEditModeStore } from "@/stores/editModeStore";
import { usePopupInput } from "@/hooks/usePopupInput";
import { usePopupPosition } from "@/hooks/usePopupPosition";
import { usePopupFocus } from "@/hooks/usePopupFocus";

interface PopupInputProps {
  onSendMessage?: (content: string) => void;
  isLoading?: boolean;
}

const PopupInput = ({ onSendMessage, isLoading = false }: PopupInputProps) => {
  const { isVisible, position, hidePopup } = usePopupStore();
  const { isEditMode } = useEditModeStore();
  const [mounted, setMounted] = useState(false);

  const {
    value,
    setValue,
    handleSubmit,
    handleDelete,
    handleKeyDown
  } = usePopupInput(onSendMessage, isLoading);

  const { adjustedPosition, popupRef } = usePopupPosition(isVisible, position);
  const inputRef = usePopupFocus(isVisible);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isEditMode) {
      hidePopup();
    }
  }, [isEditMode, hidePopup]);

  if (!mounted || !isVisible || !isEditMode) return null;

  return createPortal(
    <div
      ref={popupRef}
      className="!fixed z-[9999] overflow-hidden rounded-full"
      style={{
        left: `${adjustedPosition.x}px`,
        top: `${adjustedPosition.y}px`
      }}
    >
      <div className="relative overflow-hidden w-full h-11 white-button rounded-full flex items-center px-5 gap-2">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1 bg-transparent outline-none text-sm text-black"
          placeholder="Ask for quick changes..."
        />
        <Button
          className="h-8 aspect-square px-0 flex items-center justify-center"
          styleClassName="blue-button"
          onClick={handleSubmit}
        >
          <AnimationContainer
            key={isLoading ? "loading" : "arrow"}
            className="w-full h-full flex items-center justify-center relative"
            animationType="fade"
          >
            {isLoading ? (
              <>
                <div className="absolute h-5 aspect-square rounded-full border border-white/30 border-t-white animate-spin transition-opacity duration-400 group-hover:opacity-0" />
                <div className="absolute h-3 aspect-square rounded-full bg-white animate-[circleAnimation_2s_ease-in-out_0.5s_infinite] transition-opacity duration-400 group-hover:opacity-0" />
                <Square className="absolute z-50 h-3.25 aspect-square fill-white opacity-0 group-hover:opacity-100 transition-opacity duration-400" strokeWidth={0} />
              </>
            ) : (
              <ArrowUp className="h-1/2 w-1/2" strokeWidth={1} />
            )}
          </AnimationContainer>
        </Button>
        <div className="h-5 w-px bg-white" />
        <button
          onMouseDown={(e) => e.preventDefault()}
          onClick={handleDelete}
          className="cursor-pointer"
          type="button"
        >
          <Trash className="h-[var(--text-sm)] w-auto text-black" />
        </button>
      </div>
    </div>,
    document.body
  );
};

export default PopupInput;
"use client"

import Modal from './Modal'
import VoiceConversationModal from './VoiceConversationModal'
import PricingModal from '@/components/widgets/projects/PricingModal'
import { useModalStore } from '@/stores/modalStore'

export default function ModalManager() {
  const { activeModal, closeModal } = useModalStore()

  const renderModalContent = () => {
    switch (activeModal) {
      case 'voice-conversation':
        return <VoiceConversationModal />
      case 'pricing':
        return <PricingModal />
      default:
        return null
    }
  }

  const getModalTitle = () => {
    switch (activeModal) {
      case 'voice-conversation':
        return 'Voice Conversation'
      case 'pricing':
        return ''
      default:
        return ''
    }
  }

  if (!activeModal) return null

  if (activeModal === 'pricing') {
    return <PricingModal />
  }

  return (
    <Modal
      isOpen={!!activeModal}
      onClose={closeModal}
      title={getModalTitle()}
    >
      {renderModalContent()}
    </Modal>
  )
}

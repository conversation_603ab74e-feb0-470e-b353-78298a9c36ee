"use client"

import { useEffect } from 'react'
import { X } from 'lucide-react'
import Button from '../buttons/Button'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
  showCloseButton?: boolean
  className?: string
  showHeader?: boolean
}

export default function Modal({
  isOpen,
  onClose,
  children,
  title,
  showCloseButton = true,
  className = "",
  showHeader = true
}: ModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[9999] bg-black/50 flex items-center justify-center">
      <div className={`absolute inset-0 w-full h-full min-h-screen flex flex-col ${className}`}>
        {showHeader && (title || showCloseButton) && (
          <div className="bg-white border-b border-gray-200 p-4 flex-shrink-0">
            <div className="flex items-center justify-between">
              {title && (
                <div>
                  <h2 className="text-xl font-semibold">{title}</h2>
                </div>
              )}
              {showCloseButton && (
                <Button
                  className="flex items-center justify-center w-10 h-10"
                  onClick={onClose}
                >
                  <X className="w-5 h-5" />
                </Button>
              )}
            </div>
          </div>
        )}

        <div className="flex-1 bg-white p-6 flex flex-col min-h-0">
          {children}
        </div>
      </div>
    </div>
  )
}

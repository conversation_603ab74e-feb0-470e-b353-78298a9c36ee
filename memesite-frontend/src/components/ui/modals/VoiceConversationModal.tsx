"use client"

import { useState, useEffect, useRef } from 'react'
import { Mi<PERSON>, MicOff } from 'lucide-react'
import Button from '../buttons/Button'
import { useModalStore } from '@/stores/modalStore'
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition'
import { useSpeechSynthesis } from '@/hooks/useSpeechSynthesis'
import { createMessageAPI } from '@/utils/api'
import { useAuth } from '@clerk/nextjs'

export default function VoiceConversationModal() {
  const { closeModal } = useModalStore()
  const { getToken } = useAuth()

  // TODO:: Hardcoded IDs for now, replace with correct values in future
  const projectId = "63c690a1-6c88-4a40-afc4-01aabf313121"
  const versionId = "cda515e8-0aee-4f63-a51f-8a5544b77d65"

  const [isMuted, setIsMuted] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [currentTranscript, setCurrentTranscript] = useState('')
  const [conversationHistory, setConversationHistory] = useState<Array<{
    type: 'user' | 'assistant'
    message: string
    timestamp: Date
  }>>([])

  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastTranscriptRef = useRef('')

  const {
    isListening,
    isSupported: isRecognitionSupported,
    error: recognitionError,
    startListening,
    stopListening,
    reset: resetRecognition
  } = useSpeechRecognition({
    language: 'en-US',
    continuous: true,
    interimResults: true,
    onStart: () => {
      console.log('Speech recognition started')
    },
    onStop: () => {
      console.log('Speech recognition stopped')
    },
    onError: (error) => {
      console.error('Speech recognition error:', error)
    },
    onResult: (transcript) => {
      setCurrentTranscript(transcript)
      lastTranscriptRef.current = transcript

      // Clear existing timeout
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
      }

      // Set new timeout for 2 seconds of silence
      silenceTimeoutRef.current = setTimeout(() => {
        if (transcript.trim()) {
          console.log('Silence timeout triggered, sending message:', transcript)
          handleSendMessage(transcript)
        }
      }, 2000)
    }
  })

  const {
    speak,
    stop: stopSpeaking
  } = useSpeechSynthesis({
    rate: 1,
    pitch: 1,
    volume: 1,
    onStart: () => {
      console.log('Speech synthesis started')
      setIsSpeaking(true)
      // Stop listening while speaking (only if not muted)
      if (isListening && !isMuted) {
        stopListening()
      }
      // Clear silence timeout when speaking starts
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
        silenceTimeoutRef.current = null
      }
    },
    onEnd: () => {
      console.log('Speech synthesis ended')
      setIsSpeaking(false)
      // Resume listening after speaking ends (only if not muted)
      if (!isMuted && !isListening) {
        setTimeout(() => {
          if (!isMuted && !isListening && !isSpeaking) {
            startListening()
          }
        }, 500)
      }
    },
    onError: (error) => {
      console.error('Speech synthesis error:', error)
      setIsSpeaking(false)
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
        silenceTimeoutRef.current = null
      }
    }
  })

  useEffect(() => {
    const handleSSEMessage = (event: CustomEvent) => {
      const data = event.detail

      if (data.type === 'message_created' && data.data.type === 'assistant') {
        const assistantMessage = data.data.message

        setConversationHistory(prev => [...prev, {
          type: 'assistant',
          message: assistantMessage,
          timestamp: new Date()
        }])

        speak(assistantMessage)
      }
    }

    window.addEventListener('sse-message', handleSSEMessage as EventListener)

    return () => {
      window.removeEventListener('sse-message', handleSSEMessage as EventListener)
    }
  }, [speak])

  useEffect(() => {
    if (isRecognitionSupported && !isListening && !isMuted && !isSpeaking) {
      const timer = setTimeout(() => {
        if (!isListening && !isMuted && !isSpeaking) {
          startListening()
        }
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [isRecognitionSupported, isListening, isMuted, isSpeaking, startListening])

  useEffect(() => {
    return () => {
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
      }
      stopListening()
      stopSpeaking()
    }
  }, [stopListening, stopSpeaking])

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isProcessing) {
      console.log('Message not sent - empty or processing')
      return
    }

    setIsProcessing(true)
    stopListening()

    try {
      const token = await getToken({
        template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
      })

      if (!token) {
        throw new Error('Authentication token not found')
      }

      setConversationHistory(prev => [...prev, {
        type: 'user',
        message: message.trim(),
        timestamp: new Date()
      }])

      await createMessageAPI(projectId, versionId, message.trim(), token)

      resetRecognition()
      setCurrentTranscript('')

    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleMuteToggle = () => {
    if (isMuted) {
      setIsMuted(false)
      setTimeout(() => {
        if (!isMuted && !isListening && !isSpeaking) {
          startListening()
        }
      }, 100)
    } else {
      setIsMuted(true)
      stopListening()
    }
  }

  const handleClose = () => {
    stopListening()
    stopSpeaking()
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current)
    }
    closeModal()
  }

  if (!isRecognitionSupported) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center">
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <MicOff className="w-10 h-10 text-red-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">
            Speech Recognition Not Supported
          </h3>
          <p className="text-gray-600 mb-8">
            Your browser does not support speech recognition. Please use a modern browser like Chrome, Firefox, or Safari.
          </p>
          <Button onClick={handleClose}>
            Close
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
              <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${
              isListening ? 'bg-green-500 animate-pulse' : 
              isSpeaking ? 'bg-blue-500 animate-pulse' : 
              'bg-gray-400'
            }`} />
            <span className="text-sm text-gray-600">
              {isListening ? 'Listening...' :
               isSpeaking ? 'Speaking...' :
               isProcessing ? 'Processing...' :
               'Ready'}
            </span>
          </div>
        </div>

      {recognitionError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <p className="text-red-700 text-sm">{recognitionError}</p>
        </div>
      )}

      {currentTranscript && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-blue-800 font-medium mb-2">You said:</p>
          <p className="text-blue-700">{currentTranscript}</p>
        </div>
      )}

      <div className="flex-1 overflow-y-auto mb-6 space-y-4">
        {conversationHistory.map((item, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg ${
              item.type === 'user' 
                ? 'bg-blue-100 ml-8' 
                : 'bg-gray-100 mr-8'
            }`}
          >
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs font-medium text-gray-600">
                {item.type === 'user' ? 'You' : 'Assistant'}
              </span>
              <span className="text-xs text-gray-500">
                {item.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <p className="text-gray-800">{item.message}</p>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-center pt-4 border-t border-gray-200">
        <Button
          className="flex items-center gap-2"
          onClick={handleMuteToggle}
        >
          {isMuted ? (
            <>
              <MicOff className="w-5 h-5" />
              Unmute Microphone
            </>
          ) : (
            <>
              <Mic className="w-5 h-5" />
              Mute Microphone
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

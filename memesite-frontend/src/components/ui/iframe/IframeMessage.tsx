"use client";

import { memo, useState } from "react";
import { IframeContent } from "@/types/chat";
import IframeHeader from "@/components/ui/iframe/IframeHeader";
import IframeStatusIndicator from "@/components/ui/iframe/IframeStatusIndicator";
import ScanningEffects from "@/components/ui/iframe/ScanningEffects";
import IframeLoadingOverlay from "@/components/ui/iframe/IframeLoadingOverlay";

interface IframeMessageProps {
    content: IframeContent;
    isCompleted?: boolean;
    viewContext?: 'clone' | 'build';
}

const IframeMessage = memo(({ content, isCompleted = false, viewContext = 'build' }: IframeMessageProps) => {
    const [isIframeLoading, setIsIframeLoading] = useState(true);
    const shouldShowScanEffects = !isCompleted && !isIframeLoading;

    return (
        <div className="w-full white-box rounded p-2.5 flex flex-col gap-2">
            <IframeHeader isCompleted={isCompleted} />
            <div className="w-full h-fit p-2.5 rounded big-box-gradient flex flex-col gap-2.5">
                <IframeStatusIndicator isCompleted={isCompleted} />
                <div className="relative w-full">
                    <ScanningEffects isVisible={shouldShowScanEffects} />
                    <IframeLoadingOverlay isLoading={isIframeLoading} />
                    
                    <iframe
                        src={content.url}
                        title={content.title || "Website preview"}
                        className={`relative z-10 w-full ${viewContext === 'clone' ? 'aspect-[16/8]' : 'aspect-square'} border-0 rounded pointer-events-none`}
                        sandbox="allow-scripts allow-same-origin"
                        onLoad={() => setIsIframeLoading(false)}
                    />
                </div>
            </div>
        </div>
    );
});

IframeMessage.displayName = "IframeMessage";

export default IframeMessage;
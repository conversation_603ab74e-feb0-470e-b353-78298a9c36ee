import { memo } from "react";

type CornerPosition = "top-0 left-0" | "top-0 right-0" | "bottom-0 left-0" | "bottom-0 right-0";
type CornerBorder = "border-t-4 border-l-4" | "border-t-4 border-r-4" | "border-b-4 border-l-4" | "border-b-4 border-r-4";
type CornerRadius = "rounded-tl" | "rounded-tr" | "rounded-bl" | "rounded-br";

const CORNER_CONFIGS: Array<{
    position: CornerPosition;
    border: CornerBorder;
    radius: CornerRadius;
}> = [
    { position: "top-0 left-0", border: "border-t-4 border-l-4", radius: "rounded-tl" },
    { position: "top-0 right-0", border: "border-t-4 border-r-4", radius: "rounded-tr" },
    { position: "bottom-0 left-0", border: "border-b-4 border-l-4", radius: "rounded-bl" },
    { position: "bottom-0 right-0", border: "border-b-4 border-r-4", radius: "rounded-br" },
];

const SHADOW_POSITIONS: CornerPosition[] = [
    "top-0 left-0",
    "top-0 right-0",
    "bottom-0 left-0",
    "bottom-0 right-0",
];

interface ScanningEffectsProps {
    isVisible: boolean;
}

const ScanningEffects = memo(({ isVisible }: ScanningEffectsProps) => {
    const effectClasses = `absolute inset-0 pointer-events-none transition-opacity duration-500 ease-in-out ${isVisible ? "opacity-100" : "opacity-0"}`;

    return (
        <>
            <div className={effectClasses}>
                {SHADOW_POSITIONS.map((position) => (
                    <div
                        key={`shadow-${position}`}
                        className={`absolute ${position} w-[2.5rem] h-[2.5rem] bg-blue rounded z-0 shadow-[0_0_12px_4px_rgba(90,113,230,0.6)]`}
                    />
                ))}
            </div>

            <div className={`${effectClasses} z-20`}>
                {CORNER_CONFIGS.map(({ position, border, radius }) => (
                    <div
                        key={`border-${position}`}
                        className={`absolute ${position} w-[2.5rem] h-[2.5rem] ${border} border-[#5A71E6] ${radius}`}
                    />
                ))}
            </div>

            <div className={`${effectClasses} z-30`}>
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[80%] h-1 bg-blue shadow-[0_0_12px_4px_rgba(90,113,230,0.6)] rounded-[100%] animate-[scan_8s_ease-in-out_infinite]" />
            </div>
        </>
    );
});

ScanningEffects.displayName = "ScanningEffects";

export default ScanningEffects;
"use client";

import { memo, useState, useCallback } from "react";
import type { ChatMessage } from "@/types/chat";
import { ChatMessageProps } from "@/types/components/ChatMessage";
import UserMessage from "./chat/UserMessage";
import AssistantMessage from "./chat/AssistantMessage";

const ChatMessage = memo(({ message, isAnimated = false, isThinking = false, onAnimationComplete, onAllTasksCompleted, viewContext = 'build', onImageApply, onImageUse }: ChatMessageProps) => {
    const isUser = message.role === "user";
    const [hasCompletedAnimation, setHasCompletedAnimation] = useState(false);
    const isStillAnimating = isAnimated && !hasCompletedAnimation;

    const handleAnimationComplete = useCallback(() => {
        setHasCompletedAnimation(true);
        onAnimationComplete?.();
    }, [onAnimationComplete]);

    const isTasksResponse = message.type === "tasks" && message.role === "assistant";
    const isIframeResponse = message.type === "iframe" && message.role === "assistant";
    const isImageResponse = message.type === "image" && message.role === "assistant";
    const isFullWidthResponse = isTasksResponse || isIframeResponse || isImageResponse;
    const messageClasses = `w-full flex ${isUser ? "justify-end" : "justify-start"} mb-6`;
    const containerClasses = `${isFullWidthResponse ? "w-full" : "max-w-[85%]"} ${isUser ? "order-2" : "order-1"}`;

    return (
        <div className={messageClasses}>
            <div className={containerClasses}>
                {isUser ? (
                    <UserMessage content={message.content as string} />
                ) : (
                    <AssistantMessage 
                        content={message.content}
                        type={message.type}
                        isAnimating={isStillAnimating}
                        isThinking={isThinking}
                        onAnimationComplete={handleAnimationComplete}
                        taskCompletionTimes={message.taskCompletionTimes}
                        onAllTasksCompleted={onAllTasksCompleted}
                        iframeCompleted={message.iframeCompleted}
                        imageCompleted={message.imageCompleted}
                        manuallyStopped={message.manuallyStopped}
                        viewContext={viewContext}
                        onImageApply={onImageApply}
                        onImageUse={onImageUse}
                    />
                )}
            </div>
        </div>
    );
});

ChatMessage.displayName = "ChatMessage";

export default ChatMessage;
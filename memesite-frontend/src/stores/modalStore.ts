import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export type ModalType = 'empty-input' | 'voice-conversation' | 'pricing' | null

interface ModalState {
  activeModal: ModalType
  modalData: Record<string, unknown>
  
  openModal: (type: ModalType, data?: Record<string, unknown>) => void
  closeModal: () => void
  setModalData: (data: Record<string, unknown>) => void
}

export const useModalStore = create<ModalState>()(
  devtools(
    (set) => ({
      activeModal: null,
      modalData: {},
      
      openModal: (type, data = {}) => set({
        activeModal: type,
        modalData: data
      }),
      
      closeModal: () => set({
        activeModal: null,
        modalData: {}
      }),
      
      setModalData: (data) => set((state) => ({
        modalData: { ...state.modalData, ...data }
      }))
    }),
    {
      name: 'modal-store'
    }
  )
)

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ChatMessage, IframeContent, ImageContent, TasksContent, MessageType } from '@/types/chat';
import { AIResponse } from '@/types/aiResponse';
import { ProjectMessage } from '@/types/project';
import { ANIMATION_CONSTANTS } from '@/constants/animation';
import { getMessagesAPI, generateImageAPI } from '@/utils/api';

interface PendingTasksResponse {
    content: string | TasksContent | IframeContent | ImageContent;
    type: MessageType;
    showPreview?: boolean;
    taskCompletionTimes?: number[];
    onPreviewReset?: () => void;
}

interface ChatState {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    responseIndex: number;
    iframeCompletionTimers: Map<string, NodeJS.Timeout>;
    responseTimeout: NodeJS.Timeout | null;
    pendingTasksResponse: PendingTasksResponse | null;
    
    labMessages: ChatMessage[];
    isLabLoading: boolean;
    isLabResponseReady: boolean;
    labResponseTimeout: NodeJS.Timeout | null;
    
    addMessage: (message: ChatMessage) => void;
    updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
    setMessages: (messages: ChatMessage[]) => void;
    clearMessages: () => void;
    
    addLabMessage: (message: ChatMessage) => void;
    updateLabMessage: (id: string, updates: Partial<ChatMessage>) => void;
    clearLabMessages: () => void;
    sendLabMessage: (content: string, getToken?: () => Promise<string | null>) => Promise<void>;
    handleLabAnimationComplete: (messageId: string) => void;
    handleStopLabGeneration: () => void;
    
    fetchMessages: (projectId: string, getToken: () => Promise<string | null>) => Promise<void>;
    sendMessage: (content: string, responses: AIResponse[], skipDelay?: boolean, onPreviewReset?: () => void) => Promise<void>;
    handleAnimationComplete: (messageId: string) => void;
    handleStopGeneration: () => void;
    startIframeCompletion: (messageId: string) => void;
    
    setLoading: (loading: boolean) => void;
    setResponseReady: (ready: boolean) => void;
    incrementResponseIndex: () => void;
}

export const useChatStore = create<ChatState>()(
    devtools(
        (set, get) => ({
            messages: [],
            isLoading: false,
            isResponseReady: false,
            pendingTasksResponse: null,
            responseIndex: 0,
            iframeCompletionTimers: new Map(),
            responseTimeout: null,
            
            labMessages: [],
            isLabLoading: false,
            isLabResponseReady: false,
            labResponseTimeout: null,
            
            addMessage: (message) => set((state) => ({
                messages: [...state.messages, message]
            })),
            
            updateMessage: (id, updates) => set((state) => ({
                messages: state.messages.map(msg =>
                    msg.id === id ? { ...msg, ...updates } : msg
                )
            })),
            
            setMessages: (messages) => set({ messages }),
            
            clearMessages: () => {
                const { iframeCompletionTimers } = get();
                iframeCompletionTimers.forEach(timer => clearTimeout(timer));
                set({ messages: [], responseIndex: 0, iframeCompletionTimers: new Map() });
            },

            fetchMessages: async (projectId, getToken) => {
                set({ isLoading: true });
                const token = await getToken();
                if (!token) {
                    set({ isLoading: false });
                    return;
                }

                const allMessages: ProjectMessage[] = [];
                let currentPage = 1;
                let totalPages = 1;
                let cursor: string | undefined = undefined;

                do {
                    try {
                        const response = await getMessagesAPI(projectId, token, 20, cursor);
                        const filteredMessages = response.data.filter(
                            (msg) => !msg.is_system
                        );
                        allMessages.push(...filteredMessages);

                        totalPages = response.meta.totalPages;
                        cursor = response.meta.nextCursor;
                        currentPage++;

                        if (!response.meta.hasNextPage) {
                            break;
                        }
                    } catch {
                        break;
                    }
                } while (currentPage <= totalPages);


                const chatMessages: ChatMessage[] = allMessages
                    .map((msg): ChatMessage => ({
                        id: msg.id,
                        role: msg.type,
                        content: msg.message,
                        type: 'text',
                        timestamp: new Date(msg.createdAt),
                    }))
                    .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

                set({ messages: chatMessages, isLoading: false });
            },
            
            sendMessage: async (content, responses, skipDelay = false, onPreviewReset) => {
                const { addMessage, updateMessage, incrementResponseIndex, isLoading, handleStopGeneration } = get();
                
                if (isLoading && !content.trim()) {
                    handleStopGeneration();
                    return;
                }
                
                if (!content.trim()) return;
                
                set({ isLoading: true });
                
                const userMessage: ChatMessage = {
                    id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                    role: "user",
                    content: content,
                    type: "text",
                    timestamp: new Date()
                };
                
                const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                const thinkingMessage: ChatMessage = {
                    id: aiMessageId,
                    role: "assistant",
                    content: "",
                    type: "text",
                    timestamp: new Date()
                };
                
                addMessage(userMessage);
                addMessage(thinkingMessage);
                
                const delay = skipDelay ? 0 : ANIMATION_CONSTANTS.AI_RESPONSE_DELAY;
                
                const timeout = setTimeout(() => {
                    if (responses.length === 0) {
                        updateMessage(aiMessageId, {
                            content: "I'm sorry, I don't have a response for that.",
                            type: 'text',
                            animationComplete: true
                        });
                        set({ isLoading: false, isResponseReady: false });
                        return;
                    }
                    const responseIdx = get().responseIndex;
                    const firstResponse = responses[responseIdx * 2 % responses.length];
                    const secondResponse = responses[(responseIdx * 2 + 1) % responses.length];
                    
                    if (firstResponse.type === "tasks" && !firstResponse.showPreview && onPreviewReset) {
                        onPreviewReset();
                    }
                    
                    updateMessage(aiMessageId, {
                        content: firstResponse.content,
                        type: firstResponse.type,
                        showPreview: firstResponse.showPreview,
                        taskCompletionTimes: firstResponse.taskCompletionTimes,
                        animationStartTime: Date.now(),
                        animationComplete: false // Reset animation state for new content
                    });
                    
                    if (firstResponse.type === 'iframe') {
                        get().startIframeCompletion(aiMessageId);
                    }
                    
                    set({ isResponseReady: true, responseTimeout: null });
                    
                    if (secondResponse && secondResponse.type === "tasks") {
                        set({ pendingTasksResponse: {
                            content: secondResponse.content,
                            type: secondResponse.type,
                            showPreview: secondResponse.showPreview,
                            taskCompletionTimes: secondResponse.taskCompletionTimes,
                            onPreviewReset
                        }});
                    }
                    
                    incrementResponseIndex();
                }, delay);
                
                set({ responseTimeout: timeout });
            },
            
            handleAnimationComplete: (messageId) => {
                const { updateMessage, pendingTasksResponse, addMessage } = get();
                updateMessage(messageId, { animationComplete: true });
                
                if (pendingTasksResponse) {
                    const secondAiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                    const secondAiMessage: ChatMessage = {
                        id: secondAiMessageId,
                        role: "assistant",
                        content: pendingTasksResponse.content,
                        type: pendingTasksResponse.type,
                        timestamp: new Date(),
                        showPreview: pendingTasksResponse.showPreview,
                        taskCompletionTimes: pendingTasksResponse.taskCompletionTimes,
                        animationStartTime: Date.now()
                    };
                    
                    addMessage(secondAiMessage);
                    
                    if (!pendingTasksResponse.showPreview && pendingTasksResponse.onPreviewReset) {
                        pendingTasksResponse.onPreviewReset();
                    }
                    
                    set({ pendingTasksResponse: null });
                } else {
                    set({ isLoading: false, isResponseReady: false });
                }
            },
            
            handleStopGeneration: () => {
                const { responseTimeout, messages, updateMessage, iframeCompletionTimers } = get();
                
                if (responseTimeout) {
                    clearTimeout(responseTimeout);
                }
                
                const lastMessage = messages[messages.length - 1];
                if (lastMessage?.role === 'assistant') {
                    if (lastMessage.content === '') {
                        set((state) => ({
                            messages: state.messages.slice(0, -1)
                        }));
                    } else if (lastMessage.type === 'iframe') {
                        const timer = iframeCompletionTimers.get(lastMessage.id);
                        if (timer) {
                            clearTimeout(timer);
                            iframeCompletionTimers.delete(lastMessage.id);
                        }
                        updateMessage(lastMessage.id, {
                            iframeCompleted: true,
                            animationComplete: true,
                            manuallyStopped: true
                        });
                    } else if (typeof lastMessage.content === 'string' && lastMessage.animationStartTime) {
                        const elapsedTime = Date.now() - lastMessage.animationStartTime;
                        const visibleChars = Math.floor(elapsedTime / ANIMATION_CONSTANTS.CHAR_DELAY);
                        const truncatedContent = lastMessage.content.substring(0, visibleChars);
                        
                        updateMessage(lastMessage.id, {
                            content: truncatedContent,
                            animationComplete: true
                        });
                    } else {
                        updateMessage(lastMessage.id, {
                            animationComplete: true
                        });
                    }
                }
                
                set({ isLoading: false, isResponseReady: false, responseTimeout: null });
            },
            
            startIframeCompletion: (messageId: string) => {
                const { messages, updateMessage, iframeCompletionTimers } = get();
                const message = messages.find(msg => msg.id === messageId);
                
                if (message?.type === 'iframe' && !message.iframeCompleted) {
                    const content = message.content as IframeContent;
                    const completionTime = content.completionTime || 3000;
                    
                    const existingTimer = iframeCompletionTimers.get(messageId);
                    if (existingTimer) {
                        clearTimeout(existingTimer);
                    }
                    
                    const timer = setTimeout(() => {
                        updateMessage(messageId, { iframeCompleted: true });
                        iframeCompletionTimers.delete(messageId);
                    }, completionTime);
                    
                    iframeCompletionTimers.set(messageId, timer);
                }
            },
            
            setLoading: (loading) => set({ isLoading: loading }),
            setResponseReady: (ready) => set({ isResponseReady: ready }),
            incrementResponseIndex: () => set((state) => ({ responseIndex: state.responseIndex + 1 })),
            
            addLabMessage: (message) => set((state) => ({
                labMessages: [...state.labMessages, message]
            })),
            
            updateLabMessage: (id, updates) => set((state) => ({
                labMessages: state.labMessages.map(msg =>
                    msg.id === id ? { ...msg, ...updates } : msg
                )
            })),
            
            clearLabMessages: () => set({ labMessages: [] }),
            
            sendLabMessage: async (content, getToken) => {
                const { addLabMessage, updateLabMessage, isLabLoading, handleStopLabGeneration } = get();
                
                if (isLabLoading && !content.trim()) {
                    handleStopLabGeneration();
                    return;
                }
                
                if (!content.trim()) return;
                
                set({ isLabLoading: true });
                
                const userMessage: ChatMessage = {
                    id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                    role: "user",
                    content: content,
                    type: "text",
                    timestamp: new Date()
                };
                
                const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                const imageMessage: ChatMessage = {
                    id: aiMessageId,
                    role: "assistant",
                    content: {
                        prompt: content,
                        imageUrl: undefined,
                        isGenerating: true,
                        generationTime: 3000
                    } as ImageContent,
                    type: "image",
                    timestamp: new Date(),
                    imageCompleted: false
                };
                
                addLabMessage(userMessage);
                addLabMessage(imageMessage);
                
                // Use real API if token is available, otherwise use mock
                if (getToken) {
                    try {
                        const token = await getToken();
                        if (token) {
                            const response = await generateImageAPI(content, token);
                            
                            // Update the message with the real generated image
                            updateLabMessage(aiMessageId, { 
                                imageCompleted: true,
                                content: {
                                    prompt: content,
                                    imageUrl: response.url,
                                    key: response.key, // Store the key for save operations
                                    isGenerating: false,
                                    generationTime: 3000
                                } as ImageContent
                            });
                            
                            set({ isLabLoading: false, isLabResponseReady: false });
                            return;
                        }
                    } catch (error) {
                        console.error('Failed to generate image:', error);
                        // Show error state in the message
                        updateLabMessage(aiMessageId, {
                            imageCompleted: true,
                            manuallyStopped: true,
                            content: {
                                prompt: content,
                                imageUrl: undefined,
                                isGenerating: false,
                                generationTime: 3000
                            } as ImageContent
                        });
                        set({ isLabLoading: false, isLabResponseReady: false });
                        return;
                    }
                }
                
                // Mock behavior (fallback)
                const labMessageCount = get().labMessages.filter(m => m.type === 'image').length;
                const imageIndex = (labMessageCount % 4) + 1;
                const imageUrl = `/preview/demobg${imageIndex}.webp?id=${aiMessageId}`;
                
                const timeout = setTimeout(() => {
                    updateLabMessage(aiMessageId, { 
                        imageCompleted: true,
                        content: {
                            prompt: content,
                            imageUrl: imageUrl,
                            isGenerating: false,
                            generationTime: 3000
                        } as ImageContent
                    });
                    set({ isLabLoading: false, isLabResponseReady: false });
                }, 3000);
                
                set({ labResponseTimeout: timeout });
            },
            
            handleLabAnimationComplete: (messageId) => {
                const { updateLabMessage } = get();
                updateLabMessage(messageId, { animationComplete: true });
                set({ isLabLoading: false, isLabResponseReady: false });
            },
            
            handleStopLabGeneration: () => {
                const { labResponseTimeout, labMessages, updateLabMessage } = get();
                
                if (labResponseTimeout) {
                    clearTimeout(labResponseTimeout);
                }
                
                const lastMessage = labMessages[labMessages.length - 1];
                if (lastMessage?.role === 'assistant' && lastMessage.type === 'image') {
                    updateLabMessage(lastMessage.id, {
                        imageCompleted: true,
                        manuallyStopped: true,
                        content: {
                            ...lastMessage.content as ImageContent,
                            isGenerating: false,
                            imageUrl: undefined
                        }
                    });
                }
                
                set({ 
                    isLabLoading: false, 
                    isLabResponseReady: false,
                    labResponseTimeout: null
                });
            }
        }),
        {
            name: 'chat-store'
        }
    )
);
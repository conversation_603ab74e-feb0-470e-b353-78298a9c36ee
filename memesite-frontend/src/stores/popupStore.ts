import { create } from 'zustand';

interface PopupState {
  isVisible: boolean;
  position: { x: number; y: number; width?: number };
  value: string;
  selector: string;
  setPopupData: (data: { isVisible: boolean; position?: { x: number; y: number; width?: number }; value?: string; selector?: string }) => void;
  hidePopup: () => void;
}

export const usePopupStore = create<PopupState>((set) => ({
  isVisible: false,
  position: { x: 0, y: 0, width: 0 },
  value: '',
  selector: '',
  setPopupData: (data) => set((state) => ({ ...state, ...data })),
  hidePopup: () => set({ isVisible: false })
}));
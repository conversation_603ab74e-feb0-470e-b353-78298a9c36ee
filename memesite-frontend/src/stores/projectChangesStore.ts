import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface Change {
  id: string;
  type: "updateText" | "updateStyle" | "updateAttribute" | "deleteElement" | "addElement";
  selector: string;
  timestamp: number;
  data: {
    property?: string;
    oldValue?: string | number | boolean | null;
    newValue?: string | number | boolean | null;
    element?: {
      tagName: string;
      attributes?: Record<string, string>;
      content?: string;
    };
  };
}

interface ProjectChangesStore {
  changes: Change[];
  isDirty: boolean;
  addChange: (change: Omit<Change, "id" | "timestamp">) => void;
  clearChanges: () => void;
  removeChange: (id: string) => void;
  applyChanges: () => Promise<void>;
  undoLastChange: () => void;
  getChangesForExport: () => {
    timestamp: number;
    totalChanges: number;
    operations: Array<{
      type: Change['type'];
      selector: string;
      property?: string;
      oldValue?: string | number | boolean | null;
      newValue?: string | number | boolean | null;
      element?: {
        tagName: string;
        attributes?: Record<string, string>;
        content?: string;
      };
    }>;
  };
}

export const useProjectChangesStore = create<ProjectChangesStore>()(
  devtools(
    (set, get) => ({
      changes: [],
      isDirty: false,

      addChange: (change) => {
        const newChange: Change = {
          ...change,
          id: `change-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          timestamp: Date.now(),
        };

        set((state) => ({
          changes: [...state.changes, newChange],
          isDirty: true,
        }));
      },

      clearChanges: () => {
        set({ changes: [], isDirty: false });
      },

      removeChange: (id) => {
        set((state) => ({
          changes: state.changes.filter((change) => change.id !== id),
          isDirty: state.changes.length > 1,
        }));
      },

      applyChanges: async () => {
        set({ changes: [], isDirty: false });

        return Promise.resolve();
      },

      undoLastChange: () => {
        set((state) => {
          if (state.changes.length === 0) return state;

          const newChanges = state.changes.slice(0, -1);
          return {
            changes: newChanges,
            isDirty: newChanges.length > 0,
          };
        });
      },

      getChangesForExport: () => {
        const changes = get().changes;
        return {
          timestamp: Date.now(),
          totalChanges: changes.length,
          operations: changes.map((change) => ({
            type: change.type,
            selector: change.selector,
            ...change.data,
          })),
        };
      },
    }),
    {
      name: "project-changes-store",
    }
  )
);
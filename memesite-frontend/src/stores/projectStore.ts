import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ProjectData, CreateProjectRequest } from '@/types/project';
import { getProject, deleteProject } from '@/utils/projectStorage';
import { createProjectAPI } from '@/utils/api';

interface PendingMessage {
    message: string;
    mode: 'build' | 'clone' | 'bob';
}

interface ProjectState {
    currentProject: ProjectData | null;
    projects: ProjectData[];
    isLoading: boolean;
    error: string | null;
    pendingMessage: PendingMessage | null;
    isSubmittingMessage: boolean;

    setCurrentProject: (project: ProjectData) => void;
    updateProject: (id: string, updates: Partial<ProjectData>) => void;
    loadProject: (id: string) => Promise<void>;
    createProject: (data: CreateProjectRequest, getToken: () => Promise<string | null>) => Promise<{ id: string } | null>;
    removeProject: (id: string) => void;
    clearError: () => void;
    setPendingMessage: (message: PendingMessage) => void;
    clearPendingMessage: () => void;
    setSubmittingMessage: (isSubmitting: boolean) => void;
}

export const useProjectStore = create<ProjectState>()(
    devtools(
        (set) => ({
            currentProject: null,
            projects: [],
            isLoading: false,
            error: null,
            pendingMessage: null,
            isSubmittingMessage: false,

            setCurrentProject: (project) => set({ currentProject: project }),

            updateProject: (id, updates) => set((state) => ({
                projects: state.projects.map(p =>
                    p.id === id ? { ...p, ...updates } : p
                ),
                currentProject: state.currentProject?.id === id
                    ? { ...state.currentProject, ...updates }
                    : state.currentProject
            })),

            loadProject: async (id) => {
                set({ isLoading: true, error: null });
                try {
                    const project = getProject(id);
                    if (project) {
                        set({ currentProject: project, isLoading: false });
                    } else {
                        set({ error: 'Project not found', isLoading: false });
                    }
                } catch {
                    set({ error: 'Failed to load project', isLoading: false });
                }
            },

            createProject: async (data, getToken) => {
                set({ isLoading: true, error: null });

                try {
                    const token = await getToken();
                    if (!token) {
                        throw new Error('Authentication token not found');
                    }

                    const response = await createProjectAPI(data, token);
                    const project = response.data;

                    set({ isLoading: false });
                    return { id: project.id };
                } catch (error: unknown) {
                    set({ isLoading: false });

                    if (error && typeof error === 'object' && 'response' in error) {
                        const apiError = error as { response?: { statusCode?: number } };
                        if (apiError.response?.statusCode === 403 || apiError.response?.statusCode === 401 || apiError.response?.statusCode === 500) {
                            return null;
                        }
                    }

                    const errorMessage = error instanceof Error ? error.message : 'Failed to create project';
                    set({ error: errorMessage });
                    throw error;
                }
            },

            removeProject: (id) => {
                deleteProject(id);
                set((state) => ({
                    projects: state.projects.filter(p => p.id !== id),
                    currentProject: state.currentProject?.id === id ? null : state.currentProject
                }));
            },

            clearError: () => set({ error: null }),

            setPendingMessage: (message) => set({ pendingMessage: message }),

            clearPendingMessage: () => set({ pendingMessage: null }),

            setSubmittingMessage: (isSubmitting) => set({ isSubmittingMessage: isSubmitting })
        }),
        {
            name: 'project-store'
        }
    )
);
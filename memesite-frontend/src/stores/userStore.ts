import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface UserData {
  id: string;
  clerkId: string;
  username?: string;
}

interface UserState {
  user: UserData | null;
  isLoading: boolean;
  error: string | null;

  setUser: (user: UserData) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearUser: () => void;

  fetchUser: (getToken: () => Promise<string | null>) => Promise<void>;
}

export const useUserStore = create<UserState>()(
  devtools(
    (set, get) => ({
      user: null,
      isLoading: false,
      error: null,

      setUser: (user) => set({ user, error: null }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      clearUser: () => set({ user: null, error: null }),

      fetchUser: async (getToken) => {
        const { setLoading, setUser, setError } = get();

        setLoading(true);
        setError(null);

        try {
          const token = await getToken();

          if (!token) {
            throw new Error("Token not found");
          }

          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BACKEND_URL}/users/me`,
            {
              method: "GET",
              headers: {
                Accept: "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!response.ok) {
            throw new Error(`API error! Status: ${response.status}`);
          }

          const userData = await response.json();
          setUser(userData);
        } catch (error) {
          setError(error instanceof Error ? error.message : "Unknown error");
        } finally {
          setLoading(false);
        }
      },
    }),
    { name: "user-store" }
  )
);

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface PendingRequest {
    message: string;
    mode: 'build' | 'clone' | 'bob';
    timestamp: number;
}

interface AuthContextState {
    pendingRequest: PendingRequest | null;
    isRedirectingToAuth: boolean;
    isProcessingPendingRequest: boolean;

    setPendingRequest: (request: Omit<PendingRequest, 'timestamp'>) => void;
    clearPendingRequest: () => void;
    setRedirectingToAuth: (redirecting: boolean) => void;
    setProcessingPendingRequest: (processing: boolean) => void;
    hasPendingRequest: () => boolean;
    getPendingRequest: () => PendingRequest | null;
}

export const useAuthContextStore = create<AuthContextState>()(
    devtools(
        persist(
            (set, get) => ({
                pendingRequest: null,
                isRedirectingToAuth: false,
                isProcessingPendingRequest: false,

                setPendingRequest: (request) => {
                    set({
                        pendingRequest: {
                            ...request,
                            timestamp: Date.now()
                        }
                    });
                },

                clearPendingRequest: () => {
                    set({
                        pendingRequest: null,
                        isRedirectingToAuth: false
                        // Don't automatically clear isProcessingPendingRequest
                    });
                },

                setRedirectingToAuth: (redirecting) => {
                    set({ isRedirectingToAuth: redirecting });
                },

                setProcessingPendingRequest: (processing) => {
                    set({ isProcessingPendingRequest: processing });
                },
                
                hasPendingRequest: () => {
                    const { pendingRequest } = get();
                    if (!pendingRequest) return false;
                    
                    // Clear requests older than 1 hour
                    const oneHour = 60 * 60 * 1000;
                    if (Date.now() - pendingRequest.timestamp > oneHour) {
                        get().clearPendingRequest();
                        return false;
                    }
                    
                    return true;
                },
                
                getPendingRequest: () => {
                    const { pendingRequest } = get();
                    if (!pendingRequest) return null;
                    
                    // Clear requests older than 1 hour
                    const oneHour = 60 * 60 * 1000;
                    if (Date.now() - pendingRequest.timestamp > oneHour) {
                        get().clearPendingRequest();
                        return null;
                    }
                    
                    return pendingRequest;
                }
            }),
            {
                name: 'auth-context-store',
                partialize: (state) => ({
                    pendingRequest: state.pendingRequest
                })
            }
        ),
        {
            name: 'auth-context-store'
        }
    )
);

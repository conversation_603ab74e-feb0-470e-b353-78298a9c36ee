import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { LabImage } from '@/data/labImages';

interface LabStore {
    selectedImageForUse: LabImage | null;
    setSelectedImageForUse: (image: LabImage | null) => void;
    clearSelectedImage: () => void;
    labImages: LabImage[];
    addLabImage: (image: LabImage) => void;
    setLabImages: (images: LabImage[]) => void;
    savedImageKeys: Set<string>;
    isImageSaved: (key: string) => boolean;
    markImageAsSaved: (key: string) => void;
}

export const useLabStore = create<LabStore>()(
    devtools(
        (set, get) => ({
            selectedImageForUse: null,
            setSelectedImageForUse: (image) => set({ selectedImageForUse: image }),
            clearSelectedImage: () => set({ selectedImageForUse: null }),
            labImages: [],
            addLabImage: (image) => set((state) => ({
                labImages: [image, ...state.labImages],
                savedImageKeys: new Set([...state.savedImageKeys, image.key || image.src || ''])
            })),
            setLabImages: (images) => set({
                labImages: images,
                savedImageKeys: new Set(images.map(img => img.key || img.src || ''))
            }),
            savedImageKeys: new Set<string>(),
            isImageSaved: (key) => get().savedImageKeys.has(key),
            markImageAsSaved: (key) => set((state) => ({
                savedImageKeys: new Set([...state.savedImageKeys, key])
            })),
        }),
        {
            name: 'lab-store'
        }
    )
);
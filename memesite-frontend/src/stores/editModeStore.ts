import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface ElementInfo {
  tagName: string;
  id?: string;
  className?: string;
  selector: string;
  computedStyles?: {
    width: string;
    height: string;
    padding: string;
    margin: string;
    fontSize: string;
    color: string;
    backgroundColor: string;
  };
  attributes?: Record<string, string>;
  innerText?: string;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  imageData?: {
    src: string;
    alt?: string;
    naturalWidth?: number;
    naturalHeight?: number;
    isBackground?: boolean;
  };
}

interface EditModeStore {
  isEditMode: boolean;
  isFromLabFlow: boolean;
  hoveredElement: ElementInfo | null;
  selectedElement: ElementInfo | null;
  isTextEditing: boolean;
  setEditMode: (value: boolean, fromLabFlow?: boolean) => void;
  toggleEditMode: () => void;
  setHoveredElement: (element: ElementInfo | null) => void;
  setSelectedElement: (element: ElementInfo | null) => void;
  clearInspectorState: () => void;
  setTextEditing: (value: boolean) => void;
}

export const useEditModeStore = create<EditModeStore>()(
  devtools(
    (set) => ({
      isEditMode: false,
      isFromLabFlow: false,
      hoveredElement: null,
      selectedElement: null,
      isTextEditing: false,
      setEditMode: (value, fromLabFlow = false) => set({ isEditMode: value, isFromLabFlow: value ? fromLabFlow : false, isTextEditing: false }),
      toggleEditMode: () => set((state) => ({ isEditMode: !state.isEditMode, isFromLabFlow: false, isTextEditing: false })),
      setHoveredElement: (element) => set({ hoveredElement: element }),
      setSelectedElement: (element) => set({ selectedElement: element }),
      clearInspectorState: () => set({ hoveredElement: null, selectedElement: null }),
      setTextEditing: (value) => set({ isTextEditing: value }),
    }),
    {
      name: "edit-mode-store",
    }
  )
);
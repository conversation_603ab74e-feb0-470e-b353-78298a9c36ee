import type { Metadata } from 'next'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import { Inter_Tight } from 'next/font/google'
import './globals.css'
import ConditionalNavigation from '@/components/layout/ConditionalNavigation'
import ModalManager from '@/components/ui/modals/ModalManager'
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout'
import AuthHandler from '@/components/auth/AuthHandler'

const interTight = Inter_Tight({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Memesite",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider
      appearance={{
        elements: {
          rootBox: {
            height: "auto",
            width: "auto",
          },
          card: {
            backgroundColor: "#FFFFFF",
            boxShadow: "none",
          },
          footer: {
            display: "none",
          },
          footerAction: {
            display: "none",
          },
        },
      }}
      signInFallbackRedirectUrl="/home"
      signUpFallbackRedirectUrl="/home"
      afterSignOutUrl="/home"
    >
      <html lang="en">
        <body className={`${interTight.className} antialiased`}>
          <ConditionalNavigation />
          <AuthenticatedLayout>
            {children}
          </AuthenticatedLayout>
          <AuthHandler />
          <ModalManager />
        </body>
      </html>
    </ClerkProvider>
  );
}

@import "tailwindcss";

/* Remove autofill background color */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #000000;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 20px 20px transparent;
}

:root {
  /* Base units */
  --vw: clamp(0.551rem, 0.882vw, 1.103rem);

  /* colors */
  --background: #07070f;
  --foreground: #171717;
  --dark: #030208;
  --color-transparent-grey: #f8f9f8;
  --color-off-white: rgba(245, 245, 245, 0.8);
  --color-blue: #5a71e6;
  --color-purple: #6D33FC;

  /* text sizing */
  --text-2xs: clamp(0.634rem, 0.659vw, 0.887rem);
  --text-xs: clamp(0.697rem, 0.812vw, 0.912rem);
  --text-sm: clamp(0.761rem, 0.912vw, 1.014rem);
  --text-base: clamp(0.887rem, 1.014vw, 1.141rem);
  --text-lg: clamp(1.014rem, 1.167vw, 1.268rem);
  --text-xl: clamp(1.08rem, 1.35vw, 1.445rem);
  --text-2xl: clamp(1.268rem, 1.674vw, 1.776rem);
  --text-3xl: clamp(1.521rem, 1.927vw, 2.029rem);
  --text-4xl: clamp(1.776rem, 2.536vw, 2.283rem);
  --text-5xl: clamp(2.968rem, 4.743vw, 3.795rem);

  /* Base spacing units */
  --vw-0_25: calc(var(--vw) * 0.25);
  --vw-0_375: calc(var(--vw) * 0.375);
  --vw-0_5: calc(var(--vw) * 0.5);
  --vw-0_625: calc(var(--vw) * 0.625);
  --vw-0_75: calc(var(--vw) * 0.75);
  --vw-1: var(--vw);
  --vw-1_25: calc(var(--vw) * 1.25);
  --vw-1_5: calc(var(--vw) * 1.5);
  --vw-1_75: calc(var(--vw) * 1.75);
  --vw-2: calc(var(--vw) * 2);
  --vw-2_25: calc(var(--vw) * 2.25);
  --vw-2_5: calc(var(--vw) * 2.5);
  --vw-2_75: calc(var(--vw) * 2.75);
  --vw-3: calc(var(--vw) * 3);

  --vh-0_25: calc(var(--vh) * 0.25);
  --vh-0_5: calc(var(--vh) * 0.5);
  --vh-0_75: calc(var(--vh) * 0.75);
  --vh-1: var(--vh);
  --vh-1_25: calc(var(--vh) * 1.25);
  --vh-1_5: calc(var(--vh) * 1.5);
  --vh-1_75: calc(var(--vh) * 1.75);
  --vh-2: calc(var(--vh) * 2);
  --vh-2_25: calc(var(--vh) * 2.25);
  --vh-2_5: calc(var(--vh) * 2.5);
  --vh-3: calc(var(--vh) * 3);

  /* width */
  --width-10: clamp(7.5rem, 10vw, 10rem);
  --width-15: clamp(11.25rem, 15vw, 15rem);
  --width-20: clamp(15rem, 20vw, 20rem);
  --width-25: clamp(18.75rem, 25vw, 25rem);
  --width-30: clamp(22.5rem, 30vw, 30rem);
  --width-35: clamp(26.25rem, 35vw, 35rem);
  --width-40: clamp(30rem, 40vw, 40rem);
  --width-45: clamp(33.75rem, 45vw, 45rem);
  --width-50: clamp(37.5rem, 50vw, 50rem);
  --width-55: clamp(41.25rem, 55vw, 55rem);
  --width-60: clamp(45rem, 60vw, 60rem);
  --width-65: clamp(48.75rem, 65vw, 65rem);
  --width-70: clamp(52.5rem, 70vw, 70rem);
  --width-75: clamp(56.25rem, 75vw, 75rem);
  --width-80: clamp(60rem, 80vw, 80rem);
  --width-85: clamp(63.75rem, 85vw, 85rem);
  --width-90: clamp(67.5rem, 90vw, 90rem);
  --width-95: clamp(71.25rem, 95vw, 95rem);
  --width-100: clamp(75rem, 100vw, 100rem);

  --width-sidebar-padding: calc(var(--width-15) + var(--vw-1_5));

  --height-4: 1.1rem;
  --height-5: 1.37rem;
  --height-6: 1.68rem;
  --height-7: 1.94rem;
  --height-8: 2.21rem;
  --height-9: 2.47rem;
  --height-10: 2.73rem;
  --height-11: 3.05rem;
  --height-12: 3.31rem;
  --height-90: 24.7rem;
  --height-100: 27.6rem;
  --height-110: 30.5rem;
  --height-120: 33.1rem;
  --height-130: 35.7rem;
  --height-140: 38.6rem;
  --height-150: 41.5rem;

  --height-page-padding: calc(2.25rem+var(--vw-1_5)+var(--vw-1_5));

  --height-sidebar-height: calc(100vh - var(--height-page-padding) - var(--vw-1_5));

}

@media (max-width: 767px) {
  :root {
    --vw: 3vw;

    --text-2xs: 2.5vw;
    --text-xs: 2.75vw;
    --text-sm: 3vw;
    --text-base: 3.25vw;
    --text-lg: 4vw;
    --text-xl: 5vw;
    --text-2xl: 6vw;
    --text-3xl: 7vw;
    --text-4xl: 8vw;
    --text-5xl: 8vw;

    --width-10: 10vw;
    --width-15: 15vw;
    --width-20: 20vw;
    --width-25: 25vw;
    --width-30: 30vw;
    --width-35: 35vw;
    --width-40: 40vw;
    --width-45: 45vw;
    --width-50: 50vw;
    --width-55: 55vw;
    --width-60: 60vw;
    --width-65: 65vw;
    --width-70: 70vw;
    --width-75: 75vw;
    --width-80: 80vw;
    --width-85: 85vw;
    --width-90: 90vw;
    --width-95: 95vw;
    --width-100: 100vw;

    --height-4: 3.5vw;
    --height-5: 4.5vw;
    --height-6: 5.5vw;
    --height-7: 6.5vw;
    --height-8: 7.5vw;
    --height-9: 8.5vw;
    --height-10: 9vw;
    --height-11: 10vw;
    --height-12: 11vw;
    --height-90: 81vw;
    --height-100: 90vw;
    --height-110: 99vw;
    --height-120: 108vw;
    --height-130: 117vw;
    --height-140: 126vw;
    --height-150: 135vw;

    --height-4: 3.5vw;
    --height-5: 4.5vw;
    --height-6: 5.5vw;
    --height-7: 6.5vw;
    --height-8: 7.5vw;
    --height-9: 8.5vw;
    --height-10: 9vw;
    --height-11: 10vw;
    --height-12: 11vw;
    --height-90: 81vw;
    --height-100: 90vw;
    --height-110: 99vw;
    --height-120: 108vw;
    --height-130: 117vw;
    --height-140: 126vw;
    --height-150: 135vw;

  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-dark: var(--dark);
  --color-transparent-grey: var(--color-transparent-grey);
  --color-off-white: var(--color-off-white);
  --color-blue: var(--color-blue);
  --color-purple: var(--color-purple);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* text */
  --text-2xs: var(--text-2xs);
  --text-xs: var(--text-xs);
  --text-sm: var(--text-sm);
  --text-base: var(--text-base);
  --text-lg: var(--text-lg);
  --text-xl: var(--text-xl);
  --text-2xl: var(--text-2xl);
  --text-3xl: var(--text-3xl);
  --text-4xl: var(--text-4xl);
  --text-5xl: var(--text-5xl);

  /* height */
  --height-4: var(--height-4);
  --height-5: var(--height-5);
  --height-6: var(--height-6);
  --height-7: var(--height-7);
  --height-8: var(--height-8);
  --height-9: var(--height-9);
  --height-11: var(--height-11);
  --height-12: var(--height-12);

  --height-sidebar-height: var(--height-sidebar-height);

  --height-page-padding: var(--height-page-padding);

  /* width */
  --width-10: var(--width-10);
  --width-15: var(--width-15);
  --width-20: var(--width-20);
  --width-25: var(--width-25);
  --width-30: var(--width-30);
  --width-35: var(--width-35);
  --width-40: var(--width-40);
  --width-45: var(--width-45);
  --width-50: var(--width-50);
  --width-55: var(--width-55);
  --width-60: var(--width-60);
  --width-65: var(--width-65);
  --width-70: var(--width-70);
  --width-75: var(--width-75);
  --width-80: var(--width-80);
  --width-85: var(--width-85);
  --width-90: var(--width-90);
  --width-95: var(--width-95);
  --width-100: var(--width-100);

  --width-sidebar-padding: var(--width-sidebar-padding);

  /* gap */
  --spacing-1: var(--vw-0_25);
  --spacing-2: var(--vw-0_5);
  --spacing-3: var(--vw-0_75);
  --spacing-4: var(--vw-1);
  --spacing-5: var(--vw-1_25);
  --spacing-6: var(--vw-1_5);
  --spacing-7: var(--vw-1_75);
  --spacing-8: var(--vw-2);

  --spacing-x-1: var(--vw-0_25);
  --spacing-x-2: var(--vw-0_5);
  --spacing-x-3: var(--vw-0_75);
  --spacing-x-4: var(--vw-1);
  --spacing-x-5: var(--vw-1_25);
  --spacing-x-6: var(--vw-1_5);

  /* border radius */
  --radius-none: 0;
  --radius-extra-sm: var(--vw-0_75);
  --radius-sm: var(--vw-1);
  --radius: var(--vw-1_5);
  --radius-md: var(--vw-1_75);
  --radius-lg: var(--vw-2);
  --radius-xl: var(--vw-2_25);
  --radius-2xl: var(--vw-2_5);
  --radius-full: 9999px;

  /* padding */
  --padding-1: var(--vw-0_25);
  --padding-2: var(--vw-0_5);
  --padding-2.5: var(--vw-0_625);
  --padding-3: var(--vw-0_75);
  --padding-4: var(--vw-1);
  --padding-5: var(--vw-1_25);
  --padding-6: var(--vw-1_5);
  --padding-7: var(--vw-1_75);
  --padding-8: var(--vw-2);

  --padding-x-1: var(--vw-0_25);
  --padding-x-2: var(--vw-0_5);
  --padding-x-3: var(--vw-0_75);
  --padding-x-4: var(--vw-1);
  --padding-x-5: var(--vw-1_25);
  --padding-x-6: var(--vw-1_5);
  --padding-x-7: var(--vw-1_75);
  --padding-x-8: var(--vw-2);

  --padding-page-padding: calc(2.25rem+var(--vw-1_5)+var(--vw-1_5));

  /* margin */
  --margin-1: var(--vw-0_25);
  --margin-2: var(--vw-0_5);
  --margin-3: var(--vw-0_75);
  --margin-4: var(--vw-1);
  --margin-5: var(--vw-1_25);
  --margin-6: var(--vw-1_5);
  --margin-7: var(--vw-1_75);
  --margin-8: var(--vw-2);

  --margin-x-1: var(--vw-0_25);
  --margin-x-2: var(--vw-0_5);
  --margin-x-3: var(--vw-0_75);
  --margin-x-4: var(--vw-1);
  --margin-x-5: var(--vw-1_25);
  --margin-x-6: var(--vw-1_5);
  --margin-x-7: var(--vw-1_75);
  --margin-x-8: var(--vw-2);
}

@layer components {
  /* UI */

  .button {
    @apply relative cursor-pointer h-10 rounded-full px-4;
  }
}

@layer utilities {
  /* UI */

  .black-button {
    @apply text-white;
    background: linear-gradient(180deg, #0D0C10 0%, #0a090d 100%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 1);
  }

  .black-button::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(5deg,
        #16161A 0%,
        #363636 52%,
        #000000 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .black-button-gradient {
    background: linear-gradient(180deg, #0D0C10 0%, #0a090d 100%);
  }

  .black-button-purple-glow {
    @apply text-white;
    background: linear-gradient(180deg, #0D0C10 0%, #0a090d 100%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 1);
  }

  .black-button-purple-glow::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(190deg,
        #363636 0%,
        #16161A 10%,
        #8B5BFF 52%,
        #16161A 90%,
        #363636 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .black-card {
    @apply text-white;
    background: linear-gradient(180deg, #0D0C10 0%, #0a090d 100%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 0.15);
  }

  .black-card::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(60deg,
        #16161A 0%,
        #363636 52%,
        #000000 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .black-input {
    @apply text-white;
    background: linear-gradient(180deg, #0D0C10 0%, #0a090d 100%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 1);
  }

  .black-input::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(181deg,
        #8B5BFF 0%,
        #16161A 10%,
        #363636 52%,
        #000000 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .black-circle {
    position: relative;
    background: linear-gradient(0deg,
        #0F0E13 30%,
        rgba(78, 58, 150, 0.25) 60%,
        rgba(78, 58, 150, 0.5) 90%,
        rgba(78, 58, 150, 0.8) 100%);
    box-shadow: 0 2px 0 rgba(255, 255, 255, 0);
  }

  .black-circle::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded p-[1px] opacity-35;
    background: linear-gradient(50deg,
        #0F0E13 0%,
        #0F0E13 50%,
        rgba(212, 219, 255, 1) 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .purple-button {
    @apply text-white;
    background: linear-gradient(180deg, #9C85ED 0%, #6626FF 74%);
    /* box-shadow: 0 10px 18px -7px rgba(112, 128, 206, 1); */
  }

  .purple-button::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(0deg, #BDACFF 0%, rgba(60, 40, 135, 0.44) 18%, rgba(60, 40, 135, 0.66) 46%, #350B9A 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .black-box {
    @apply relative backdrop-blur-xs bg-dark/50;
    backdrop-filter: blur(6px);
  }

  .black-box::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded p-[1px] opacity-35;
    background: linear-gradient(140deg, #5a5a5a 0%, #363636 14%, #525252 100%);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .button-circle {
    @apply h-7 aspect-square rounded-full flex items-center justify-center;
  }

  .gradient-before-rounded-sm::before {
    @apply rounded-sm;
  }

  .gradient-before-rounded::before {
    @apply rounded;
  }

  .gradient-before-rounded-lg::before {
    @apply rounded-lg;
  }

  .gradient-before-rounded-xl::before {
    @apply rounded-xl;
  }

  .gradient-before-rounded-full::before {
    @apply rounded-full;
  }

  /* TEXT */

  .gradient-text {
    width: fit-content;
    background-image: radial-gradient(47.08% 208.33% at 77.5% 128.33%, rgba(135, 206, 250, 0.8) 20%, rgba(90, 113, 230, .8) 25%, #000000 70.24%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .gradient-text-white {
    background: linear-gradient(to bottom, #fff, rgba(255, 255, 255, 0.5));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* MASKS */

  .mask-fade-y {
    mask-image: linear-gradient(to bottom,
        transparent 0%,
        black var(--vw-1_5),
        black calc(100% - var(--vw-1_5)),
        transparent 100%);
  }

  .mask-fade-bottom {
    mask-image: linear-gradient(to bottom,
        black 0%,
        black calc(100% - var(--vw-1_5)),
        transparent 100%);
  }

  .mask-fade-bottom-border {
    mask-image: linear-gradient(to bottom,
        transparent 0%,
        black 1rem,
        black calc(100% - 1rem - 1px),
        transparent calc(100% - 1px),
        black calc(100% - 1px),
        black 100%);
  }

  .mask-fade-edges-2px {
    mask-image: linear-gradient(to right,
        transparent 0px,
        black 2px,
        black calc(100% - 2px),
        transparent 100%),
      linear-gradient(to bottom,
        transparent 0px,
        black 2px,
        black calc(100% - 2px),
        transparent 100%);
    mask-composite: intersect;
  }

  /* ANIMATION */

  @keyframes fadeInOut {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0;
    }
  }

  .logo-glow-animation {
    animation: fadeInOut 2s cubic-bezier(0.37, 0, 0.63, 1) infinite;
  }

  @keyframes moveUpDown {

    0%,
    100% {
      transform: translateY(1px);
    }

    50% {
      transform: translateY(-2px);
    }
  }

  .logo-dot-animation {
    animation: moveUpDown 2s cubic-bezier(0.37, 0, 0.63, 1) infinite;
  }

  .animation-container {
    animation: fadeInOpacity 0.8s ease-in-out forwards,
      fadeInTranslate 0.6s forwards;
  }

  .animation-container-fade {
    animation: fadeInOpacity 0.8s ease-in-out forwards;
  }

  @keyframes circleAnimation {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(0.65);
    }
  }

  @keyframes colorWave {

    0%,
    100% {
      color: white;
    }

    50% {
      color: #6D33FC;
    }
  }

  @keyframes colorWaveFaded {

    0%,
    100% {
      color: white;
    }

    50% {
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .continuous-color-animation {
    animation: colorWave 2s ease-in-out infinite;
  }

  .continuous-color-faded-animation {
    animation: colorWaveFaded 2s ease-in-out infinite;
  }

  @keyframes fadeInOpacity {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes fadeInTranslate {
    from {
      transform: translateY(0.75vh);
    }

    to {
      transform: translateY(0vh);
    }
  }

  @keyframes scan {

    0%,
    100% {
      transform: translateY(-5rem);
    }

    50% {
      transform: translateY(5rem);
    }
  }
}

@media (prefers-color-scheme: dark) {
  :root {}
}

* {
  scrollbar-width: thin;
  scrollbar-color: rgba(30, 30, 30, 1) rgba(255, 255, 255, 0);
}

html {
  overscroll-behavior: none;
  overscroll-behavior-y: none;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Inter Tight", sans-serif;
  position: relative;
  min-height: 100vh;
  overscroll-behavior: none;
  overscroll-behavior-y: none;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/staticfaded.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

/* CLERK */

.cl-internal-zar2af {
  @apply shadow-none bg-white;
}

/* clerk button */

.cl-internal-1o76iuo {
  @apply !border-none !shadow-none rounded-full h-10 transition-none;
  background: linear-gradient(180deg, #bec8f6 0%, #7887d1 74%);
}

.cl-internal-1o76iuo:hover {
  @apply shadow-none transform-none opacity-100;
  background: linear-gradient(180deg, #bec8f6 0%, #7887d1 74%) !important;
}

.cl-internal-1o76iuo:hover::before,
.cl-internal-1o76iuo:hover::after,
.cl-internal-1o76iuo:hover * {
  @apply transition-none transform-none opacity-100;
}

.cl-internal-1o76iuo,
.cl-internal-1o76iuo * {
  @apply transition-none;
}

.cl-internal-1o76iuo:active,
.cl-internal-1o76iuo:focus {
  @apply transform-none;
  background: linear-gradient(180deg, #bec8f6 0%, #7887d1 74%) !important;
}

.cl-internal-1o76iuo::before {
  content: "";
  @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
  background: linear-gradient(0deg,
      #bfc9fb 0%,
      #838fce 27%,
      #5a6390 62%,
      #c9d2fa 100%);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask-composite: exclude;
}

.cl-internal-1uq3j8z {
  @apply hidden;
}
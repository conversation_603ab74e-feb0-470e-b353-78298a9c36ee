import React from "react";
import ProjectsHeader from "@/components/widgets/projects/ProjectsHeader";
import ProjectsContent from "@/components/widgets/projects/ProjectsContent";
import { ProjectsProvider } from "@/components/widgets/projects/ProjectContext";
import AnimationContainer from "@/components/layout/AnimationContainer";

const ProjectsPage = () => {
  return (
    <ProjectsProvider>
      <AnimationContainer>
        <div className="min-h-screen pt-page-padding px-6 flex flex-col overflow-x-hidden">
          <div className="w-full h-sidebar-height flex flex-col min-w-0 rounded overflow-hidden">
            <div className="relative h-full flex flex-col black-box gradient-before-rounded rounded p-5 overflow-hidden">
              <ProjectsHeader />
              <ProjectsContent />
            </div>
          </div>
        </div>
      </AnimationContainer>
    </ProjectsProvider>
  );
};

export default ProjectsPage;
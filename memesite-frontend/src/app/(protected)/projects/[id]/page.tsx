"use client";


import CloningView from "@/components/widgets/projects/views/CloningView";
import <PERSON><PERSON>ie<PERSON> from "@/components/widgets/projects/views/BobView";
import BuildView from "@/components/widgets/projects/views/BuildView";
import { useProjectWorkspace } from "@/hooks/useProjectWorkspace";
import { ANIMATION_CONSTANTS } from "@/constants/animation";

import ProjectNotFound from "@/components/widgets/projects/states/ProjectNotFound";


const ProjectContent = ({ params }: { params: Promise<{ id: string }> }) => {
  const {
    projectData,
    projectIsLoading,
    projectError,
    mutateProject,
    shouldShowCloneView,
    shouldShowBobView,
    clonedUrl,
    activeView,
    setActiveView,
    allTasksCompleted,
    previewStage,
    chatContainerRef,
    cloningViewRef,
    messages,
    isLoading,
    isResponseReady,
    isSubmittingMessage,
    isInputEnabled,
    isGenerating,
    hasReceivedApiResponse,

    handleMessageSubmit,
    handleAnimationComplete,
    handleAllTasksCompleted,
    incrementBobResponseCount,
  } = useProjectWorkspace({ params });

  const versionStatus = projectData?.versions?.[0]?.version_status || 'drafted';
  const isExecuting = versionStatus === 'processing';
  const projectStatus = versionStatus === 'processing' ? 'pending' :
                       versionStatus === 'executed' ? 'success' :
                       versionStatus === 'failed' ? 'failed' : null;
  const previewUrl = projectData?.preview_url || null;

  // Show loading only for the first few milliseconds to prevent flash
  if (projectIsLoading && !projectData) {
    return <div className="w-full h-screen" />; // Minimal loading to prevent flash
  }

  // Show error only if we have a definitive error
  if (projectError) {
    return <ProjectNotFound projectError={projectError} />;
  }

  // Show not found only if we have no data and no error (after loading attempt)
  if (!projectData && !projectIsLoading && !projectError) {
    return <ProjectNotFound projectError="Project not found" />;
  }

  // Always show preview if there's any URL available - no loading animations
  const shouldShowPreview = !!(clonedUrl || previewUrl || projectData?.preview_url) && !isExecuting;

  return (
    <>
      {shouldShowCloneView ? (
        <CloningView
          ref={cloningViewRef}
          messages={messages}
          isLoading={isLoading}
          isResponseReady={isResponseReady}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={handleAnimationComplete}
          onAllTasksCompleted={handleAllTasksCompleted}
        />
      ) : shouldShowBobView ? (
        <BobView
          ref={cloningViewRef}
          messages={messages}
          isLoading={isLoading}
          isResponseReady={isResponseReady}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={(messageId) => {
            handleAnimationComplete(messageId);
            const message = messages.find((m) => m.id === messageId);
            if (message?.role === "assistant") {
              setTimeout(() => {
                incrementBobResponseCount();
              }, ANIMATION_CONSTANTS.COMPLETION_BUFFER);
            }
          }}
          onAllTasksCompleted={handleAllTasksCompleted}
        />
      ) : (
        <BuildView
          ref={chatContainerRef}
          activeView={activeView}
          setActiveView={setActiveView}
          messages={messages}
          isLoading={isLoading || isSubmittingMessage}
          isExecuting={isExecuting}
          isResponseReady={isResponseReady}
          isInputEnabled={isInputEnabled}
          isGenerating={isGenerating}
          hasReceivedApiResponse={hasReceivedApiResponse}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={handleAnimationComplete}
          onAllTasksCompleted={handleAllTasksCompleted}
          shouldShowPreview={shouldShowPreview}
          previewStage={previewStage}
          clonedUrl={clonedUrl}
          previewUrl={previewUrl}
          projectStatus={projectStatus}
          project={projectData}
          mutateProject={mutateProject}
        />
      )}
    </>
  );
};

const Page = ({ params }: { params: Promise<{ id: string }> }) => {
  return <ProjectContent params={params} />;
};

export default Page;

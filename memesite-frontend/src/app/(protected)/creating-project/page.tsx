"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import LogoAnimation from "@/components/ui/animation/LogoAnimation";
import { useAuthContextStore } from "@/stores/authContextStore";

export default function CreatingProjectPage() {
    const router = useRouter();
    const { hasPendingRequest } = useAuthContextStore();

    useEffect(() => {
        // If there's no pending request, redirect back to home
        if (!hasPendingRequest()) {
            router.replace('/');
        }
    }, [hasPendingRequest, router]);

    return (
        <div className="w-full h-svh flex items-center justify-center">
            <LogoAnimation className="h-10 w-auto" />
        </div>
    );
}

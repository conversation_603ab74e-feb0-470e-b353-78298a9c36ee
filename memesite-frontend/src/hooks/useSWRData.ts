import useS<PERSON> from 'swr';
import { useAuth } from '@clerk/nextjs';
import { getProjectIDAPI, getMessagesAPI } from '@/utils/api';
import { GetProjectIDResponse, GetMessagesResponse } from '@/types/project';

// SWR fetcher function for project data
const createProjectFetcher = (getToken: (options?: { template?: string }) => Promise<string | null>) => {
  return async (url: string): Promise<GetProjectIDResponse> => {
    const token = await getToken({
      template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
    });

    if (!token) {
      throw new Error('Authentication token not found');
    }

    // Extract projectId from the endpoint
    const projectId = url.split('/')[2];
    return getProjectIDAPI(projectId, token);
  };
};

// SWR fetcher function for messages data
const createMessagesFetcher = (getToken: (options?: { template?: string }) => Promise<string | null>) => {
  return async (url: string): Promise<GetMessagesResponse> => {
    const token = await getToken({
      template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
    });

    if (!token) {
      throw new Error('Authentication token not found');
    }

    // Parse the URL to extract parameters
    const [endpoint, params] = url.split('?');
    const urlParams = new URLSearchParams(params);

    // Extract projectId from the endpoint
    const projectId = endpoint.split('/')[2];
    const limit = parseInt(urlParams.get('limit') || '20');
    const cursor = urlParams.get('cursor') || undefined;

    return getMessagesAPI(projectId, token, limit, cursor);
  };
};

// Hook for fetching project data
export const useProject = (projectId: string | null) => {
  const { getToken, isSignedIn } = useAuth();

  const { data, error, isLoading, mutate } = useSWR<GetProjectIDResponse>(
    isSignedIn && projectId ? `/projects/${projectId}` : null,
    createProjectFetcher(getToken),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      refreshInterval: 2000, // Poll every 2 seconds for real-time updates
      errorRetryCount: 0,
      errorRetryInterval: 500,
      shouldRetryOnError: () => false,
      dedupingInterval: 0, // No deduping for instant loading
      keepPreviousData: false,
    }
  );

  return {
    project: data?.data || null,
    isLoading,
    error: error ? (error.status === 404 ? 'Project not found' :
                   error.status === 403 ? 'Access denied' :
                   error.status === 401 ? 'Unauthorized' :
                   'Failed to load project') : null,
    mutate
  };
};

// Hook for fetching messages
export const useMessages = (projectId: string | null, projectExists: boolean = true, limit: number = 20, cursor?: string) => {
  const { getToken, isSignedIn } = useAuth();

  const queryParams = new URLSearchParams();
  queryParams.set('limit', limit.toString());
  if (cursor) {
    queryParams.set('cursor', cursor);
  }

  const { data, error, isLoading, mutate } = useSWR<GetMessagesResponse>(
    isSignedIn && projectId && projectExists ? `/projects/${projectId}/messages?${queryParams.toString()}` : null,
    createMessagesFetcher(getToken),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      refreshInterval: 100, // Poll every 0.1 seconds for INSTANT updates
      errorRetryCount: 0,
      errorRetryInterval: 500,
      shouldRetryOnError: () => false,
      dedupingInterval: 0, // No deduping for instant loading
      keepPreviousData: false,
      fallbackData: {
        data: [],
        meta: {
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false
        }
      }, // Provide immediate fallback
    }
  );

  return {
    messages: data?.data || [],
    meta: data?.meta || null,
    isLoading,
    error: error ? (error.status === 404 ? 'Messages not found' :
                   error.status === 403 ? 'Access denied' :
                   error.status === 401 ? 'Unauthorized' :
                   'Failed to load messages') : null,
    mutate
  };
};

export const useProjectConditional = (projectId: string | null, shouldFetch: boolean = true) => {
  const { getToken, isSignedIn } = useAuth();
  
  const { data, error, isLoading, mutate } = useSWR<GetProjectIDResponse>(
    isSignedIn && projectId && shouldFetch ? `/projects/${projectId}` : null,
    createProjectFetcher(getToken),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 1,
      errorRetryInterval: 1000,
      shouldRetryOnError: (error) => {
        return error?.status !== 404 && error?.status !== 403;
      }
    }
  );

  return {
    project: data?.data || null,
    isLoading,
    error,
    mutate,
    projectExists: !!data?.data && !error
  };
};

import { useState, useEffect, useRef } from 'react';

interface Position {
  x: number;
  y: number;
  width?: number;
}

interface UsePopupPositionReturn {
  adjustedPosition: Position;
  popupRef: React.RefObject<HTMLDivElement | null>;
}

export const usePopupPosition = (
  isVisible: boolean,
  position: Position
): UsePopupPositionReturn => {
  const [adjustedPosition, setAdjustedPosition] = useState(position);
  const popupRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isVisible || !popupRef.current) {
      setAdjustedPosition(position);
      return;
    }

    const popupWidth = popupRef.current.offsetWidth;
    const elementWidth = position.width || 0;

    setAdjustedPosition({
      x: position.x - (popupWidth / 2) + elementWidth / 2,
      y: position.y
    });
  }, [isVisible, position]);

  return { adjustedPosition, popupRef };
};
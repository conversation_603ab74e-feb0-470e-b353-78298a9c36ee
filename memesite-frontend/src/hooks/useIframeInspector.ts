import { useEffect, RefObject } from 'react';
import { createInspectorScript, cleanupInspectorScript } from '@/utils/iframeInspector';
import { useEditModeStore } from '@/stores/editModeStore';
import { useProjectChangesStore } from '@/stores/projectChangesStore';
import { usePopupStore } from '@/stores/popupStore';

interface UseIframeInspectorProps {
    iframeRef: RefObject<HTMLIFrameElement | null>;
    isEditMode?: boolean;
    iframeUrl: string;
    onImageElementSelected?: () => void;
    onNonImageElementSelected?: () => void;
}

export const useIframeInspector = ({
    iframeRef,
    isEditMode,
    iframeUrl,
    onImageElementSelected,
    onNonImageElementSelected
}: UseIframeInspectorProps) => {
    const { setHoveredElement, setSelectedElement, clearInspectorState, setTextEditing } = useEditModeStore();
    const { addChange } = useProjectChangesStore();
    const { setPopupData, hidePopup } = usePopupStore();

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            if (event.data.type === 'webild-element-hover') {
                setHoveredElement(event.data.data);
            } else if (event.data.type === 'webild-element-selected') {
                setSelectedElement(event.data.data);

                if (event.data.data && iframeRef.current && isEditMode) {
                    const elementBox = event.data.data.boundingBox;
                    const iframeRect = iframeRef.current.getBoundingClientRect();

                    const isImage = event.data.data.tagName === 'img' || !!event.data.data.imageData?.src;

                    if (isImage && onImageElementSelected) {
                        hidePopup();
                        onImageElementSelected();
                    } else {
                        if (onNonImageElementSelected) {
                            onNonImageElementSelected();
                        }

                        const x = iframeRect.left + elementBox.x;
                        const y = iframeRect.top + elementBox.y + elementBox.height + 8;

                        setPopupData({
                            isVisible: true,
                            position: { x, y, width: elementBox.width },
                            value: "",
                            selector: event.data.data.selector
                        });
                    }
                } else {
                    hidePopup();
                }
            } else if (event.data.type === 'webild-element-changed') {
                addChange({
                    type: event.data.data.type,
                    selector: event.data.data.selector,
                    data: {
                        oldValue: event.data.data.oldValue,
                        newValue: event.data.data.newValue
                    }
                });
            } else if (event.data.type === 'webild-iframe-scroll') {
                hidePopup();
            } else if (event.data.type === 'webild-text-editing-started') {
                setTextEditing(true);
            } else if (event.data.type === 'webild-text-editing-ended') {
                setTextEditing(false);
            } else if (event.data.type === 'webild-text-changed') {
                setTextEditing(true);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [setHoveredElement, setSelectedElement, addChange, setPopupData, hidePopup, isEditMode, iframeRef, onImageElementSelected, onNonImageElementSelected, setTextEditing]);

    useEffect(() => {
        if (!iframeRef.current) return;

        const iframe = iframeRef.current;

        if (!isEditMode) {
            clearInspectorState();

            try {
                const contentWindow = iframe.contentWindow;
                const contentDocument = iframe.contentDocument;

                if (contentWindow && contentDocument) {
                    const cleanupScript = contentDocument.createElement('script');
                    cleanupScript.textContent = cleanupInspectorScript();
                    contentDocument.body.appendChild(cleanupScript);
                }
            } catch {
            }
            return;
        }

        const injectInspector = () => {
            try {
                const contentWindow = iframe.contentWindow;
                const contentDocument = iframe.contentDocument;

                if (!contentWindow || !contentDocument) {
                    return;
                }

                const script = contentDocument.createElement('script');
                script.textContent = createInspectorScript();
                contentDocument.body.appendChild(script);
            } catch {
            }
        };

        const handleLoad = () => {
            setTimeout(injectInspector, 100);
        };

        iframe.addEventListener('load', handleLoad);

        if (iframe.contentDocument?.readyState === 'complete') {
            injectInspector();
        }

        return () => {
            iframe.removeEventListener('load', handleLoad);

            if (!isEditMode) {
                try {
                    const contentWindow = iframe.contentWindow;
                    const contentDocument = iframe.contentDocument;

                    if (contentWindow && contentDocument) {
                        const cleanupScript = contentDocument.createElement('script');
                        cleanupScript.textContent = cleanupInspectorScript();
                        contentDocument.body.appendChild(cleanupScript);
                    }
                } catch {
                }
            }
        };
    }, [isEditMode, iframeUrl, clearInspectorState, iframeRef]);
};
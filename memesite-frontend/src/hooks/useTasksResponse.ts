import { useEffect, useState } from "react";
import type { TasksContent } from "@/types/chat";

interface UseTasksResponseProps {
    content: TasksContent;
    isAnimating: boolean;
    onAnimationComplete: () => void;
    taskCompletionTimes?: number[];
    onAllTasksCompleted?: () => void;
}

export const useTasksResponse = ({
    content,
    isAnimating,
    onAnimationComplete,
    taskCompletionTimes,
    onAllTasksCompleted
}: UseTasksResponseProps) => {
    const [completedTasks, setCompletedTasks] = useState<number[]>([]);
    const [isPaused, setIsPaused] = useState(false);

    useEffect(() => {
        if (isAnimating && !taskCompletionTimes) {
            const timer = setTimeout(onAnimationComplete, 100);
            return () => clearTimeout(timer);
        }
    }, [isAnimating, onAnimationComplete, taskCompletionTimes]);

    useEffect(() => {
        if (!isAnimating) {
            setIsPaused(true);
            return;
        }
        
        setIsPaused(false);
        
        if (taskCompletionTimes && taskCompletionTimes.length > 0) {
            const timers: NodeJS.Timeout[] = [];
            
            content.tasks.forEach((_, index) => {
                if (!completedTasks.includes(index)) {
                    const timer = setTimeout(() => {
                        setCompletedTasks(prev => [...prev, index]);
                    }, taskCompletionTimes[index]);
                    timers.push(timer);
                }
            });
            
            const lastTaskTimer = setTimeout(() => {
                onAllTasksCompleted?.();
                onAnimationComplete();
            }, Math.max(...taskCompletionTimes));
            timers.push(lastTaskTimer);

            return () => {
                timers.forEach(timer => clearTimeout(timer));
            };
        }
    }, [isAnimating, content.tasks, completedTasks, taskCompletionTimes, onAllTasksCompleted, onAnimationComplete]);

    return {
        completedTasks,
        isPaused
    };
};
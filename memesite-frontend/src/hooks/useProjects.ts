import { useState, useEffect } from "react";
import { getProjectsAPI } from "@/utils/api";
import { useAuth } from "@clerk/nextjs";
import { BackendProject } from "@/types/project";

export function useProjects(limit: number = 10, cursor?: string) {
  const { getToken } = useAuth();
  const [projects, setProjects] = useState<BackendProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    async function fetchProjects() {
      try {
        setIsLoading(true);
        setError(null);

        const token = await getToken({
          template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
        });

        const res = await getProjectsAPI(limit, token!, cursor);

        if (isMounted) {
          setProjects(res.data);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchProjects();

    return () => {
      isMounted = false;
    };
  }, [limit, cursor, getToken]);

  return { projects, isLoading, error };
}

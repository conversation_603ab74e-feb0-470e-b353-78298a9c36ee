import { useState } from 'react';
import { LabImage } from '@/data/labImages';
import { useEditModeStore } from '@/stores/editModeStore';
import { useLabStore } from '@/stores/labStore';

interface UseImageEditConfirmationsProps {
    uploadedImages: LabImage[];
    selectedImages: Set<string>;
    handleImageToggle: (imageName: string) => void;
    handleRemoveImage: () => void;
    setCurrentImage: (image: LabImage | null) => void;
    currentImage: LabImage | null;
    selectedImage?: LabImage | null;
    onIframeImageReplace?: (newImage: LabImage, targetSelector?: string) => void;
}

export const useImageEditConfirmations = ({
    uploadedImages,
    selectedImages,
    handleImageToggle,
    handleRemoveImage,
    setCurrentImage,
    currentImage,
    selectedImage,
    onIframeImageReplace,
    clearSelection
}: UseImageEditConfirmationsProps & { clearSelection?: () => void }) => {
    const [showReplaceConfirmation, setShowReplaceConfirmation] = useState(false);
    const [pendingImage, setPendingImage] = useState<LabImage | null>(null);
    const [showRemoveConfirmation, setShowRemoveConfirmation] = useState(false);

    const { setEditMode } = useEditModeStore();
    const { clearSelectedImage } = useLabStore();

    const handleImageToggleWithConfirmation = (imageName: string) => {
        const imageToUse = uploadedImages.find(img => (img.key || img.name) === imageName);

        if (selectedImages.has(imageName)) {
            return;
        }

        setPendingImage(imageToUse || null);
        setShowReplaceConfirmation(true);
    };

    const handleConfirmReplace = () => {
        if (pendingImage) {
            setCurrentImage(pendingImage);

            if (onIframeImageReplace) {
                onIframeImageReplace(pendingImage);
                setEditMode(false);
                if (clearSelection) {
                    clearSelection();
                }
            } else {
                const imageKey = pendingImage.key || pendingImage.name || '';
                if (imageKey) {
                    handleImageToggle(imageKey);
                    setEditMode(false);
                }
            }
        }
        setShowReplaceConfirmation(false);
        setPendingImage(null);
    };

    const handleCancelReplace = () => {
        setShowReplaceConfirmation(false);
        setPendingImage(null);
    };

    const handleRemoveClick = () => {
        setShowRemoveConfirmation(true);
    };

    const handleConfirmRemove = () => {
        if (onIframeImageReplace) {
            onIframeImageReplace({ key: '', url: '', name: '', src: '' });
        }
        setCurrentImage(null);
        handleRemoveImage();
        setShowRemoveConfirmation(false);
        setEditMode(false);
    };

    const handleCancelRemove = () => {
        setShowRemoveConfirmation(false);
    };

    const handleCancel = () => {
        clearSelectedImage();
        setEditMode(false);
        if (currentImage && currentImage.name === 'placeholder-image') {
            setCurrentImage(null);
        }
    };

    const handleReplace = () => {
        if (onIframeImageReplace && selectedImage) {
            setCurrentImage(selectedImage);
            onIframeImageReplace(selectedImage);
            setEditMode(false);
        } else if (currentImage) {
            setCurrentImage(currentImage);
            setEditMode(false);
        }
    };

    return {
        showReplaceConfirmation,
        showRemoveConfirmation,
        handleImageToggleWithConfirmation,
        handleConfirmReplace,
        handleCancelReplace,
        handleRemoveClick,
        handleConfirmRemove,
        handleCancelRemove,
        handleCancel,
        handleReplace
    };
};
import { useRef, useCallback, use, useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { useUIStore } from "@/stores/uiStore";
import { useChatStore } from "@/stores/chatStore";
import { useAuthContextStore } from "@/stores/authContextStore";
import { useModalStore } from "@/stores/modalStore";
import { ChatContainerRef } from "@/components/widgets/projects/ChatContainer";
import { scrollToBottom as scrollUtil } from "@/utils/scroll";
import { useProject, useMessages } from "@/hooks/useSWRData";
import { createMessageAPI } from "@/utils/api";

interface UseProjectWorkspaceParams {
    params: Promise<{ id: string }>;
}

export const useProjectWorkspace = ({ params }: UseProjectWorkspaceParams) => {
    const resolvedParams = use(params);
    const { getToken } = useAuth();
    const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);
    const [hasProcessedPendingRequest, setHasProcessedPendingRequest] = useState(false);
    // Use global state for isGenerating
    const isGenerating = useUIStore(state => state.isGenerating);
    const hasReceivedApiResponse = useUIStore(state => state.hasReceivedApiResponse);
    const setIsGenerating = useUIStore(state => state.setIsGenerating);
    const setHasReceivedApiResponse = useUIStore(state => state.setHasReceivedApiResponse);
    const [lastMessageCount, setLastMessageCount] = useState(0);
    const [isResponseReady, setIsResponseReady] = useState(false);
    const { project: projectData, isLoading: projectIsLoading, error: projectError, mutate: mutateProject } = useProject(resolvedParams.id);
    const projectExists = !!projectData && !projectError;
    const { messages: messagesData, mutate: mutateMessages } = useMessages(resolvedParams.id, projectExists);

    const { getPendingRequest, clearPendingRequest, setProcessingPendingRequest } = useAuthContextStore();
    const { openModal } = useModalStore();

    const messages = useChatStore(state => state.messages);
    const setMessages = useChatStore(state => state.setMessages);
    const updateMessage = useChatStore(state => state.updateMessage);
    // Use local state for isLoading and isResponseReady to handle real API responses
    const isLoading = false; // We'll manage loading state locally if needed

    const activeView = useUIStore(state => state.activeView);
    const setActiveView = useUIStore(state => state.setActiveView);
    const allTasksCompleted = useUIStore(state => state.allTasksCompleted);
    const previewStage = useUIStore(state => state.previewStage);
    const handleAllTasksCompleted = useUIStore(state => state.handleAllTasksCompleted);
    const hasTransitioned = useUIStore(state => state.hasTransitioned);
    const setHasTransitioned = useUIStore(state => state.setHasTransitioned);
    const bobResponseCount = useUIStore(state => state.bobResponseCount);
    const incrementBobResponseCount = useUIStore(state => state.incrementBobResponseCount);
    const resetBobResponseCount = useUIStore(state => state.resetBobResponseCount);

    const chatContainerRef = useRef<ChatContainerRef>(null);
    const cloningViewRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (messagesData && messagesData.length > 0) {
            const filteredMessages = messagesData.filter(msg => !msg.is_system);
            const chatMessages = filteredMessages.map((msg, index) => ({
                id: msg.id,
                role: msg.type === 'user' ? 'user' as const : 'assistant' as const,
                content: msg.message,
                type: 'text' as const,
                timestamp: new Date(msg.createdAt),
                // Set animation properties - only animate if this is a truly new message (not on initial load)
                animationComplete: !(msg.type === 'assistant' && index === filteredMessages.length - 1 && filteredMessages.length > lastMessageCount && lastMessageCount > 0),
                animationStartTime: msg.type === 'assistant' && index === filteredMessages.length - 1 && filteredMessages.length > lastMessageCount && lastMessageCount > 0 ? Date.now() : undefined
            }));

            const reversedMessages = [...chatMessages.slice(0, chatMessages.length - 1)].reverse();

            // Optimized comparison: check length first, then IDs
            const hasChanged = messages.length !== reversedMessages.length ||
                               messages.some((msg, index) => msg.id !== reversedMessages[index]?.id);

            if (hasChanged) {
                setMessages(reversedMessages);

                // Check if a new assistant message was added to trigger animation
                if (filteredMessages.length > lastMessageCount) {
                    const lastMessage = filteredMessages[filteredMessages.length - 1];
                    if (lastMessage.type === 'assistant') {
                        setIsResponseReady(true);
                        // Reset after a short delay to allow animation to start
                        setTimeout(() => setIsResponseReady(false), 100);
                    }
                }
                setLastMessageCount(filteredMessages.length);
            }
        } else if (messagesData && messagesData.length === 0 && messages.length > 0) {
            // Clear messages if no messages are returned from API
            setMessages([]);
        }
    }, [messagesData, setMessages, messages, lastMessageCount]);

    // Clear processing state when project loads successfully
    useEffect(() => {
        if (projectData && !projectIsLoading && !projectError) {
            setProcessingPendingRequest(false);
        }
    }, [projectData, projectIsLoading, projectError, setProcessingPendingRequest, openModal]);

    // Handle pending request from project creation
    useEffect(() => {
        const processPendingRequest = async () => {
            const pendingRequest = getPendingRequest();

            if (pendingRequest && projectData && !hasProcessedPendingRequest && messagesData.length === 0) {
                setHasProcessedPendingRequest(true);

                try {
                    const token = await getToken({
                        template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
                    });

                    if (token && projectData.activeVersionId) {
                        const response = await createMessageAPI(projectData.id, projectData.activeVersionId, pendingRequest.message, token);
                        // Handle isGenerating field from response
                        setIsGenerating(response.data.isGenerating);
                        setHasReceivedApiResponse(true);
                        mutateMessages(); // Refresh messages after creating the initial message
                    }
                } catch (error: unknown) {
                    console.error('Failed to create initial message:', error);

                    // Handle specific error cases for initial message
                    const axiosError = error as { response?: { status?: number; data?: { message?: string } } };
                    if (axiosError?.response?.status === 401) {
                        console.error('Unauthorized: User session expired during initial message creation');
                    } else if (axiosError?.response?.status === 403) {
                        console.error('Forbidden: User limits exceeded during initial message creation');
                        // Only show pricing modal for plan upgrade issues, not quota issues
                        const errorMessage = axiosError?.response?.data?.message || '';
                        if (errorMessage.includes('Upgrade your plan')) {
                            openModal('pricing', { projectId: projectData.id });
                        }
                    }
                } finally {
                    clearPendingRequest();
                }
            }
        };

        processPendingRequest();
    }, [projectData, messagesData, hasProcessedPendingRequest, getPendingRequest, clearPendingRequest, getToken, mutateMessages]);

    const isCloneMode = false;
    const isBobMode = false;
    const iframeMessage = messages.find(
        msg => msg.type === 'iframe' && msg.role === 'assistant'
    );
    const isCloningComplete = iframeMessage?.iframeCompleted && !iframeMessage?.manuallyStopped || false;
    const clonedUrl = isCloneMode && iframeMessage
        ? (iframeMessage.content as { url?: string })?.url
        : undefined;
    const shouldShowCloneView = isCloneMode && !hasTransitioned && !isCloningComplete;
    const shouldShowBobView = isBobMode && !hasTransitioned && bobResponseCount < 1;

    useEffect(() => {
        if (isCloneMode && isCloningComplete && !hasTransitioned) {
            const timer = setTimeout(() => {
                setHasTransitioned(true);
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [isCloneMode, isCloningComplete, hasTransitioned, setHasTransitioned]);

    useEffect(() => {
        if (isBobMode && bobResponseCount >= 1 && !hasTransitioned) {
            const timer = setTimeout(() => {
                setHasTransitioned(true);
            }, 500);
            return () => clearTimeout(timer);
        }
    }, [isBobMode, bobResponseCount, hasTransitioned, setHasTransitioned]);

    useEffect(() => {
        if (isBobMode) {
            resetBobResponseCount();
        }
    }, [isBobMode, resetBobResponseCount]);

    const scrollToBottom = useCallback(() => {
        const ref = shouldShowCloneView || shouldShowBobView ? cloningViewRef : chatContainerRef;
        scrollUtil(ref, shouldShowCloneView || shouldShowBobView);
    }, [shouldShowCloneView, shouldShowBobView]);

    const handleMessageSubmit = useCallback(async (content: string, shouldScroll: boolean = true) => {
        if (!projectData) return;

        setIsSubmittingMessage(true);

        // 1. INSTANTLY show user message (within 0.5s)
        const userMessage = {
            id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            role: 'user' as const,
            content,
            type: 'text' as const,
            timestamp: new Date(),
            animationComplete: true // User messages don't need animation
        };

        // Immediately update UI with user message
        setMessages([...messages, userMessage]);

        if (shouldScroll) {
            scrollToBottom();
        }

        try {
            const token = await getToken({
                template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
            });

            if (!token) {
                throw new Error('Authentication token not found');
            }

            const versionId = projectData.activeVersionId;

            // 2. Make API call in background
            const response = await createMessageAPI(projectData.id, versionId, content, token);

            // 3. Handle isGenerating field from response
            setIsGenerating(response.data.isGenerating);
            setHasReceivedApiResponse(true);

            // 4. AGGRESSIVE message refresh for instant loading
            mutateMessages();

            // Multiple refreshes to ensure instant loading
            setTimeout(() => mutateMessages(), 50);
            setTimeout(() => mutateMessages(), 100);
            setTimeout(() => mutateMessages(), 200);

        } catch (error: unknown) {
            console.error('Failed to submit message:', error);

            // Handle specific error cases
            const axiosError = error as { response?: { status?: number; data?: { message?: string } } };
            if (axiosError?.response?.status === 401) {
                console.error('Unauthorized: User session expired');
                // Could redirect to login or show auth modal
            } else if (axiosError?.response?.status === 403) {
                console.error('Forbidden: User limits exceeded or access denied');
                // Only show pricing modal for plan upgrade issues, not quota issues
                const errorMessage = axiosError?.response?.data?.message || '';
                if (errorMessage.includes('Upgrade your plan')) {
                    openModal('pricing', { projectId: projectData.id });
                }
            } else if (axiosError?.response?.status === 429) {
                console.error('Rate limited: Too many requests');
                // Could show rate limit message
            }
        } finally {
            setIsSubmittingMessage(false);
        }
    }, [projectData, getToken, messages, setMessages, scrollToBottom, mutateMessages, openModal]);

    const handleAnimationComplete = useCallback((messageId: string) => {
        updateMessage(messageId, { animationComplete: true });
    }, [updateMessage]);

    // SIMPLIFIED INPUT LOGIC - Fix the damn thing!
    const versionStatus = projectData?.versions?.[0]?.version_status;

    let isInputEnabled: boolean;

    // If version is processing or drafted, ALWAYS disable
    if (versionStatus === 'processing' || versionStatus === 'drafted') {
        isInputEnabled = false;
    }
    // If we have an API response and isGenerating is true, disable
    else if (hasReceivedApiResponse && isGenerating === true) {
        isInputEnabled = false;
    }
    // Otherwise, enable the input
    else {
        isInputEnabled = true;
    }

    // CRITICAL DEBUG - Let's see what's happening
    console.log('🚨 SIMPLIFIED INPUT DEBUG:', {
        versionStatus,
        hasReceivedApiResponse,
        isGenerating,
        isInputEnabled,
        'Logic used': versionStatus === 'processing' || versionStatus === 'drafted' ? 'Version blocking' :
                     hasReceivedApiResponse && isGenerating === true ? 'API blocking' : 'Enabled'
    });

    return {
        projectData,
        projectIsLoading,
        projectError,
        mutateProject,
        messages,
        isLoading,
        isResponseReady,
        isSubmittingMessage,
        isInputEnabled,
        isGenerating,
        hasReceivedApiResponse,

        activeView,
        setActiveView,
        allTasksCompleted,
        previewStage,
        shouldShowCloneView,
        shouldShowBobView,
        clonedUrl,

        chatContainerRef,
        cloningViewRef,

        handleMessageSubmit,
        handleAnimationComplete,
        handleAllTasksCompleted,
        incrementBobResponseCount
    };
};
import { useMemo } from 'react';
import { strs } from '@/constants/pages/projects/strs';

interface UseIframeUrlProps {
    showLoadingState: boolean;
    previewUrl?: string | null;
    publishUrl?: string | null;
    clonedUrl?: string;
    previewStage: number;
}

export const useIframeUrl = ({
    showLoadingState,
    previewUrl,
    publishUrl,
    clonedUrl,
    previewStage
}: UseIframeUrlProps): string => {
    return useMemo(() => {
        if (showLoadingState) return "";
        if (publishUrl) return `${publishUrl}`;
        if (previewUrl) return `${previewUrl}`;
        if (clonedUrl) return clonedUrl;
        return strs.iframe.urls[previewStage % strs.iframe.urls.length];
    }, [showLoadingState, previewStage, clonedUrl, previewUrl, publishUrl]);
};
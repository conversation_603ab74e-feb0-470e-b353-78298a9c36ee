import { useState, useCallback, useRef, useEffect } from "react";
import {
  AspectRatio,
  aspectRatioValues,
  CROP_CONSTANTS,
} from "@/constants/pages/projects/imageCropOverlay";

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface UseCropAreaProps {
  imageContainerRef: React.RefObject<HTMLDivElement | null>;
  imageDimensions: { width: number; height: number };
  isVisible: boolean;
}

export const useCropArea = ({
  imageContainerRef,
  imageDimensions,
  isVisible,
}: UseCropAreaProps) => {
  const [selectedAspect, setSelectedAspect] = useState<AspectRatio>("freeform");
  const [cropArea, setCropArea] = useState<CropArea>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const [isDragging, setIsDragging] = useState(false);
  type ResizeAction =
    | "top-left"
    | "top-right"
    | "bottom-left"
    | "bottom-right"
    | "top"
    | "bottom"
    | "left"
    | "right";
  const [isResizing, setIsResizing] = useState<ResizeAction | null>(null);
  const [startPos, setStartPos] = useState({
    x: 0,
    y: 0,
    cropX: 0,
    cropY: 0,
    cropWidth: 0,
    cropHeight: 0,
  });
  const startPosRef = useRef(startPos);
  const isDraggingRef = useRef(isDragging);
  const isResizingRef = useRef(isResizing);
  const selectedAspectRef = useRef(selectedAspect);
  const imageDimensionsRef = useRef(imageDimensions);

  useEffect(() => {
    startPosRef.current = startPos;
  }, [startPos]);

  useEffect(() => {
    isDraggingRef.current = isDragging;
  }, [isDragging]);

  useEffect(() => {
    isResizingRef.current = isResizing;
  }, [isResizing]);

  useEffect(() => {
    selectedAspectRef.current = selectedAspect;
  }, [selectedAspect]);

  useEffect(() => {
    imageDimensionsRef.current = imageDimensions;
  }, [imageDimensions]);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      setSelectedAspect("freeform");
      setImageLoaded(false);
      // Reset crop area to prevent permanent cropping when overlay is closed
      setCropArea({ x: 0, y: 0, width: 0, height: 0 });
    }
  }, [isVisible]);

  useEffect(() => {
    if (isVisible && imageContainerRef.current && !imageLoaded) {
      setTimeout(() => {
        if (imageContainerRef.current) {
          const img = imageContainerRef.current.querySelector("img");
          if (img) {
            const rect = img.getBoundingClientRect();

            setCropArea({
              x: 0,
              y: 0,
              width: rect.width,
              height: rect.height,
            });
            setImageLoaded(true);
          }
        }
      }, CROP_CONSTANTS.IMAGE_LOAD_DELAY);
    }
  }, [isVisible, imageLoaded, imageContainerRef]);

  const handleImageLoad = useCallback(
    (e: React.SyntheticEvent<HTMLImageElement>) => {
      if (imageContainerRef.current) {
        const img = e.currentTarget;
        const imgRect = img.getBoundingClientRect();

        setCropArea({
          x: 0,
          y: 0,
          width: imgRect.width,
          height: imgRect.height,
        });
        setImageLoaded(true);
      }
    },
    [imageContainerRef]
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>, action?: ResizeAction) => {
      e.preventDefault();
      e.stopPropagation();

      const rect = imageContainerRef.current?.getBoundingClientRect();
      if (!rect) return;

      if (action) {
        setIsResizing(action);
      } else {
        setIsDragging(true);
      }

      setStartPos({
        x: e.clientX,
        y: e.clientY,
        cropX: cropArea.x,
        cropY: cropArea.y,
        cropWidth: cropArea.width,
        cropHeight: cropArea.height,
      });
    },
    [imageContainerRef, cropArea]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!imageContainerRef.current) return;
      const rect = imageContainerRef.current.getBoundingClientRect();

      const startPos = startPosRef.current;
      const isDragging = isDraggingRef.current;
      const isResizing = isResizingRef.current;
      const selectedAspect = selectedAspectRef.current;
      const imageDimensions = imageDimensionsRef.current;

      const deltaX = e.clientX - startPos.x;
      const deltaY = e.clientY - startPos.y;

      if (isDragging) {
        const newX = startPos.cropX + deltaX;
        const newY = startPos.cropY + deltaY;

        setCropArea((prev) => ({
          ...prev,
          x: Math.max(0, Math.min(newX, rect.width - prev.width)),
          y: Math.max(0, Math.min(newY, rect.height - prev.height)),
        }));
      } else if (isResizing) {
        const aspectRatio =
          selectedAspect === "original"
            ? imageDimensions.width / imageDimensions.height
            : aspectRatioValues[selectedAspect];

        let newX = startPos.cropX;
        let newY = startPos.cropY;
        let newWidth = startPos.cropWidth;
        let newHeight = startPos.cropHeight;

        if (selectedAspect === "freeform") {
          if (isResizing.includes("right")) {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth + deltaX
            );
          }
          if (isResizing.includes("left")) {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth - deltaX
            );
            newX = startPos.cropX + startPos.cropWidth - newWidth;
          }
          if (isResizing.includes("bottom")) {
            newHeight = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropHeight + deltaY
            );
          }
          if (isResizing.includes("top")) {
            newHeight = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropHeight - deltaY
            );
            newY = startPos.cropY + startPos.cropHeight - newHeight;
          }
        } else if (aspectRatio) {
          if (isResizing === "top-left") {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth - deltaX
            );
            newHeight = newWidth / aspectRatio;
            newX = startPos.cropX + startPos.cropWidth - newWidth;
            newY = startPos.cropY + startPos.cropHeight - newHeight;
          } else if (isResizing === "top-right") {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth + deltaX
            );
            newHeight = newWidth / aspectRatio;
            newY = startPos.cropY + startPos.cropHeight - newHeight;
          } else if (isResizing === "bottom-left") {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth - deltaX
            );
            newHeight = newWidth / aspectRatio;
            newX = startPos.cropX + startPos.cropWidth - newWidth;
          } else if (isResizing === "bottom-right") {
            newWidth = Math.max(
              CROP_CONSTANTS.MIN_CROP_SIZE,
              startPos.cropWidth + deltaX
            );
            newHeight = newWidth / aspectRatio;
          }
        }

        if (newX < 0) {
          newWidth += newX;
          newX = 0;
          if (aspectRatio) {
            newHeight = newWidth / aspectRatio;
          }
        }
        if (newY < 0) {
          newHeight += newY;
          newY = 0;
          if (aspectRatio) {
            newWidth = newHeight * aspectRatio;
          }
        }
        if (newX + newWidth > rect.width) {
          newWidth = rect.width - newX;
          if (aspectRatio) {
            newHeight = newWidth / aspectRatio;
          }
        }
        if (newY + newHeight > rect.height) {
          newHeight = rect.height - newY;
          if (aspectRatio) {
            newWidth = newHeight * aspectRatio;
          }
        }

        newWidth = Math.max(CROP_CONSTANTS.MIN_CROP_SIZE, newWidth);
        newHeight = Math.max(CROP_CONSTANTS.MIN_CROP_SIZE, newHeight);

        setCropArea({
          x: newX,
          y: newY,
          width: newWidth,
          height: newHeight,
        });
      }
    },
    [imageContainerRef]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(null);
  }, []);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  const handleAspectChange = useCallback(
    (aspect: AspectRatio) => {
      setSelectedAspect(aspect);
      if (!imageContainerRef.current) return;

      const rect = imageContainerRef.current.getBoundingClientRect();

      if (aspect === "freeform") {
        return;
      }

      let aspectRatio: number | null = aspectRatioValues[aspect];

      if (
        aspect === "original" &&
        imageDimensions.width &&
        imageDimensions.height
      ) {
        aspectRatio = imageDimensions.width / imageDimensions.height;
      }

      if (!aspectRatio) return;

      let newWidth = rect.width;
      let newHeight = newWidth / aspectRatio;

      if (newHeight > rect.height) {
        newHeight = rect.height;
        newWidth = newHeight * aspectRatio;
      }

      const newX = (rect.width - newWidth) / 2;
      const newY = (rect.height - newHeight) / 2;

      setCropArea({
        x: Math.max(0, Math.min(newX, rect.width - newWidth)),
        y: Math.max(0, Math.min(newY, rect.height - newHeight)),
        width: newWidth,
        height: newHeight,
      });
    },
    [imageContainerRef, imageDimensions]
  );

  const handleApplyCrop = useCallback(
    (image: string, onCrop: (croppedImage: string) => void) => {
      if (!image || !imageContainerRef.current) return;

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const img = new Image();
      img.onload = () => {
        const container = imageContainerRef.current;
        if (!container) return;

        const containerImg = container.querySelector("img");
        if (!containerImg) return;

        const containerRect = containerImg.getBoundingClientRect();
        const scaleX = img.naturalWidth / containerRect.width;
        const scaleY = img.naturalHeight / containerRect.height;

        canvas.width = cropArea.width * scaleX;
        canvas.height = cropArea.height * scaleY;

        ctx.drawImage(
          img,
          cropArea.x * scaleX,
          cropArea.y * scaleY,
          cropArea.width * scaleX,
          cropArea.height * scaleY,
          0,
          0,
          canvas.width,
          canvas.height
        );

        const croppedImage = canvas.toDataURL("image/png");
        onCrop(croppedImage);
      };
      img.src = image;
    },
    [imageContainerRef, cropArea]
  );

  return {
    cropArea,
    selectedAspect,
    imageLoaded,
    handleMouseDown,
    handleImageLoad,
    handleAspectChange,
    handleApplyCrop,
    setImageLoaded,
  };
};

import { useState, useCallback } from 'react';
import { LabImage } from '@/data/labImages';
import { useLabImages } from './useLabImages';
import { useImageSelection } from './useImageSelection';
import { useFileDrop } from './useFileDrop';
import { useLabStore } from '@/stores/labStore';

export const useImageEditOverlay = () => {
    const [activeTab, setActiveTab] = useState<string>("manage");
    const [currentImage, setCurrentImage] = useState<LabImage | null>(null);

    const {
        uploadedImages,
        fileInputRef,
        openFilePicker
    } = useLabImages();

    const { addLabImage } = useLabStore();

    const {
        selectedImages,
        toggleImageSelection,
        clearSelection
    } = useImageSelection();

    const handleFileSelect = (files: FileList | null) => {
        if (files && files.length > 0) {
            const file = files[0];
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result as string;
                const timestamp = Date.now();
                const imageName = `uploaded_${timestamp}_0`;
                const newImage: LabImage = {
                    key: imageName,
                    url: result,
                    name: `${timestamp}_0_${file.name}`,
                    src: result
                };
                addLabImage(newImage);
                setCurrentImage(newImage);
                clearSelection();
                toggleImageSelection(imageName);
            };
            reader.readAsDataURL(file);
        }
    };

    const { isDragging, handleDragOver, handleDragLeave } = useFileDrop(handleFileSelect);

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        const files = e.dataTransfer.files;
        handleFileSelect(files);
    };

    const handleRemoveImage = () => {
        setCurrentImage(null);
        clearSelection();
    };

    const handleImageToggle = (imageName: string) => {
        if (selectedImages.has(imageName)) {
            clearSelection();
            setCurrentImage(null);
        } else {
            clearSelection();
            toggleImageSelection(imageName);
            const selectedImage = uploadedImages.find(img => (img.key || img.name) === imageName);
            if (selectedImage) {
                setCurrentImage(selectedImage);
            }
        }
    };

    const handleGeneratedImageUse = useCallback((image?: LabImage) => {
        setActiveTab("manage");
        if (image) {
            setTimeout(() => {
                clearSelection();
                const imageKey = image.key || image.name || '';
                if (imageKey) {
                    toggleImageSelection(imageKey);
                    setCurrentImage(image);
                }
            }, 100);
        }
    }, [clearSelection, toggleImageSelection]);

    return {
        activeTab,
        setActiveTab,
        currentImage,
        setCurrentImage,
        uploadedImages,
        fileInputRef,
        selectedImages,
        isDragging,
        handleDragOver,
        handleDragLeave,
        handleDrop,
        handleFileSelect,
        handleRemoveImage,
        handleImageToggle,
        openFilePicker,
        handleGeneratedImageUse,
        clearSelection
    };
};
import { CheckSquare, Trash, Download, MousePointerClick, Save } from 'lucide-react';
import { LabImage } from '@/data/labImages';
import { ViewType } from '@/types/chat';
import { useLabStore } from '@/stores/labStore';
import { useEditModeStore } from '@/stores/editModeStore';
import { strs } from '@/constants/pages/projects/strs';

interface UseLabActionsProps {
    selectedImages: Set<string>;
    uploadedImages: LabImage[];
    selectAll: () => void;
    deleteImages: () => void;
    downloadImages: () => void;
    clearSelection: () => void;
    onViewChange?: (view: ViewType) => void;
    onSaveImage?: (key: string, url: string) => void;
    allImages?: LabImage[]; // Combined backend + uploaded images
}

export const useLabActions = ({
    selectedImages,
    uploadedImages,
    selectAll,
    deleteImages,
    downloadImages,
    clearSelection,
    onViewChange,
    onSaveImage,
    allImages
}: UseLabActionsProps) => {
    const { setSelectedImageForUse, isImageSaved } = useLabStore();
    const { setEditMode } = useEditModeStore();

    const imagesToWorkWith = allImages || uploadedImages;
    const isAllSelected = selectedImages.size === imagesToWorkWith.length && imagesToWorkWith.length > 0;

    const handleDelete = () => {
        deleteImages();
        clearSelection();
    };

    const handleDownload = () => {
        downloadImages();
    };

    const handleUseIt = () => {
        const selectedImageName = Array.from(selectedImages)[0];
        const selectedImage = imagesToWorkWith.find(img => (img.name || img.key) === selectedImageName);
        if (selectedImage) {
            setSelectedImageForUse(selectedImage);
            clearSelection();
            if (onViewChange) {
                onViewChange('bilder' as ViewType);
            }
            setEditMode(true, true);
        }
    };

    const handleSave = () => {
        const selectedImageName = Array.from(selectedImages)[0];
        const selectedImage = imagesToWorkWith.find(img => (img.name || img.key) === selectedImageName);
        if (selectedImage && selectedImage.key && selectedImage.url && onSaveImage) {
            onSaveImage(selectedImage.key, selectedImage.url);
        }
    };

    const handleSelectToggle = () => {
        if (isAllSelected) {
            clearSelection();
        } else {
            selectAll();
        }
    };

    // Check if selected image is from API (has key) and not saved yet
    const selectedImageName = Array.from(selectedImages)[0];
    const selectedImage = imagesToWorkWith.find(img => (img.name || img.key) === selectedImageName);
    const showSaveButton = selectedImages.size === 1 && 
                          selectedImage && 
                          selectedImage.key && 
                          !selectedImage.key.startsWith('uploaded_') && 
                          !isImageSaved(selectedImage.key);

    const actionButtons = [
        { icon: CheckSquare, label: isAllSelected ? strs.lab.actionBar.deselectAll : strs.lab.actionBar.selectAll, onClick: handleSelectToggle },
        { icon: Trash, label: strs.lab.actionBar.delete, onClick: handleDelete },
        { icon: Download, label: strs.lab.actionBar.download, onClick: handleDownload },
        ...(selectedImages.size === 1 ? [{ icon: MousePointerClick, label: strs.lab.actionBar.useIt, onClick: handleUseIt }] : []),
        ...(showSaveButton ? [{ icon: Save, label: 'Save', onClick: handleSave }] : []),
    ];

    return {
        actionButtons,
        handleUseIt
    };
};
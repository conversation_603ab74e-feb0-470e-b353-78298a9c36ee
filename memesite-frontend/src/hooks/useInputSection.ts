import { useState, useCallback, useRef, useEffect, useMemo, KeyboardEvent } from "react";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useProjectStore } from "@/stores/projectStore";
import { useUIStore } from "@/stores/uiStore";
import { useModalStore } from "@/stores/modalStore";
import { useChatStore } from "@/stores/chatStore";
import { useLabStore } from "@/stores/labStore";
import { useEditModeStore } from "@/stores/editModeStore";
import { useAuthContextStore } from "@/stores/authContextStore";

import { strs } from "@/constants/pages/home/<USER>/strs";

interface UseInputSectionProps {
    onSubmit?: (value: string) => void;
    onInputChange?: (value: string) => void;
    isLoading?: boolean;
    enableConversation?: boolean;
}

export const useInputSection = ({
    onSubmit,
    onInputChange,
    isLoading = false,
    enableConversation = false
}: UseInputSectionProps) => {
    const { isSignedIn, getToken } = useAuth();
    const router = useRouter();
    const [inputValue, setInputValue] = useState("");
    const [isCloneMode, setIsCloneMode] = useState(false);
    const [isPlanMode, setIsPlanMode] = useState(false);
    const [isBobMode, setIsBobMode] = useState(false);
    const [isDictating, setIsDictating] = useState(false);
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [isCreatingProject, setIsCreatingProject] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const { openModal } = useModalStore();
    const { createProject } = useProjectStore();
    const { clearMessages } = useChatStore();
    const { pendingBobMode, setPendingBobMode, isBobModeActive } = useUIStore();
    const { selectedImageForUse, clearSelectedImage } = useLabStore();
    const { setEditMode } = useEditModeStore();
    const { setPendingRequest, setRedirectingToAuth } = useAuthContextStore();

    useEffect(() => {
        if (pendingBobMode) {
            setInputValue("Let's build a website together!");
            setIsCloneMode(false);
            setIsPlanMode(false);
            setIsBobMode(true);
            if (textareaRef.current) {
                textareaRef.current.focus();
                textareaRef.current.setSelectionRange(textareaRef.current.value.length, textareaRef.current.value.length);
            }
            setPendingBobMode(false);
        }
    }, [pendingBobMode, setPendingBobMode]);

    useEffect(() => {
        if (!isBobModeActive && isBobMode) {
            setIsBobMode(false);
            setInputValue("");
        }
    }, [isBobModeActive, isBobMode]);

    const handleSubmit = useCallback(() => {
        if (onSubmit) {
            if (isLoading && inputValue.trim()) {
                onSubmit("");
                setTimeout(() => {
                    onSubmit(inputValue);
                    setInputValue("");
                }, 100);
            } else if (isLoading) {
                onSubmit("");
            } else if (inputValue.trim()) {
                onSubmit(inputValue);
                setInputValue("");
            } else if (!inputValue.trim() && enableConversation) {
                openModal('voice-conversation');
            }
        } else {
            if (!isSignedIn) {
                setPendingRequest({
                    message: inputValue.trim(),
                    mode: isCloneMode ? 'clone' : isBobMode ? 'bob' : 'build'
                });
                setRedirectingToAuth(true);
                setInputValue("");
                router.push("/sign-in");
                return;
            }

            const handleCreateProject = async () => {
                // Immediately show loading state (within 0.5s)
                setIsCreatingProject(true);

                // Store the message immediately for background processing
                setPendingRequest({
                    message: inputValue.trim(),
                    mode: isCloneMode ? 'clone' : isBobMode ? 'bob' : 'build'
                });

                // Navigate to loading screen immediately
                router.push('/creating-project');

                try {
                    clearMessages();
                    const project = await createProject({
                        name: inputValue.trim()
                    }, getToken);

                    if (project) {
                        // Immediately redirect to project page as soon as API responds
                        router.replace(`/projects/${project.id}`);
                    } else {
                        // Go back to home and show pricing modal
                        router.replace('/');
                        openModal('pricing');
                    }
                } catch (error) {
                    console.error('Failed to create project:', error);
                    // Go back to home on error
                    router.replace('/');
                } finally {
                    setIsCreatingProject(false);
                }
            };

            handleCreateProject();
        }
    }, [isSignedIn, router, onSubmit, inputValue, isCloneMode, isBobMode, createProject, isLoading, getToken, openModal, enableConversation, clearMessages, setPendingRequest, setRedirectingToAuth]);

    const handleKeyDown = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            if (!isLoading || inputValue.trim()) {
                handleSubmit();
            }
        }
    }, [handleSubmit, isLoading, inputValue]);

    const handleInputChange = useCallback((value: string) => {
        setInputValue(value);
        if (onInputChange) {
            onInputChange(value);
        }
    }, [onInputChange]);

    const handleCloneClick = useCallback(() => {
        setIsCloneMode(prev => !prev);
        setIsPlanMode(false);
        setIsBobMode(false);
        if (!isCloneMode) {
            setInputValue("Clone this website URL: ");
            if (textareaRef.current) {
                textareaRef.current.focus();
                const cursorPosition = "Clone this website URL: ".length;
                textareaRef.current.setSelectionRange(cursorPosition, cursorPosition);
            }
        } else {
            setInputValue("");
        }
    }, [isCloneMode]);

    const handlePlanClick = useCallback(() => {
        setIsPlanMode(prev => !prev);
        setIsCloneMode(false);
        setIsBobMode(false);
        if (!isPlanMode) {
            setInputValue("");
            if (textareaRef.current) {
                textareaRef.current.focus();
            }
        } else {
            setInputValue("");
        }
    }, [isPlanMode]);

    const handleVoiceInput = useCallback((text: string) => {
        setInputValue(text);
        if (textareaRef.current) {
            textareaRef.current.focus();
            setTimeout(() => {
                if (textareaRef.current) {
                    textareaRef.current.setSelectionRange(text.length, text.length);
                }
            }, 0);
        }
    }, []);

    const handleDictatingChange = useCallback((isListening: boolean) => {
        setIsDictating(isListening);
    }, []);

    const handleImageRemove = useCallback(() => {
        clearSelectedImage();
        setEditMode(false);
        setSelectedImage(null);
    }, [clearSelectedImage, setEditMode]);

    const handleImageSelect = useCallback(() => {
        fileInputRef.current?.click();
    }, []);

    const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            setSelectedImage(file);
        }
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    }, []);

    const placeholder = useMemo(() => {
        if (isDictating) return strs.inputPlaceholderListening;
        if (isCloneMode) return strs.inputPlaceholderClone;
        if (isBobMode) return strs.inputPlaceholderBob;
        return strs.inputPlaceholder;
    }, [isDictating, isCloneMode, isBobMode]);

    return {
        inputValue,
        isCloneMode,
        isPlanMode,
        isBobMode,
        isDictating,
        textareaRef,
        fileInputRef,
        selectedImageForUse,
        selectedImage,
        isSignedIn,
        isLoading: isLoading || isCreatingProject,
        enableConversation,
        placeholder,
        handleSubmit,
        handleKeyDown,
        handleInputChange,
        handleCloneClick,
        handlePlanClick,
        handleVoiceInput,
        handleDictatingChange,
        handleImageRemove,
        handleImageSelect,
        handleFileChange
    };
};
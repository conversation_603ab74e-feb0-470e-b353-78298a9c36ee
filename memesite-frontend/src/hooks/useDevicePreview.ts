import { useState, useMemo } from "react";

export type DeviceType = "desktop" | "mobile";

export const useDevicePreview = () => {
    const [activeDevice, setActiveDevice] = useState<DeviceType>("desktop");

    const deviceClasses = useMemo(() => {
        return activeDevice === 'mobile' 
            ? 'h-full aspect-[9/16]' 
            : 'h-full w-full';
    }, [activeDevice]);

    const isDesktop = activeDevice === "desktop";
    const isMobile = activeDevice === "mobile";

    return {
        activeDevice,
        setActiveDevice,
        deviceClasses,
        isDesktop,
        isMobile
    };
};
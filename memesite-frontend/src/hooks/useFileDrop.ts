import { useState, DragEvent } from 'react';

export const useFileDrop = (onFilesDropped: (files: FileList) => void) => {
    const [isDragging, setIsDragging] = useState(false);

    const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);
        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
            onFilesDropped(files);
        }
    };

    return {
        isDragging,
        handleDragOver,
        handleDragLeave,
        handleDrop
    };
};
import { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { ImageContent, ViewType } from "@/types/chat";
import { LabImage } from "@/data/labImages";
import { useLabStore } from "@/stores/labStore";
import { useUIStore } from "@/stores/uiStore";
import { useEditModeStore } from "@/stores/editModeStore";
import { saveImageAPI } from "@/utils/api";

interface UseImageMessageProps {
    content: ImageContent;
    onImageComplete?: () => void;
    wasStopped?: boolean;
    viewContext?: string;
    onImageApply?: () => void;
    onImageUse?: (image?: LabImage) => void;
}

export const useImageMessage = ({ content, onImageComplete, wasStopped, viewContext, onImageApply, onImageUse }: UseImageMessageProps) => {
    const { addLabImage, setSelectedImageForUse, isImageSaved } = useLabStore();
    const { setActiveView } = useUIStore();
    const { setEditMode } = useEditModeStore();
    const { getToken } = useAuth();

    const [imageLoaded, setImageLoaded] = useState(!content.isGenerating || wasStopped);
    const [isApplied, setIsApplied] = useState(false);
    const [isUsed, setIsUsed] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const generationTime = content.generationTime || 3000;

    useEffect(() => {
        if (content.imageUrl && imageLoaded && !content.isGenerating) {
            setIsApplied(isImageSaved(content.imageUrl));
        }
    }, [content.imageUrl, imageLoaded, content.isGenerating, isImageSaved]);

    useEffect(() => {
        if (wasStopped) {
            setImageLoaded(true);
            return;
        }

        if (!content.imageUrl || imageLoaded) return;

        if (!content.isGenerating) {
            setImageLoaded(true);
            if (onImageComplete) {
                onImageComplete();
            }
            return;
        }

        const timer = setTimeout(() => {
            setImageLoaded(true);
            if (onImageComplete) {
                onImageComplete();
            }
        }, generationTime);

        return () => clearTimeout(timer);
    }, [content.imageUrl, content.isGenerating, generationTime, onImageComplete, imageLoaded, wasStopped]);

    const handleApply = async () => {
        if (content.imageUrl && content.key && getToken) {
            setIsSaving(true);
            try {
                const token = await getToken();
                if (token) {
                    // Call the backend save API to move image from /tmp/ to main folder
                    await saveImageAPI(content.key, content.imageUrl, token);
                    setIsApplied(true);
                    
                    if (viewContext === 'imageEdit' && onImageApply) {
                        onImageApply();
                    }
                }
            } catch (error) {
                console.error('Failed to save image:', error);
            } finally {
                setIsSaving(false);
            }
        }
    };

    const handleUse = () => {
        if (content.imageUrl) {
            const timestamp = Date.now();
            const imageForUse: LabImage = {
                key: `generated_${timestamp}`,
                url: content.imageUrl,
                name: `generated_${timestamp}_${content.prompt.slice(0, 20).replace(/\s+/g, '_')}`,
                src: content.imageUrl
            };
            if (!isApplied) {
                addLabImage(imageForUse);
            }

            if (viewContext === 'imageEdit') {
                if (onImageUse) {
                    onImageUse(imageForUse);
                }
            } else {
                setSelectedImageForUse(imageForUse);
                setActiveView('bilder' as ViewType);
                setEditMode(true, true);
            }

            setIsUsed(true);
        }
    };

    return {
        imageLoaded,
        isApplied,
        isUsed,
        isSaving,
        handleApply,
        handleUse
    };
};
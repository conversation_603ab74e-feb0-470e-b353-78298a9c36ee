"use client"

import { useState, useEffect, useRef, useCallback } from 'react'

interface UseSpeechRecognitionOptions {
  language?: string
  continuous?: boolean
  interimResults?: boolean
  onStart?: () => void
  onStop?: () => void
  onError?: (error: string) => void
  onResult?: (transcript: string) => void
  onListeningChange?: (isListening: boolean) => void
}

interface SpeechRecognitionState {
  isListening: boolean
  isSupported: boolean
  transcript: string
  error: string | null
}

export function useSpeechRecognition({
  language = 'en-US',
  continuous = true,
  interimResults = true,
  onStart,
  onStop,
  onError,
  onResult,
  onListeningChange
}: UseSpeechRecognitionOptions = {}) {
  const [state, setState] = useState<SpeechRecognitionState>({
    isListening: false,
    isSupported: false,
    transcript: '',
    error: null
  })

  const recognitionRef = useRef<SpeechRecognition | null>(null)
  const finalTranscriptRef = useRef('')
  const callbacksRef = useRef({ onStart, onStop, onError, onResult, onListeningChange })

  useEffect(() => {
    callbacksRef.current = { onStart, onStop, onError, onResult, onListeningChange }
  }, [onStart, onStop, onError, onResult, onListeningChange])

  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition

    if (SpeechRecognition) {
      setState(prev => ({ ...prev, isSupported: true }))

      recognitionRef.current = new SpeechRecognition()
      const recognition = recognitionRef.current

      recognition.continuous = continuous
      recognition.interimResults = interimResults
      recognition.lang = language

      recognition.onstart = () => {
        setState(prev => ({
          ...prev,
          isListening: true,
          error: null
        }))
        callbacksRef.current.onStart?.()
        callbacksRef.current.onListeningChange?.(true)
      }

      recognition.onresult = (event) => {
        let interimTranscript = ''
        let finalTranscript = finalTranscriptRef.current

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        finalTranscriptRef.current = finalTranscript

        const fullTranscript = finalTranscript + interimTranscript
        setState(prev => ({ ...prev, transcript: fullTranscript }))
        callbacksRef.current.onResult?.(fullTranscript)
      }

      recognition.onerror = (event) => {
        const errorMessage = event.error === 'not-allowed'
          ? 'Microphone access denied. Please allow access in browser settings.'
          : event.error === 'no-speech'
          ? 'No speech detected. Try speaking louder.'
          : `Speech recognition error: ${event.error}`

        setState(prev => ({
          ...prev,
          isListening: false,
          error: errorMessage
        }))
        callbacksRef.current.onError?.(errorMessage)
        callbacksRef.current.onListeningChange?.(false)
      }

      recognition.onend = () => {
        setState(prev => ({ ...prev, isListening: false }))
        callbacksRef.current.onStop?.()
        callbacksRef.current.onListeningChange?.(false)
      }
    } else {
      setState(prev => ({
        ...prev,
        isSupported: false,
        error: "Your browser does not support speech recognition"
      }))
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [language, continuous, interimResults])

  const startListening = useCallback(() => {
    if (!state.isSupported || state.isListening) return

    finalTranscriptRef.current = ''
    setState(prev => ({ ...prev, transcript: '', error: null }))

    try {
      recognitionRef.current?.start()
    } catch (error) {
      console.error('Failed to start speech recognition:', error)
      setState(prev => ({ ...prev, isListening: false }))
    }
  }, [state.isSupported, state.isListening])

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
    }
  }, [])

  const reset = useCallback(() => {
    finalTranscriptRef.current = ''
    setState(prev => ({ ...prev, transcript: '', error: null }))
  }, [])

  return {
    ...state,
    startListening,
    stopListening,
    reset
  }
}

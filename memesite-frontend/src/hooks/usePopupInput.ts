import { useState, useEffect, useCallback } from 'react';
import { usePopupStore } from '@/stores/popupStore';
import { useEditModeStore } from '@/stores/editModeStore';
import { useProjectChangesStore } from '@/stores/projectChangesStore';

interface UsePopupInputReturn {
  value: string;
  setValue: (value: string) => void;
  handleSubmit: () => void;
  handleDelete: () => void;
  handleClose: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
}

export const usePopupInput = (
  onSendMessage?: (content: string) => void,
  isLoading?: boolean
): UsePopupInputReturn => {
  const { selector, hidePopup } = usePopupStore();
  const { setSelectedElement } = useEditModeStore();
  const { addChange } = useProjectChangesStore();

  const [value, setValue] = useState('');
  const [lastSelector, setLastSelector] = useState(selector);

  useEffect(() => {
    if (selector !== lastSelector) {
      setValue('');
      setLastSelector(selector);
    }
  }, [selector, lastSelector]);

  useEffect(() => {
    const handleResize = () => hidePopup();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [hidePopup]);

  const handleClose = useCallback(() => {
    setSelectedElement(null);
    hidePopup();
  }, [setSelectedElement, hidePopup]);

  const handleSubmit = useCallback(() => {
    if (value.trim() && onSendMessage && !isLoading) {
      onSendMessage(value.trim());
      setValue('');
    }
  }, [value, onSendMessage, isLoading]);

  const handleDelete = useCallback(() => {
    const iframe = document.querySelector('iframe');
    if (!iframe || !selector) return;

    try {
      const contentDocument = iframe.contentDocument;
      if (contentDocument) {
        const element = contentDocument.querySelector(selector);
        if (element) {
          element.remove();

          addChange({
            type: 'deleteElement',
            selector,
            data: {
              element: {
                tagName: element.tagName.toLowerCase(),
                attributes: Array.from(element.attributes).reduce((acc, attr) => {
                  acc[attr.name] = attr.value;
                  return acc;
                }, {} as Record<string, string>),
                content: element.innerHTML
              }
            }
          });
        }
      }
    } catch (error) {
      console.error('Failed to delete element:', error);
    }

    handleClose();
  }, [selector, addChange, handleClose]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleClose();
    }
  }, [handleSubmit, handleClose, isLoading]);

  return {
    value,
    setValue,
    handleSubmit,
    handleDelete,
    handleClose,
    handleKeyDown
  };
};
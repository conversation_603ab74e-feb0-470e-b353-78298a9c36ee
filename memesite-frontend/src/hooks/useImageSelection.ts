import { useState } from 'react';
import { LabImage } from '@/data/labImages';

export const useImageSelection = () => {
    const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

    const toggleImageSelection = (imageName: string) => {
        setSelectedImages(prev => {
            const newSet = new Set(prev);
            if (newSet.has(imageName)) {
                newSet.delete(imageName);
            } else {
                newSet.add(imageName);
            }
            return newSet;
        });
    };

    const clearSelection = () => {
        setSelectedImages(new Set());
    };

    const selectAll = (images: LabImage[]) => {
        setSelectedImages(new Set(images.map(img => img.key || img.name || '')));
    };

    return {
        selectedImages,
        toggleImageSelection,
        clearSelection,
        selectAll
    };
};
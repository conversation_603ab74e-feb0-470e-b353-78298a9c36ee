import { useMemo } from 'react';
import { ChatMessage } from '@/types/chat';
import { shouldAnimate } from '@/utils/chat';

export const useMessageAnimationState = (
    messages: ChatMessage[],
    isLoading: boolean,
    isResponseReady: boolean
) => {
    // Simply check if any message should be animating right now
    // No need for complex state management that can cause infinite loops
    const isAnyMessageAnimating = useMemo(() => {
        return messages.some((message, index) =>
            shouldAnimate(message, index, messages.length, isLoading, isResponseReady)
        );
    }, [messages, isLoading, isResponseReady]);

    return { isAnyMessageAnimating };
};
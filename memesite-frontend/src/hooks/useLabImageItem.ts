import { useMemo } from 'react';

interface UseLabImageItemProps {
    isSelected: boolean;
    showSelectText: boolean;
}

export const useLabImageItem = ({ isSelected, showSelectText }: UseLabImageItemProps) => {
    const checkmarkClasses = useMemo(() => {
        if (isSelected) return 'opacity-100';
        if (showSelectText) return 'opacity-0';
        return 'opacity-0 group-hover:opacity-100';
    }, [isSelected, showSelectText]);

    const checkIconClasses = useMemo(() =>
        isSelected ? 'opacity-100' : 'opacity-0'
        , [isSelected]);

    const purpleOverlayClasses = useMemo(() =>
        isSelected ? 'opacity-100' : 'opacity-0'
        , [isSelected]);

    const borderClasses = useMemo(() =>
        isSelected ? 'border-purple' : 'border-transparent'
        , [isSelected]);

    const showSelectOverlay = showSelectText && !isSelected;

    return {
        checkmarkClasses,
        checkIconClasses,
        purpleOverlayClasses,
        borderClasses,
        showSelectOverlay
    };
};
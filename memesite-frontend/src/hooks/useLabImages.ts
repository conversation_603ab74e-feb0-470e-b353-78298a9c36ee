import { useRef } from 'react';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { LabImage } from '@/data/labImages';
import { useLabStore } from '@/stores/labStore';

export const useLabImages = () => {
    const { labImages: uploadedImages, setLabImages, addLabImage } = useLabStore();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (files: FileList | null) => {
        if (!files) return;

        const imageFiles = Array.from(files).filter(file =>
            file.type.startsWith('image/')
        );

        imageFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const timestamp = Date.now();
                const newImage: LabImage = {
                    key: `uploaded_${timestamp}_${index}`,
                    url: e.target?.result as string,
                    name: `${timestamp}_${index}_${file.name}`,
                    src: e.target?.result as string
                };
                addLabImage(newImage);
            };
            reader.readAsDataURL(file);
        });
    };

    const deleteImages = (imageNames: Set<string>) => {
        setLabImages(
            uploadedImages.filter(img => !imageNames.has(img.name || img.key || ''))
        );
    };

    const downloadImages = async (imageNames: Set<string>, allImages: LabImage[]) => {
        if (imageNames.size === 1) {
            const imageName = Array.from(imageNames)[0];
            const image = allImages.find(img => (img.name || img.key) === imageName);
            if (image) {
                const imageUrl = image.url || image.src || '';
                const imageName_clean = image.name || image.key || 'image';
                
                // For external URLs, we need to fetch and download
                if (imageUrl.startsWith('http')) {
                    try {
                        const response = await fetch(imageUrl);
                        const blob = await response.blob();
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `${imageName_clean}.jpg`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        // Clean up
                        setTimeout(() => URL.revokeObjectURL(url), 100);
                    } catch (error) {
                        console.error('Failed to download image:', error);
                    }
                } else {
                    // For data URLs, direct download
                    const link = document.createElement('a');
                    link.href = imageUrl;
                    link.download = `${imageName_clean}.jpg`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        } else if (imageNames.size > 1) {
            const zip = new JSZip();
            const imagesFolder = zip.folder('lab-images');

            const promises = Array.from(imageNames).map(async (imageName, index) => {
                const image = allImages.find(img => (img.name || img.key) === imageName);
                if (image && imagesFolder) {
                    try {
                        const imageUrl = image.url || image.src;
                        if (!imageUrl) return;
                        
                        const response = await fetch(imageUrl);
                        const blob = await response.blob();

                        const extension = blob.type.split('/')[1] || 'jpg';
                        const cleanName = image.name || image.key || `image_${index + 1}`;
                        const fileName = `${cleanName}.${extension}`;

                        imagesFolder.file(fileName, blob);
                    } catch (error) {
                        console.error(`Failed to add image ${imageName} to zip:`, error);
                    }
                }
            });

            await Promise.all(promises);

            const content = await zip.generateAsync({ type: 'blob' });

            const url = URL.createObjectURL(content);
            const link = document.createElement('a');
            link.href = url;
            link.download = `lab-images-${Date.now()}.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            setTimeout(() => URL.revokeObjectURL(url), 100);
        }
    };

    const openFilePicker = () => {
        fileInputRef.current?.click();
    };

    return {
        uploadedImages,
        fileInputRef,
        handleFileSelect,
        deleteImages,
        downloadImages,
        openFilePicker
    };
};
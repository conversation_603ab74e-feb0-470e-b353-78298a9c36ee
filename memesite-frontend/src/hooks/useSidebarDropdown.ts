import { useState, useRef, useEffect } from "react";
import type { SidebarCategory } from "@/types/sidebar";

export const useSidebarDropdown = (categories: SidebarCategory[]) => {
    const categoriesWithDropdowns = categories.filter(cat => cat.subItems && cat.subItems.length > 0);
    const shouldAutoExpand = categoriesWithDropdowns.length > 1;
    const initialExpanded = shouldAutoExpand && categoriesWithDropdowns[0] ? categoriesWithDropdowns[0].id : null;
    
    const [expandedCategory, setExpandedCategory] = useState<string | null>(initialExpanded);
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [selectedSubItem, setSelectedSubItem] = useState<string | null>(null);
    const contentRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    const toggleCategory = (categoryId: string) => {
        if (expandedCategory === categoryId && categoriesWithDropdowns.length === 1) {
            setExpandedCategory(null);
        } else if (expandedCategory !== categoryId) {
            setExpandedCategory(categoryId);
        } else if (categoriesWithDropdowns.length > 1 && expandedCategory === categoryId) {
            setExpandedCategory(null);
        }
    };

    const handleCategoryClick = (category: SidebarCategory, onItemSelect?: (categoryId: string, subItemId?: string) => void) => {
        if (category.onClick) {
            category.onClick();
        } else {
            setSelectedCategory(category.id);
            setSelectedSubItem(null);
            toggleCategory(category.id);
            onItemSelect?.(category.id);
        }
    };

    const handleSubItemClick = (categoryId: string, subItemId: string, onItemSelect?: (categoryId: string, subItemId?: string) => void) => {
        setSelectedCategory(categoryId);
        setSelectedSubItem(subItemId);
        onItemSelect?.(categoryId, subItemId);
    };

    return {
        expandedCategory,
        selectedCategory,
        selectedSubItem,
        contentRefs,
        mounted,
        handleCategoryClick,
        handleSubItemClick
    };
};
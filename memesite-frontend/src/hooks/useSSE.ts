import { useEffect, useRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { sseService } from '@/utils/sse';

export function useSSE() {
  const { getToken } = useAuth();
  const isConnectedRef = useRef(false);

  useEffect(() => {
    const connectSSE = async () => {
      try {
        const token = await getToken();

        if (token && !isConnectedRef.current) {
          sseService.connect(token);
          isConnectedRef.current = true;
        }
      } catch (error) {
        console.error('Failed to get token for SSE connection:', error);
      }
    };

    connectSSE();

    return () => {
      if (isConnectedRef.current) {
        sseService.disconnect();
        isConnectedRef.current = false;
      }
    };
  }, [getToken]);

  return {
    isConnected: sseService.isConnected(),
    disconnect: () => {
      sseService.disconnect();
      isConnectedRef.current = false;
    }
  };
}

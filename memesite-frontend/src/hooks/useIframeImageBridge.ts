import { useState, useCallback, useEffect } from 'react';
import { LabImage } from '@/data/labImages';
import { useEditModeStore } from '@/stores/editModeStore';

export const useIframeImageBridge = () => {
    const { selectedElement, isEditMode } = useEditModeStore();
    const [targetImageSelector, setTargetImageSelector] = useState<string | null>(null);
    const [showImageOverlay, setShowImageOverlay] = useState(false);
    const [preservedElementData, setPreservedElementData] = useState<{
        selector: string;
        isBackground?: boolean;
    } | null>(null);

    const isImageElement = useCallback(() => {
        if (!selectedElement) return false;

        return selectedElement.tagName === 'img' ||
            !!selectedElement.imageData?.src;
    }, [selectedElement]);

    const getCurrentImageFromElement = useCallback((): LabImage | null => {
        if (!selectedElement || !isImageElement()) return null;

        let src = selectedElement.imageData?.src ||
            selectedElement.attributes?.src;

        if (!src) {
            return null;
        }

        if (src.startsWith('http://') || src.startsWith('https://')) {
            try {
                const url = new URL(src);
                src = url.pathname;
            } catch {
            }
        }

        return {
            key: `current-image-${Date.now()}`,
            url: src,
            name: `current-image-${Date.now()}`,
            src: src
        };
    }, [selectedElement, isImageElement]);

    const handleIframeImageReplace = useCallback((newImage: LabImage, selector?: string) => {
        const elementData = preservedElementData || (selectedElement ? {
            selector: selectedElement.selector,
            isBackground: selectedElement.imageData?.isBackground
        } : null);

        const targetSelector = selector || elementData?.selector;

        if (!targetSelector || !newImage.src) {
            return;
        }

        const iframe = document.querySelector('iframe') as HTMLIFrameElement;
        if (iframe?.contentWindow) {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                if (iframeDoc) {
                    const element = iframeDoc.querySelector(targetSelector);
                    if (element) {
                        if (elementData?.isBackground) {
                            (element as HTMLElement).style.backgroundImage = `url('${newImage.src}')`;
                        } else if (element.tagName === 'IMG') {
                            (element as HTMLImageElement).src = newImage.src;
                            (element as HTMLImageElement).srcset = '';
                            (element as HTMLImageElement).style.display = 'none';
                            void (element as HTMLImageElement).offsetHeight;
                            (element as HTMLImageElement).style.display = '';
                        }
                    }
                }
            } catch {
                const message = {
                    type: 'webild-replace-image',
                    data: {
                        selector: targetSelector,
                        newSrc: newImage.src,
                        isBackground: elementData?.isBackground
                    }
                };
                iframe.contentWindow?.postMessage(message, '*');
            }
        }
    }, [preservedElementData, selectedElement]);

    useEffect(() => {
        if (isEditMode && isImageElement() && selectedElement) {
            setTargetImageSelector(selectedElement.selector);
            setPreservedElementData({
                selector: selectedElement.selector,
                isBackground: selectedElement.imageData?.isBackground
            });
        } else if (!isEditMode) {
            setTargetImageSelector(null);
            setPreservedElementData(null);
        }
    }, [isEditMode, selectedElement, isImageElement]);

    const openImageEditOverlay = useCallback(() => {
        if (isImageElement()) {
            setShowImageOverlay(true);
        }
    }, [isImageElement]);

    const closeImageEditOverlay = useCallback(() => {
        setShowImageOverlay(false);
        setTargetImageSelector(null);
    }, []);

    return {
        isImageElement: isImageElement(),
        currentIframeImage: getCurrentImageFromElement(),
        targetImageSelector,
        showImageOverlay,
        openImageEditOverlay,
        closeImageEditOverlay,
        handleIframeImageReplace
    };
};
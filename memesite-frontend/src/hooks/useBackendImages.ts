import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { getImagesAPI, deleteImageAPI, deleteBulkImagesAPI, saveImageAPI } from '@/utils/api';
import { LabImage } from '@/data/labImages';
import { ImageItem } from '@/types/project';
import { extractImageNameFromUrl } from '@/utils/imageHelpers';

export const useBackendImages = (shouldFetch: boolean = true) => {
    const { getToken } = useAuth();
    const [backendImages, setBackendImages] = useState<LabImage[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const refreshImages = useCallback(async () => {
        if (!getToken || !shouldFetch) return;

        setIsLoading(true);
        setError(null);

        try {
            const token = await getToken();
            if (!token) return;

            const images: ImageItem[] = await getImagesAPI(token);

            // Convert ImageItem[] to LabImage format (NO SORTING - keep original order)
            const labImages: LabImage[] = images.map((image) => ({
                key: image.key,
                url: image.url,
                name: extractImageNameFromUrl(image.url), // Use extracted name
                src: image.url // Backward compatibility
            }));

            setBackendImages(labImages);
        } catch (err) {
            console.error('Failed to fetch images:', err);
            setError('Failed to fetch images');
        } finally {
            setIsLoading(false);
        }
    }, [getToken, shouldFetch]);

    const deleteBackendImage = useCallback(async (key: string, url: string) => {
        if (!getToken) return false;
        
        try {
            const token = await getToken();
            if (!token) return false;
            
            await deleteImageAPI(key, url, token);

            // Refresh from server to ensure consistency
            await refreshImages();

            return true;
        } catch (err) {
            console.error('Failed to delete image:', err);
            setError('Failed to delete image');
            return false;
        }
    }, [getToken]);

    const deleteBulkBackendImages = useCallback(async (images: LabImage[]) => {
        if (!getToken || images.length === 0) return false;
        
        try {
            const token = await getToken();
            if (!token) return false;
            
            const keys = images.map(img => img.key).filter(Boolean) as string[];
            const urls = images.map(img => img.url || img.src).filter(Boolean) as string[];
            
            if (keys.length === 0) return false;
            
            const result = await deleteBulkImagesAPI(keys, urls, token);

            // Refresh from server to ensure consistency
            await refreshImages();

            return true;
        } catch (err) {
            console.error('Failed to delete images:', err);
            setError('Failed to delete images');
            return false;
        }
    }, [getToken]);

    const saveBackendImage = useCallback(async (key: string, url: string) => {
        if (!getToken) return false;
        
        try {
            const token = await getToken();
            if (!token) return false;
            
            await saveImageAPI(key, url, token);
            
            // Refresh images to get the updated list after save
            await refreshImages();
            
            return true;
        } catch (err) {
            console.error('Failed to save image:', err);
            setError('Failed to save image');
            return false;
        }
    }, [getToken, refreshImages]);

    useEffect(() => {
        if (shouldFetch) {
            refreshImages();
        }
    }, [refreshImages, shouldFetch]);

    return {
        backendImages,
        isLoading,
        error,
        refreshImages,
        deleteBackendImage,
        deleteBulkBackendImages,
        saveBackendImage
    };
};

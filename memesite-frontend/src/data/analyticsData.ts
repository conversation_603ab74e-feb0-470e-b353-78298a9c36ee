export const generateChartData = () => {
    const days = 90;
    const today = new Date();
    const data = [];
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const visitors = Math.floor(500 + Math.sin(i * 0.2) * 300 + Math.random() * 200);
        const pageViews = Math.floor(visitors * (3.2 + Math.sin(i * 0.5) * 0.8));
        const bounceRate = Math.max(10, Math.min(50, Math.floor(45 - (visitors / 30) + Math.cos(i * 0.3) * 8)));
        
        data.push({ date: dateStr, visitors, pageViews, bounceRate });
    }
    
    return data;
};

export const chartData = generateChartData();
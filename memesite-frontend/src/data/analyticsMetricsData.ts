export const generateMetricsData = (timeRange: string) => {
    const baseVisitors = timeRange === "90d" ? 12400 : timeRange === "30d" ? 3850 : 678;
    const randomVariation = Math.floor(Math.random() * 100) - 50;
    const visitors = baseVisitors + randomVariation;

    const pageViewMultiplier = 2.8 + Math.random() * 1.2;
    const pageViews = Math.floor(visitors * pageViewMultiplier);

    const bounceRate = timeRange === "90d" ?
        Math.floor(28 + Math.random() * 8) :
        timeRange === "30d" ?
            Math.floor(32 + Math.random() * 10) :
            Math.floor(38 + Math.random() * 12);

    return {
        visitors: visitors >= 10000 ? `${(visitors / 1000).toFixed(1)}K` :
            visitors >= 1000 ? `${(visitors / 1000).toFixed(1)}K` :
                visitors.toString(),
        pageViews: pageViews >= 10000 ? `${(pageViews / 1000).toFixed(1)}K` :
            pageViews >= 1000 ? `${(pageViews / 1000).toFixed(1)}K` :
                pageViews.toString(),
        bounceRate: `${bounceRate}%`
    };
};
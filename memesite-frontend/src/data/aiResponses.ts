import { AIResponse } from "@/types/aiResponse";

export const aiResponses: AIResponse[] = [
  {
    type: "text",
    content: "I'll help you create a modern and engaging website for your business! Let me outline the plan below:\n\nHigh-Level Plan\nKey Features:\n• Professional landing page with compelling hero section and clear value proposition\n• Interactive product/service showcase with filtering and search capabilities\n• Contact form with email integration and location map\n• Blog section for content marketing and SEO optimization\n• Responsive navigation with smooth scrolling between sections\n• Social media integration and sharing capabilities\n• Performance optimization for fast loading times\n• Accessibility features ensuring inclusive user experience\n\nDesign Language:\n• Clean and modern visual design with strategic use of whitespace\n• Professional color palette aligned with your brand identity\n• Typography hierarchy using modern, readable font combinations\n• Subtle animations and micro-interactions for engagement\n• High-quality imagery and icons to enhance visual appeal\n• Consistent spacing and grid system for visual harmony"
  },
  {
    type: "tasks",
    content: {
      title: "Defining Website Studio Website:",
      tasks: [
        { text: "Identifying your website's type" },
        { text: "Extracting brand or product focus" },
        { text: "Determining must-have sections" },
        { text: "Creating initial page structure layout" }
      ]
    },
    taskCompletionTimes: [3000, 6000, 9000, 12000],
    showPreview: false
  },
  {
    type: "text",
    content: "I'll help you create a modern and engaging website for your business! Let me outline the plan below:\n\nHigh-Level Plan\nKey Features:\n• Professional landing page with compelling hero section and clear value proposition\n• Interactive product/service showcase with filtering and search capabilities\n• Contact form with email integration and location map\n• Blog section for content marketing and SEO optimization\n• Responsive navigation with smooth scrolling between sections\n• Social media integration and sharing capabilities\n• Performance optimization for fast loading times\n• Accessibility features ensuring inclusive user experience\n\nDesign Language:\n• Clean and modern visual design with strategic use of whitespace\n• Professional color palette aligned with your brand identity\n• Typography hierarchy using modern, readable font combinations\n• Subtle animations and micro-interactions for engagement\n• High-quality imagery and icons to enhance visual appeal\n• Consistent spacing and grid system for visual harmony"
  },
  {
    type: "tasks",
    content: {
      title: "Defining Website Studio Website:",
      tasks: [
        { text: "Identifying your website's type" },
        { text: "Extracting brand or product focus" },
        { text: "Determining must-have sections" },
        { text: "Creating initial page structure layout" }
      ]
    },
    taskCompletionTimes: [2000, 4000, 6000, 8000],
    showPreview: false
  },
  {
    type: "text",
    content: "I'll help you create a modern and engaging website for your business! Let me outline the plan below:\n\nHigh-Level Plan\nKey Features:\n• Professional landing page with compelling hero section and clear value proposition\n• Interactive product/service showcase with filtering and search capabilities\n• Contact form with email integration and location map\n• Blog section for content marketing and SEO optimization\n• Responsive navigation with smooth scrolling between sections\n• Social media integration and sharing capabilities\n• Performance optimization for fast loading times\n• Accessibility features ensuring inclusive user experience\n\nDesign Language:\n• Clean and modern visual design with strategic use of whitespace\n• Professional color palette aligned with your brand identity\n• Typography hierarchy using modern, readable font combinations\n• Subtle animations and micro-interactions for engagement\n• High-quality imagery and icons to enhance visual appeal\n• Consistent spacing and grid system for visual harmony"
  },
  {
    type: "tasks",
    content: {
      title: "Defining Website Studio Website:",
      tasks: [
        { text: "Identifying your website's type" },
        { text: "Extracting brand or product focus" },
        { text: "Determining must-have sections" },
        { text: "Creating initial page structure layout" }
      ]
    },
    taskCompletionTimes: [1500, 3000, 4500, 6000],
    showPreview: true
  }
];
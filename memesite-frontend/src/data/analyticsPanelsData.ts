export const generatePanelsData = (timeRange: string, metricType: string) => {
    const baseMult = timeRange === "90d" ? 3 : timeRange === "30d" ? 1.5 : 1;
    
    const metricMult = metricType === 'pageViews' ? 3.5 : metricType === 'bounceRate' ? 0.15 : 1;
    
    const getVariation = (index: number, range: string, metric: string) => {
        const baseVariation = range === "7d" ? 0.8 + (index * 0.3) : 
                              range === "30d" ? 1.5 - (index * 0.2) : 
                              0.5 + (index * 0.25);
        
        if (metric === 'bounceRate') {
            return baseVariation * (1.2 - index * 0.1);
        } else if (metric === 'pageViews') {
            return baseVariation * (1 + index * 0.1);
        }
        return baseVariation;
    };
    
    return {
        pages: [
            { name: "/", value: Math.floor(342 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "/projects", value: Math.floor(215 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "/about", value: Math.floor(98 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() },
            { name: "/contact", value: Math.floor(45 * baseMult * metricMult * getVariation(3, timeRange, metricType)).toString() },
            { name: "/blog", value: Math.floor(35 * baseMult * metricMult * getVariation(4, timeRange, metricType)).toString() },
            { name: "/careers", value: Math.floor(25 * baseMult * metricMult * getVariation(5, timeRange, metricType)).toString() }
        ],
        devices: [
            { name: "Desktop", value: Math.floor(420 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "Mobile", value: Math.floor(210 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "Tablet", value: Math.floor(70 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() }
        ],
        referrers: [
            { name: "Google", value: Math.floor(315 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "Direct", value: Math.floor(210 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "Twitter", value: Math.floor(105 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() },
            { name: "GitHub", value: Math.floor(70 * baseMult * metricMult * getVariation(3, timeRange, metricType)).toString() }
        ],
        browsers: [
            { name: "Chrome", value: Math.floor(385 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "Safari", value: Math.floor(175 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "Firefox", value: Math.floor(105 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() },
            { name: "Edge", value: Math.floor(35 * baseMult * metricMult * getVariation(3, timeRange, metricType)).toString() }
        ],
        countries: [
            { name: "United States", value: Math.floor(280 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "United Kingdom", value: Math.floor(140 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "Germany", value: Math.floor(105 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() },
            { name: "Canada", value: Math.floor(70 * baseMult * metricMult * getVariation(3, timeRange, metricType)).toString() },
            { name: "France", value: Math.floor(63 * baseMult * metricMult * getVariation(4, timeRange, metricType)).toString() },
            { name: "Japan", value: Math.floor(42 * baseMult * metricMult * getVariation(5, timeRange, metricType)).toString() }
        ],
        os: [
            { name: "Windows", value: Math.floor(350 * baseMult * metricMult * getVariation(0, timeRange, metricType)).toString() },
            { name: "macOS", value: Math.floor(210 * baseMult * metricMult * getVariation(1, timeRange, metricType)).toString() },
            { name: "Linux", value: Math.floor(70 * baseMult * metricMult * getVariation(2, timeRange, metricType)).toString() },
            { name: "Android", value: Math.floor(70 * baseMult * metricMult * getVariation(3, timeRange, metricType)).toString() }
        ]
    };
};
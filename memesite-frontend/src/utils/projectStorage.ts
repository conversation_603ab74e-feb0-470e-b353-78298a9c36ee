import { ProjectData } from '@/types/project';

export const saveProject = (project: ProjectData): void => {
    sessionStorage.setItem(`project-${project.id}`, JSON.stringify(project));
};

export const getProject = (projectId: string): ProjectData | null => {
    try {
        const data = sessionStorage.getItem(`project-${projectId}`);
        return data ? JSON.parse(data) : null;
    } catch {
        return null;
    }
};

export const deleteProject = (projectId: string): void => {
    sessionStorage.removeItem(`project-${projectId}`);
};
import {
  BackendProject,
  CreateProjectRequest,
  CreateProjectResponse,
  GetProjectIDResponse,
  CreateMessageResponse,
  GetMessagesResponse,
  DeployProjectResponse,
  GenerateImageResponse,
  DeleteImageResponse,
  BulkDeleteImageResponse,
  ImageItem,
  SaveImageResponse,
} from "@/types/project";

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: unknown
  ) {
    super(message);
    this.name = "ApiError";
  }
}

async function makeAuthenticatedRequest<T>(
  endpoint: string,
  options: RequestInit,
  token: string
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const response = await fetch(url, {
    ...options,
    headers: {
      mode: 'cors',
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      `API error! Status: ${response.status}`,
      response.status,
      errorData
    );
  }

  return response.json();
}

export async function createProjectAPI(
  data: CreateProjectRequest,
  token: string
): Promise<CreateProjectResponse> {
  return makeAuthenticatedRequest<CreateProjectResponse>(
    "/projects",
    {
      method: "POST",
      body: JSON.stringify(data),
    },
    token
  );
}

export async function getMessagesAPI(
  projectId: string,
  token: string,
  limit: number = 10,
  cursor?: string
): Promise<GetMessagesResponse> {
  let endpoint = `/projects/${projectId}/messages?limit=${limit}`;
  if (cursor) {
    endpoint += `&cursor=${cursor}`;
  }

  return makeAuthenticatedRequest<GetMessagesResponse>(
    endpoint,
    {
      method: "GET",
    },
    token
  );
}

export async function deployProjectAPI(
  projectId: string,
  token: string
): Promise<DeployProjectResponse> {
  return makeAuthenticatedRequest<DeployProjectResponse>(
    `/projects/${projectId}/deploy`,
    {
      method: "POST",
    },
    token
  );
}

export async function getProjectIDAPI(
  projectId: string,
  token: string
): Promise<GetProjectIDResponse> {
  return makeAuthenticatedRequest<GetProjectIDResponse>(
    `/projects/${projectId}`,
    {
      method: "GET",
    },
    token
  );
}

export async function getProjectsAPI(
  limit: number = 10,
  token: string,
  cursor?: string
): Promise<{ code: number; data: BackendProject[]; meta: unknown }> {
  return makeAuthenticatedRequest<{
    code: number;
    data: BackendProject[];
    meta: unknown;
  }>(
    `/projects?limit=${limit}&${cursor && `&cursor=${cursor}`}`,
    {
      method: "GET",
    },
    token
  );
}

export async function createMessageAPI(
  projectId: string,
  versionId: string,
  message: string,
  token: string
): Promise<CreateMessageResponse> {
  return makeAuthenticatedRequest<CreateMessageResponse>(
    `/projects/${projectId}/versions/${versionId}/messages`,
    {
      method: "POST",
      body: JSON.stringify({ type: "user", message, is_system: false }),
    },
    token
  );
}

export async function createStripeCheckoutSessionAPI(
  plan: 'month' | 'year',
  token: string,
  projectId?: string
): Promise<{ code: number; data: { url: string } }> {
  const body: { plan: string; projectId?: string } = { plan };
  if (projectId) {
    body.projectId = projectId;
  }

  return makeAuthenticatedRequest<{ code: number; data: { url: string } }>(
    "/stripe/checkout-session",
    {
      method: "POST",
      body: JSON.stringify(body),
    },
    token
  );
}

// Image Generation APIs
export async function generateImageAPI(
  prompt: string,
  token: string
): Promise<GenerateImageResponse> {
  return makeAuthenticatedRequest<GenerateImageResponse>(
    "/images/generate",
    {
      method: "POST",
      body: JSON.stringify({ prompt }),
    },
    token
  );
}

export async function getImagesAPI(token: string): Promise<ImageItem[]> {
  return makeAuthenticatedRequest<ImageItem[]>(
    "/images",
    {
      method: "GET",
    },
    token
  );
}

export async function saveImageAPI(
  key: string,
  url: string,
  token: string
): Promise<SaveImageResponse> {
  return makeAuthenticatedRequest<SaveImageResponse>(
    "/images/save",
    {
      method: "POST",
      body: JSON.stringify({ key, url }),
    },
    token
  );
}

export async function deleteImageAPI(
  key: string,
  url: string,
  token: string
): Promise<DeleteImageResponse> {
  return makeAuthenticatedRequest<DeleteImageResponse>(
    "/images",
    {
      method: "DELETE",
      body: JSON.stringify({ key, url }),
    },
    token
  );
}

export async function deleteBulkImagesAPI(
  keys: string[],
  urls: string[],
  token: string
): Promise<BulkDeleteImageResponse> {
  return makeAuthenticatedRequest<BulkDeleteImageResponse>(
    "/images",
    {
      method: "DELETE",
      body: JSON.stringify({ keys, urls }),
    },
    token
  );
}

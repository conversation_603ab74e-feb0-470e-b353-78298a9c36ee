const colors = {
  hover: 'rgba(109, 51, 252, 1)',
  hoverBg: 'rgba(109, 51, 252, 0.25)',
  selected: '#93C5FD',
  editable: '#3B82F6',
  editableBg: 'rgba(59, 130, 246, 0.05)'
};

export const classNames = {
  hover: 'webild-hover',
  selected: 'webild-selected'
};

export const textElements = [
  'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'span', 'a', 'button', 'li', 'td', 'th',
  'label', 'div'
];

export const invalidElements = [
  'html', 'body', 'script', 'style'
];

export const createStylesheet = (): string => {
  return `
    .${classNames.hover} {
      outline: 1px dashed ${colors.hover} !important;
      outline-offset: 2px !important;
      border-radius: 3px !important;
      cursor: pointer !important;
    }
    .${classNames.selected} {
      outline: 2px solid ${colors.editable} !important;
      outline-offset: 2px !important;
      border-radius: 3px !important;
    }
    [contenteditable="true"] {
      outline: 2px solid ${colors.editable} !important;
      outline-offset: 2px !important;
      border-radius: 3px !important;
    }
  `;
};
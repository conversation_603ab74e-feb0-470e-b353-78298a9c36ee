import { createStylesheet, classNames, textElements, invalidElements } from './styles';
import { inspectorCore } from './core';

export const createInspectorScript = (): string => {
  const stylesheet = createStylesheet();

  return `
    (function() {
      const existingStyle = document.getElementById('webild-inspector-styles');
      if (existingStyle) existingStyle.remove();
      
      const style = document.createElement('style');
      style.id = 'webild-inspector-styles';
      style.textContent = \`${stylesheet}\`;
      document.head.appendChild(style);
      
      const textElements = ${JSON.stringify(textElements)};
      const invalidElements = ${JSON.stringify(invalidElements)};
      const hoverClass = '${classNames.hover}';
      const selectedClass = '${classNames.selected}';
      
      ${inspectorCore}
    })();
  `;
};

export const cleanupInspectorScript = (): string => {
  return `
    if (window.webildCleanup) {
      window.webildCleanup();
      delete window.webildCleanup;
    }
  `;
};
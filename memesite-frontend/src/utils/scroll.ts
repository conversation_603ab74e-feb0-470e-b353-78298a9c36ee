import type { RefObject } from "react";
import type { ChatContainerRef } from "@/components/widgets/projects/ChatContainer";

export const scrollToBottom = (
    ref: RefObject<HTMLDivElement | ChatContainerRef | null>,
    isHtmlDiv: boolean = false
) => {
    if (!ref.current) return;
    
    if (isHtmlDiv || !('scrollToBottom' in ref.current)) {
        const element = ref.current as HTMLDivElement;
        element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
        });
    } else {
        const chatContainer = ref.current as ChatContainerRef;
        chatContainer.scrollToBottom();
    }
};
export const getDropdownStyle = (
    isExpanded: boolean,
    mounted: boolean,
    scrollHeight: number | undefined
) => {
    const maxScrollHeight = 150;
    const actualHeight = scrollHeight || 0;
    
    return {
        maxHeight: isExpanded && mounted 
            ? actualHeight > maxScrollHeight 
                ? `${maxScrollHeight}px` 
                : `${actualHeight}px`
            : '0px',
        transition: 'max-height 600ms cubic-bezier(0.4, 0, 0.2, 1), margin-bottom 600ms cubic-bezier(0.4, 0, 0.2, 1)',
        overflow: isExpanded ? 'auto' : 'hidden',
        position: 'relative' as const,
        marginBottom: isExpanded ? '0.5rem' : '0'
    };
};

export const getCategoryButtonClass = (isActive: boolean) => {
    return `
        cursor-pointer w-full h-8 block relative overflow-hidden rounded-full
        transition-all duration-[400ms] ease-[cubic-bezier(0.65,0.05,0,1)]
        ${isActive ? '' : 'bg-transparent'}
    `.trim();
};

export const getChevronClass = (isExpanded: boolean) => {
    return `
        h-[var(--text-sm)] w-auto text-white
        transition-transform duration-[800ms] ease-[cubic-bezier(0.76,0,0.24,1)]
        ${isExpanded ? "rotate-180" : ""}
    `.trim();
};

export const getSubItemButtonClass = (isSubActive: boolean) => {
    return `cursor-pointer flex items-center justify-between h-5 rounded relative transition-opacity duration-200 ${isSubActive ? 'opacity-100' : 'opacity-50 hover:opacity-100'}`;
};

export const getDropdownBorderClass = (isExpanded: boolean) => {
    return `mask-fade-bottom-border border-0 border-b transition-colors duration-600 ${isExpanded ? 'border-white/20' : 'border-transparent'}`;
};
const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

export interface SSEEvent {
  type: string;
  data: unknown;
}

export class SSEService {
  private eventSource: EventSource | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(token: string): void {
    if (this.eventSource) {
      this.disconnect();
    }

    try {
      const url = `${API_BASE_URL}/sse/messages?token=${encodeURIComponent(token)}`;
      this.eventSource = new EventSource(url);

      this.eventSource.onopen = () => {
        console.log('SSE connection established');
        this.reconnectAttempts = 0;
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data: SSEEvent = JSON.parse(event.data);
          console.log('SSE message received:', data);
          window.dispatchEvent(new CustomEvent('sse-message', { detail: data }));

          this.reconnectAttempts = 0;
        } catch (error) {
          console.error('Failed to parse SSE message:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        console.log('EventSource readyState:', this.eventSource?.readyState);
        console.log('Reconnect attempts:', this.reconnectAttempts);

        if (this.reconnectAttempts === 0 || this.eventSource?.readyState === EventSource.CLOSED) {
          this.handleReconnect(token);
        } else {
          console.log('Skipping reconnect - connection seems stable');
        }
      };

      this.eventSource.addEventListener('close', () => {
        console.log('SSE connection closed by server');
        this.handleReconnect(token);
      });

    } catch (error) {
      console.error('Failed to create SSE connection:', error);
    }
  }

  private handleReconnect(token: string): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max SSE reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`Attempting SSE reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect(token);
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      console.log('SSE connection closed');
    }
  }

  isConnected(): boolean {
    return this.eventSource !== null && this.eventSource.readyState === EventSource.OPEN;
  }
}

export const sseService = new SSEService();

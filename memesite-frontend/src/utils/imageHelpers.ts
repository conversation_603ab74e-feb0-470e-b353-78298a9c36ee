/**
 * Extract a clean, readable name from an image URL
 * Example: "https://webuild-dev.s3.eu-north-1.amazonaws.com/users/user_310tiVdOrEXM34e78IftJEinzYH/tmp/make-a-scary-memecoin-dark-mascot-1757364005211-2fcd6d8e.jpg"
 * Returns: "Make a Scary memecoin dark"
 */
export function extractImageNameFromUrl(url: string): string {
  try {
    // Extract filename from URL
    const urlParts = url.split('/');
    const filename = urlParts[urlParts.length - 1];
    
    if (!filename) return 'Untitled';
    
    // Remove file extension
    const nameWithoutExtension = filename.replace(/\.[^/.]+$/, '');
    
    // Extract the descriptive part (before timestamp and hash)
    // Pattern: descriptive-name-timestamp-hash
    const parts = nameWithoutExtension.split('-');
    
    // Find where the timestamp starts (usually a long number)
    let descriptiveParts: string[] = [];
    for (const part of parts) {
      // If part is a long number (timestamp) or looks like a hash, stop
      if (/^\d{10,}$/.test(part) || /^[a-f0-9]{8,}$/i.test(part)) {
        break;
      }
      descriptiveParts.push(part);
    }
    
    // If no descriptive parts found, take first 3-4 parts
    if (descriptiveParts.length === 0) {
      descriptiveParts = parts.slice(0, 4);
    }
    
    // Convert to proper case and limit to 3-4 words
    const words = descriptiveParts
      .join(' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .slice(0, 4); // Limit to 4 words maximum
    
    return words.join(' ') || 'Untitled';
  } catch (error) {
    console.error('Error extracting image name from URL:', error);
    return 'Untitled';
  }
}

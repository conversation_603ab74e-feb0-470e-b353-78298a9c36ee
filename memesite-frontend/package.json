{"name": "webild-front-inside", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.31.10", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.2.0", "swr": "^2.3.6", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.13", "@types/jszip": "^3.4.1", "@types/node": "^20.19.13", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "eslint-config-next": "15.4.3", "tailwindcss": "^4.1.13", "typescript": "^5.9.2"}}
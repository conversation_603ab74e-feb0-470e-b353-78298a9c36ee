Based on the backend code, here's the detailed workflow for code generation:

## 1. Project Version Creation & Queue Initialization

````typescript path=webild-backend/src/modules/projects/projects.service.ts mode=EXCERPT
async createProjectVersion(dto: CreateProjectVersionDto & { project_id: string }) {
  // Creates new version branch in GitHub
  const versionBranch = `version_${versionNumber}`;
  await this.gitHubService.createBranch(project.repositoryOwner, project.repositoryName, versionBranch);
  
  // Generates combined prompt and enqueues generation job
  const combinedPrompt = await this.messagesService.generateVersionPrompt(projectId, versionId);
  await this.queueService.enqueueProjectGenerationJob(combinedPrompt, userId, projectId, versionId);
}
````

## 2. Queue Processing Pipeline

````typescript path=webild-backend/src/modules/queue/processors/generate-project.processor.ts mode=EXCERPT
@Processor(PROJECT_FILES_QUEUE)
export class GenerateProjectProcessor extends WorkerHost {
  override async process(job: Job<GenerateProjectFilesPayload>): Promise<void> {
    const { prompt, userId, projectId, versionId } = job.data;
    
    // Main generation workflow with auto-fix retry logic
  }
}
````

## 3. Component Selection & Locking

The system uses a deterministic component selection process:

````typescript path=webild-backend/src/common/utils/executor-gasket.ts mode=EXCERPT
export function buildLockedSelection(plan: Plan, registry: Registry) {
  // Uses randomSalt + projectName + section to deterministically pick components
  const seed = `${plan.randomSalt}:${plan.projectName}:${sec.id}:${cat}:${plan.promptVersion || 'v1'}`;
  const chosen = pickDeterministic(seed, pool);
  
  return picks; // Locked component selections
}
````

## 4. AI Code Generation

````typescript path=webild-backend/src/modules/projects/projects.service.ts mode=EXCERPT
async getProjectStructure(userId, projectId, versionId, plainPrompt, systemPrompts, userPrompt) {
  // Sends structured prompt to OpenAI with:
  // - LOCKED_SELECTION JSON (deterministic component picks)
  // - COMPONENT REGISTRY JSON 
  // - PLANNER PLAN JSON
  // - Current file contents
  // - IMAGE MANIFEST JSON
  
  const generatedMessage = await this.messagesService.askNStoreAI(
    userId, projectId, versionId,
    { message: plainPrompt, type: MessageTypes.User },
    systemPrompts, false, GenerateCodeType.CODE_GENERATE, userPrompt, true
  );
}
````

## 5. File System Operations

````typescript path=webild-backend/src/modules/projects/projects.service.ts mode=EXCERPT
async createProjectFromFiles(filesPaths: ManifestFile[], projectId: string, initialRun = true) {
  const projectPath = `${projectFolder}/main`;
  
  if (initialRun) {
    await unzipProject('example.zip', projectFolder); // Base template
  }
  
  // Write generated files
  for (const { path, content } of filesPaths) {
    this.writeParsedFile(path, content, projectPath);
  }
  
  if (initialRun) {
    // Add CI/CD workflow and Next.js config
    this.writeParsedFile('.github/workflows/build.yml', BuildFileContent, projectPath);
    this.writeParsedFile('next.config.ts', NextConfigContent, projectPath);
  }
}
````

## 6. GitHub Integration & CI Pipeline

````typescript path=webild-backend/src/modules/queue/processors/generate-project.processor.ts mode=EXCERPT
const runCiPipeline = async (owner: string, repo: string, branch: string) => {
  await this.gitHubService.ensureWorkflowIsIndexed(owner, repo);
  await this.gitHubService.triggerWorkflowDispatch(owner, repo, branch);
  return this.gitHubService.waitForWorkflowCompletion(owner, repo, branch);
};

// Push to GitHub and run build
await this.projectsService.pushProjectToGitHub(projectId, versionId);
await this.gitHubService.forcePushBranchToMain(repositoryOwner, repositoryName, branch);
const result = await runCiPipeline(repositoryOwner, repositoryName, branch);
````

## 7. Auto-Fix Retry Loop

````typescript path=webild-backend/src/modules/queue/processors/generate-project.processor.ts mode=EXCERPT
const MAX_FIX_ATTEMPTS = 5;
let attempt = 1;
let lastError = result.error ?? 'Unknown build error';

while (attempt <= MAX_FIX_ATTEMPTS) {
  const fixUserContent = makeFixUserContent({
    errorLogs: lastError,
    previousPatchJson: previousPatch,
    registryJson, lockedJson: JSON.stringify(locked)
  });
  
  const filesToChange = await this.projectsService.getProjectStructure(
    userId, projectId, versionId, fixPromptForDB,
    [SystemPrompts.FIX_CODE_ERRORS], fixUserContent
  );
  
  // Apply fixes and retry CI
  await this.projectsService.createProjectFromFiles(filesToChange.files, projectId, false);
  await pushAll(repositoryOwner, repositoryName, branch);
  const next = await runCiPipeline(repositoryOwner, repositoryName, branch);
}
````

## 8. Sandbox Deployment

````typescript path=webild-backend/src/common/services/sandbox/sandbox.service.ts mode=EXCERPT
async createSandboxWithPreview(repoUrl: string, name: string, branch: string = 'main') {
  const sandbox = await this.daytona.create({
    image: 'node:24', language: 'typescript', public: true,
    resources: { memory: 4, cpu: 2 }
  });
  
  await sandbox.git.clone(repoUrl, WORKSPACE, branch);
  await sandbox.process.executeCommand(`npm ci || npm install`, WORKSPACE);
  sandbox.process.executeCommand(`npm run build && npm run start -- -H 0.0.0.0 -p ${port}`, WORKSPACE);
  
  const preview = await sandbox.getPreviewLink(port);
  return { sandboxId: sandbox.id, preview: preview.legacyProxyUrl };
}
````

## Key Features:

1. **Deterministic Component Selection**: Uses `randomSalt` to ensure consistent component picks across regenerations
2. **Structured AI Prompts**: Combines locked selections, registry, and current state for precise generation
3. **Auto-Fix Loop**: Automatically attempts to fix build errors up to 5 times using error logs
4. **Live Preview**: Creates Daytona sandbox with real-time preview URL
5. **Version Control**: Each generation creates a new Git branch with full history
6. **Queue-Based**: Asynchronous processing using Bull queues for scalability

The workflow ensures reliable, deterministic code generation with automatic error recovery and live deployment.
